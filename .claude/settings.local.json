{"permissions": {"allow": ["<PERSON><PERSON>(curl:*)", "mcp__spec-workflow-mcp__specs-workflow", "mcp__chrome-mcp__chrome_navigate", "mcp__chrome-mcp__chrome_get_web_content", "mcp__chrome-mcp__chrome_screenshot", "WebFetch(domain:jsondiffpatch.com)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__chrome-mcp__get_windows_and_tabs", "mcp__chrome-mcp__chrome_console", "mcp__chrome-mcp__chrome_get_interactive_elements"], "deny": []}}