package consts

import "fwyytool/stru"

var ClusterList = []stru.Cluster{
	{
		Name: "服务运营-汇总",
		List: []stru.ClusterDetail{
			{Name: "服务运营-yk-fwyy_bzr_online_mysql", Port: "3550"},
			{Name: "服务运营-yk-fwyy_lpc_duxuesc_online_mysql", Port: "3782"},
			{Name: "服务运营-yk-fwyy_data_online_mysql", Port: "3552"},
			{Name: "服务运营-yk-fwyy_lpc_reach_online_mysql", Port: "3646"},
			{Name: "服务运营-yk-fwyy_subsystem_call_online_mysql", Port: "3818"},
			{Name: "服务运营-yk-fwyy_subsystem_wechat_online_mysql", Port: "3816"},
			{Name: "服务运营-yk-fwyy_subsystem_remind_online_mysql", Port: "3820"},
			{Name: "服务运营-yk-fwyy_lpcactive_online_mysql", Port: "3644"},
			{Name: "服务运营-yk-fwyy_pigai_online_mysql", Port: "3752"},
			{Name: "服务运营-yk-fwyy_lpc_insight_online_mysql", Port: "3688"},
			{Name: "服务运营-yk-fwyy_allocate_online_mysql", Port: "3892"},
			{Name: "服务运营-yk-fwyy_userprofile_online_mysql", Port: "3884"},
			{Name: "服务运营-yk-fwyy_lpc_leads_online_mysql", Port: "3662"},
		},
	},
	{
		Name: "素养-汇总",
		List: []stru.ClusterDetail{
			{Name: "素养-zhibo-jx_deer_workbench_online_mysql", Port: "3900"},
			{Name: "素养-yk-course_deer_bc_online_mysql", Port: "3804"},
			{Name: "素养-suyang-deer-ty", Port: "4437"},
			{Name: "素养-yk-course_jingzhunlian_online_mysql", Port: "4114"},
			{Name: "素养-gx-workbench", Port: "4420"},
			{Name: "素养-gx-motivate", Port: "4413"},
		},
	},
	{
		Name: "教务教研-汇总",
		List: []stru.ClusterDetail{
			{Name: "教务教研-yk-jy_jiaoyan_jy_hudong_online_mysql", Port: "4058"},
			{Name: "教务教研-yk-jy_exam_online_mysql", Port: "3370"},
			{Name: "教务教研-yk-sell_das0-9_online_mysql", Port: "3436,3438,3440,3442,3444,3446,3448,3450,3452,3454"},
			{Name: "教务教研-jy_dcs_online_mysql", Port: "3316"},
			{Name: "教务教研-yk-fwyy_subsystem_tower_online_mysql", Port: "3894"},
			{Name: "教务教研-mysql-jw-produce", Port: "4537"},
			{Name: "教务教研-jx-jiaowu", Port: "4472"},
		},
	}, {
		Name: "线下课-汇总",
		List: []stru.ClusterDetail{
			{Name: "教务教研-mysql-xianxiake", Port: "4511"},
			{Name: "教务教研-mysql-xianxk-stu", Port: "4539"},
		},
	},
	{
		Name: "创新业务-汇总",
		List: []stru.ClusterDetail{
			{Name: "创新业务-jx-zhibo-aiassistant", Port: "4412"},
		},
	},
	{
		Name: "课中-汇总",
		List: []stru.ClusterDetail{
			{Name: "课中-yk-course_inclass_online_mysql", Port: "3364"},
			{Name: "课中-zhibo-jx_interactcenter_online_mysql", Port: "3992"},
			{Name: "课中-yk-jx_jxzt_online_mysql", Port: "3404"},
			{Name: "课中-zhibo-jx_interactstation_online_mysql", Port: "3652"},
			{Name: "课中-zhibo-jx_chat_online_mysql", Port: "3994"},
			{Name: "课中-yk-course_livestation_online_mysql", Port: "3418"},
			{Name: "课中-zhibo-jx_livemarketing_online_mysql", Port: "3726"},
			{Name: "课中-zhibo-jx_jxzt_studyroom_online_mysql", Port: "3722"},
			{Name: "课中-yk-course_jx_attend_online_mysql", Port: "3526"},
			{Name: "课中-yk-course_course_online_mysql", Port: "3362"},
			{Name: "课中-yk-jx_noticelog_online_mysql", Port: "3470"},
			{Name: "课中-zhibo-jx_jx_studentnoticelog_online_mysql", Port: "3650"},
			{Name: "课中-yk-course_fudao_online_mysql", Port: "3368"},
			{Name: "课中-mysql-jx-f-exrcs", Port: "4521"},
			{Name: "课中-yk-jx_front_online_mysql", Port: "3384"},
			{Name: "课中-mysql-jx-f-task", Port: "4520"},
			{Name: "课中-mysql-jx-f-pet", Port: "4519"},
			{Name: "课中-zhibo-jx_flipped_online_mysql", Port: "3578"},
			{Name: "课中-yk-flipped_hdjw_online_mysql", Port: "3472"},
			{Name: "课中-yk-sell_zbui_oper_online_mysql", Port: "3352"},
			{Name: "课中-zhibo-jx_xuexiguanjia_online_mysql", Port: "4072"},
			{Name: "课中-zhibo-jx_jx_report_online_mysql", Port: "3648"},
			{Name: "课中-zhibo-new_y_biz_basic_online_mysql", Port: "3878"},
			{Name: "课中-zhibo-jx_jx_monitor_data_online_mysql", Port: "3882"},
			{Name: "课中-yk-jy_kejian_online_mysql", Port: "3854"},
			{Name: "课中-zhibo-new_aiclass_online_mysql", Port: "3866"},
			{Name: "课中-zhibo-jx_dashboard_online_mysql", Port: "3610"},
			{Name: "课中-mysql-jxduration", Port: "4538"},
			{Name: "课中-mysql-intcusans", Port: "4549"},
			{Name: "课中-mysql-jxf-reward", Port: "4547"},
			{Name: "课中-mysql-jxf-task", Port: "4546"},
		},
	},
}

var DatePosArr = []stru.DatePos{
	{Title: "今天", Val: "0"},
	{Title: "最近3天", Val: "-3"},
	{Title: "最近7天", Val: "-7"},
	{Title: "最近15天", Val: "-15"},
	{Title: "最近30天", Val: "-30"},
}
