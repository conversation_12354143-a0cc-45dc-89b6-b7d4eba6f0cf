package stru

// APIDiffConfig 典型配置
/*
{
    "CourseListCard_0": {
        "disable": true,
        "startTime": 20250422,
        "endTime": 20250701,
        "oneDayStartTime": 10,
        "oneDayEndTime": 21,
        "storeReq": true,
        "oneDayRecordMaxNum": 100,
        "storeResp": false,
        "reExecutionDayAfter": 0,
        "collectFrequency": 100,
        "IgnorePathArrayOrder": {
            "FieldItem":"id"
        },
        "ignorePaths": [
            "/demo/1/b"
        ],
        "diffString": ["sort"],
        "oldPath": "assistantdesk/deskv1/filter/qwe",
        "newPath": "deskcrm/ui/card",
        "ralMethod": "POST",
        "ralConverter": "application/json;",
        "cookie": "ZYBIPSCAS=IPS_2fcbd6a91faca7035058218d68c22cf51754966809"
    }
}
*/

type APIDiffConfig struct {
	Disable              bool              `json:"disable"`                                                  //配置是否启用
	StartTime            int64             `json:"startTime"`                                                //参数收集时间 时间戳
	EndTime              int64             `json:"endTime"`                                                  //参数收集时间 时间戳
	OneDayStartTime      int               `json:"oneDayStartTime"`                                          //参数收集时间 h
	OneDayEndTime        int               `json:"oneDayEndTime"`                                            // 参数收集时间 h
	StoreReq             bool              `json:"storeReq"`                                                 // 保存请求参数
	OneDayRecordMaxNum   int               `json:"oneDayRecordMaxNum"`                                       // 每小时最大记录请求数
	StoreResp            bool              `json:"storeResp"`                                                // 保存返回结果
	ReExecutionDayAfter  int               `json:"reExecutionDayAfter"`                                      // 几天后进行回放
	CollectFrequency     int               `json:"collectFrequency"`                                         // 采集的频率,X%概率收集参数
	IgnorePathArrayOrder map[string]string `json:"ignorePathArrayOrder" mapstructure:"ignorePathArrayOrder"` // diff 指定 path 忽略对象数组顺序，并转为 map 比较
	IgnorePaths          []string          `json:"ignorePaths"`                                              // diff 指定 path 字段不参与 diff 比较
	DiffString           []string          `json:"diffString"`                                               // 指定叶子结点 key cast to string 比较
	OldPath              string            `json:"oldPath"`                                                  // 接口 url
	NewPath              string            `json:"newPath"`                                                  // 接口 url
	RalMethod            string            `json:"ralMethod"`                                                // GET/POST
	RalConverter         string            `json:"ralConverter"`                                             // application/json;/form
	Cookie               string            `json:"cookie"`                                                   // IPS=asd
}

type ArkStudentListDataDiff struct {
	ID          int64  `gorm:"column:id" json:"id"`
	Params      string `gorm:"column:params" json:"params"`
	DiffNum     int    `gorm:"column:diff_num" json:"diff_num"`
	OldData     string `gorm:"column:old_data" json:"oldData"`
	NewData     string `gorm:"column:new_data" json:"newData"`
	DiffResult  string `gorm:"column:diff_result" json:"diffResult"` //diff详情
	Status      int    `gorm:"column:status" json:"status"`
	DiffType    int    `gorm:"column:diff_type" json:"diffType"`
	HandlerName string `gorm:"column:handler_name" json:"handler_name"`
	Fingerprint string `gorm:"column:fingerprint" json:"fingerprint"`
	CreateTime  int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime  int64  `gorm:"column:update_time" json:"updateTime"`
}

type DiffExportItem struct {
	HandlerName string `json:"handlerName"` // 接口名称
	OldData     string `json:"oldData"`     // 旧数据
	NewData     string `json:"newData"`     // 新数据
}
