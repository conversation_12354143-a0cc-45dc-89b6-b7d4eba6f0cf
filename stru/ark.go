package stru

import "encoding/json"

type Rule struct {
	Apps           []int64         `json:"apps" form:"apps"`
	ExportFieldMap json.RawMessage `json:"exportFieldMap" form:"exportFieldMap"`
	DataSourceInfo json.RawMessage `json:"dataSourceInfo" form:"dataSourceInfo"`
	FilterMap      json.RawMessage `json:"filterMap" form:"filterMap"`
	FilterMapMulti json.RawMessage `json:"filterMapMulti" form:"filterMapMulti"`
	ServiceConfig  json.RawMessage `json:"serviceConfig" form:"serviceConfig"`
	FeConfig       json.RawMessage `json:"feConfig" form:"feConfig"`
	Key            string          `json:"key" form:"key"`
	OriName        string          `json:"oriName" form:"oriName"`
	CustomName     string          `json:"customName" form:"customName"`
	ArkType        int64           `json:"type" form:"type"`
	Info           string          `json:"info" form:"info"`
	IsExportExcle  bool            `json:"isExportExcle" form:"isExportExcle"`
	TogetherType   int64           `json:"togetherType" form:"togetherType"`
	Source         string          `json:"source" form:"source"`
	Function       string          `json:"function" form:"function"`
	IsSelect       bool            `json:"isSelect" form:"isSelect"`
	IsDisabled     bool            `json:"isDisabled" form:"isDisabled"`
	FieldType      int64           `json:"fieldType" form:"fieldType"`
	NewFieldType   int64           `json:"newFieldType" form:"newFieldType"`
	SecondGroup    string          `json:"secondGroup" form:"secondGroup"`
	Sort           int64           `json:"sort" form:"sort"`
	Id             int64           `json:"id" form:"id"`
	Name           string          `json:"name" form:"name"`
}
