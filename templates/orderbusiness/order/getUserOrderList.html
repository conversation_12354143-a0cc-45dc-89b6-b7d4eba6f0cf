{{ define "orderbusiness/order/getUserOrderList.html" }}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>用户uid查询全部订单</title>

    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?12223"/>
    <link rel="stylesheet" href="/fwyyyool/assets/css/index.css">

    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/fwyytool/assets/js/bootstrap.min.js"></script>
    <script src="/fwyytool/assets/layer/layer.js"></script>
    <script src="/fwyytool/assets/js/tool.js"></script>
    <style>
        table th {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #dedede;
        }

        table td {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #ffffff;
        }

        .tb-th-order {
            height: 24px;
            font-size: 16px;
            text-align: center;
            background-color: #2e6da4;
            color: white
        }

        div {
            margin-left: 10px;
        }

    </style>
</head>
<body>
<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">商品Id获取课程信息</h3>
    <div class="form-group">
        <label for="idx_p_user_id">用户Id：</label>
        <input type="text" class="form-control" id="idx_p_user_id" placeholder="请输入用户Id" name="userId"
               {{if ne .params.UserId 0}} value="{{.params.UserId}}" {{end}}>
        <label for="idx_p_shop_ids">店铺Id：</label>
        <input type="text" class="form-control" id="idx_p_shop_ids" placeholder="店铺id,多个英文逗号分隔" name="shopIds"
               {{if ne (len .params.ShopIds) 0}} value="{{.params.ShopIds}}" {{end}}>

        <label for="idx_p_pn">页数：</label>
        <input type="text" class="form-control" id="idx_p_pn" placeholder="页数" name="pn"
               {{if ne .params.Pn 0}} value="{{.params.Pn}}" {{else}} value="1" {{end}}>
    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #FD482C">{{.errMsg}}</label>
</form>

{{if ne (len .data) 0}}
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th class="tb-th-order"><span>订单号</span></th>
            <th class="tb-th-order"><span>订单状态</span></th>
            <th class="tb-th-order"><span><a href="https://ued.zuoyebang.cc/documents/docs/dds/orderFlag.html" target="_blank" style="cursor: pointer; text-decoration-line: none; color: orange">订单标识</a></span></th>
            <th class="tb-th-order"><span>下单时间</span></th>
            <th class="tb-th-order"><span>店铺</span></th>
            <th class="tb-th-order"><span>渠道</span></th>
            <th class="tb-th-order"><span>购课来源</span></th>
            <th class="tb-th-order"><span>子订单号</span></th>
            <th class="tb-th-order"><span>商品信息</span></th>
            <th class="tb-th-order"><span>课程信息</span></th>
            <th class="tb-th-order"><span>课程年级</span></th>
            <th class="tb-th-order"><span>课程学科</span></th>
            <th class="tb-th-order"><span>商品金额[分]</span></th>
            <th class="tb-th-order"><span>支付金额[分]</span></th>
            <th class="tb-th-order"><span>操作</span></th>
        </tr>
        {{range $idx, $orderInfo := .data}}
        <tr>
            <td rowspan="{{len $orderInfo.SkuRowList}}">{{$orderInfo.OrderId}}</td>
            <td rowspan="{{len $orderInfo.SkuRowList}}">{{$orderInfo.OrderBusinessStatusName}}<br/>{{$orderInfo.OrderBusinessStatus}}</td>
            <td rowspan="{{len $orderInfo.SkuRowList}}">{{$orderInfo.OrderFlags}}</td>
            <td rowspan="{{len $orderInfo.SkuRowList}}">{{$orderInfo.OrderTimeDesc}}</td>
            <td rowspan="{{len $orderInfo.SkuRowList}}">{{$orderInfo.ShopName}}<br/>{{$orderInfo.ShopId}}</td>
            <td rowspan="{{len $orderInfo.SkuRowList}}">{{$orderInfo.SaleChannel}}</td>
            <td rowspan="{{len $orderInfo.SkuRowList}}">{{$orderInfo.LastFrom}}</td>
            {{range $j, $skuRowInfo := $orderInfo.SkuRowList}}
            {{if eq $j 0}}
            <td>{{$skuRowInfo.SkuRowId}}</td>
            <td>{{$skuRowInfo.SkuName}}<br/>{{$skuRowInfo.SkuId}}</td>
            <td>{{$skuRowInfo.CourseName}}<br/>{{$skuRowInfo.CourseId}}</td>
            <td>{{$skuRowInfo.CourseGradeName}}<br/>{{$skuRowInfo.CourseGradeId}}</td>
            <td>{{$skuRowInfo.CourseSubjectName}}<br/>{{$skuRowInfo.CourseSubjectId}}</td>
            <td>{{$skuRowInfo.GoodsAmount}}</td>
            <td>{{$skuRowInfo.PaidAmount}}</td>
            <td><a href="https://sell-infra-base-cc.suanshubang.cc/afterplat/flow/apply?userId={{$orderInfo.UserId}}&requestNo={{$orderInfo.UserId}}_{{$orderInfo.OrderId}}&orderId={{$orderInfo.OrderId}}&skuRowList=%5B%7B%22skuRowId%22%3A%20{{$skuRowInfo.SkuRowId}}%2C%20%22applyQuantity%22%3A%20{{$skuRowInfo.Quantity}}%7D%5D&reasonId=99&applyAfterType=5&applyFrom=1&applyPlat=fwyy_coursetrans&opUid=2000004961" target="_blank" style="cursor: pointer; text-decoration-line: none; color: orange">发起退款</a></td>
            {{end}}
            {{end}}
        </tr>
            {{range $j, $skuRowInfo := $orderInfo.SkuRowList}}
            {{if ne $j 0}}
            <tr>
                <td>{{$skuRowInfo.SkuRowId}}</td>
                <td>{{$skuRowInfo.SkuName}}<br/>{{$skuRowInfo.SkuId}}</td>
                <td>{{$skuRowInfo.CourseName}}<br/>{{$skuRowInfo.CourseId}}</td>
                <td>{{$skuRowInfo.CourseGradeName}}<br/>{{$skuRowInfo.CourseGradeId}}</td>
                <td>{{$skuRowInfo.CourseSubjectName}}<br/>{{$skuRowInfo.CourseSubjectId}}</td>
                <td>{{$skuRowInfo.GoodsAmount}}</td>
                <td>{{$skuRowInfo.PaidAmount}}</td>
                <td><a href="https://sell-infra-base-cc.suanshubang.cc/afterplat/flow/apply?userId={{$orderInfo.UserId}}&requestNo={{$orderInfo.UserId}}_{{$orderInfo.OrderId}}&orderId={{$orderInfo.OrderId}}&skuRowList=%5B%7B%22skuRowId%22%3A%20{{$skuRowInfo.SkuRowId}}%2C%20%22applyQuantity%22%3A%20{{$skuRowInfo.Quantity}}%7D%5D&reasonId=99&applyAfterType=5&applyFrom=1&applyPlat=fwyy_coursetrans&opUid=2000004961" target="_blank" style="cursor: pointer; text-decoration-line: none; color: orange">发起退款</a></td>
            </tr>
            {{end}}
            {{end}}
        {{end}}
    </table>
    <br />
</div>
{{end}}
<div style="display:none;">
    {{.dataJson}}
</div>
</body>
</html>
{{ end }}