{{define "tools/mysql/slowCollect.html"}}
<!DOCTYPE HTML>
<head>
    <title>慢sql业务线统计</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css"/>

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table.taskList td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }
    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }
</style>

<div style="position: relative;">
    <table class="table table-bordered table-striped table-hover">
        <thead style="position: sticky; top: 0; z-index: 1;">
        <tr>
            <th colspan="13" style="text-align:center;background-color: #2a5839;color:white">慢sql业务线统计</th>
        </tr>
        <tr class="active">
            <th style="text-align:center;">集群</th>
            <th style="text-align:center;width: 200px;">本周</th>
            <th style="text-align:center;width: 200px;">上周</th>
            <th style="text-align:center;width: 200px;">前第二周</th>
            <th style="text-align:center;width: 200px;">前第三周</th>
        </tr>
        </thead>
        <tbody>

    {{range .list}}
    <tr>
        <td style="text-align:left;">{{.Name}}</td>
        {{range $idx, $value := .CntList}}
        <td style="text-align:center;"><a href="/fwyytool/tools/mysql/slowstat?port={{$value.Port}}&beginDate={{$value.BeginDate}}&endDate={{$value.EndDate}}">{{$value.Cnt}}</a></td>
        {{end}}
    </tr>
    {{end}}
        </tbody>
    </table>
</div>


{{end}}