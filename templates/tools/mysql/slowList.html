{{define "tools/mysql/slowList.html"}}
<!DOCTYPE HTML>
<head>
    <title>方舟 - 课程配置详情</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css"/>
<link rel="stylesheet" href="/fwyytool/assets/js/layui/layui.css">

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<script src="/fwyytool/assets/js/layui/layui.js"></script>

<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table.taskList td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }
    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }
</style>


<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">慢sql集群统计</h3>
    <div class="form-group">
        <label>集群：</label>
        <select class="form-control" name="port">
            {{range $key, $cluster := .clusterList}}
            <option value="{{$cluster.Port}}" data-type="0" {{if eq $cluster.Port $.params.Port}} selected="selected" {{end}}>{{$cluster.Name}}</option>
            {{end}}
        </select>

        <div class="form-group" id="selectDate"  style="margin-left:10px;">
            <label>时间</label>
            <input class="form-control" type="text" value='{{$.params.BeginDate}}' name='beginDate' id="beginDate"> 到 <input class="form-control" type="text" id="endDate" value='{{$.params.EndDate}}'  name='endDate'>
        </div>

        <input type="text" class="form-control" id="checkSum" placeholder="checkSum" name="checkSum"
               {{if .params.CheckSum}} value="{{.params.CheckSum}}" {{end}}>

    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #ac2925">{{.error}}</label>
</form>

<div style="position: relative;">
    <table class="table table-bordered table-striped table-hover">
        <thead style="position: sticky; top: 0; z-index: 1;">
        <tr>
            <th colspan="13" style="text-align:center;background-color: #2a5839;color:white">慢SQL列表 CheckSum : {{$.params.CheckSum}}</th>
        </tr>
        <tr class="active">
            <th style="text-align:center;">SQL</th>
            <th style="text-align:center;width: 200px;">service</th>
            <th style="text-align:center;width: 200px;">client</th>
            <th style="text-align:center; width: 120px;">QueryTime</th>
            <th style="text-align:center; width: 180px;">时间</th>
        </tr>
        </thead>
        <tbody>

    {{range .list}}
    <tr>
        <td style="text-align:left;">{{.Sql}}</td>
        <td style="text-align:center;">{{.MysqlIp}}:{{.MysqlPort}}</td>
        <td style="text-align:center;">{{.ClientIp}}:{{.ClientUser}}</td>
        <td style="text-align:center;">{{.QueryTime}}</td>
        <td style="text-align:center;">{{.Ts}}</td>
    </tr>
    {{end}}
        </tbody>
    </table>
</div>


{{if .total}}

<div style="float: right;">
    {{getPageHtml .total .params.PageSize .params.Page .rawParams "page" | htmlUnescaped}}
</div>
{{end}}


<script>
    var laydate = layui.laydate;
    laydate.render({
        elem: '#selectDate',
        range: ['#beginDate', '#endDate'],
        rangeLinked: true, // 开启日期范围选择时的区间联动标注模式 ---  2.8+ 新增
        min: -130,
        max: 1,
        shortcuts: [
            { text: "今天", value: Date.now() },
            {
                text: "最近3天",
                value: function(){
                    var now = new Date();
                    now.setDate(now.getDate() - 3);
                    return now;
                }
            },
            {
                text: "最近7天",
                value: function(){
                    var now = new Date();
                    now.setDate(now.getDate() - 7);
                    return now;
                }
            },
            {
                text: "最近15天",
                value: function(){
                    var now = new Date();
                    now.setDate(now.getDate() - 15);
                    return now;
                }
            },
            {
                text: "最近30天",
                value: function(){
                    var now = new Date();
                    now.setDate(now.getDate() - 30);
                    return now;
                }
            }
        ]
    });
</script>

{{end}}