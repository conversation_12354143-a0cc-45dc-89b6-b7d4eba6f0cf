{{ define "tools/gray/checkclusterinfo.html"}}
<!DOCTYPE HTML>
<head>
    <title>大促检测工具</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?12223"/>

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table.taskList td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }

    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }

    .my-text {
        width: 1300px; /* 设置宽度为 1300 像素 */
        word-wrap: break-word;
    }
</style>


<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">大促检测工具</h3>
    <div class="form-group">
        <label for="personUids">真人ID：</label>
        <input type="text" class="form-control" id="personUids" placeholder="请输入真人ID, 多个用,分隔 "
               name="personUids"
               {{if .params.personUids }} value="{{.params.personUids}}" {{end}}>
        <label for="groupIds">组织ID：</label>
        <input type="text" class="form-control" id="groupIds" placeholder="请输入组织ID, 多个用,分隔" name="groupIds"
               {{if .params.groupIds }} value="{{.params.groupIds}}" {{end}}>
    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #ac2925">{{.error}}</label>
</form>

{{if or (.params.personUids) (.params.groupIds)}} <!-- 如果有值, 渲染结果 -->
{{if eq .queryTag 1}}  <!-- 查询中, 3秒刷新一下页面 -->
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='10' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>
                大促流量信息
            </th>
        </tr>
        <tr>
            <th colspan='10' style='height:24px;font-size:16px;text-align:center'>
                查询中
                <progress id="progressBar" {{if ne .current 0}} value="{{.current}}" {{else}} value="0" {{end}}  {{if ne .total 0}} max="{{.total}}" {{else}} max="100" {{end}}></progress>
            </th>
        </tr>
    </table>
</div>

<script>
    // 定时刷新页面
    setInterval(function () {
        location.reload();
    }, 3000); // 每隔 3 秒刷新页面

</script>
{{else}}
<div style="margin-top: 8px;margin-right: 10px">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='10' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>
                大促流量信息
            </th>
        </tr>
        <tr style="background-color: #d9d9d9">
            <th>环境</th>
            <th>账号</th>
        </tr>
        <tr style="height: 46px;width: 100px">
            <td style="color: tomato"><span class="label label-primary">online</span></td>
            <td>
                <p class="my-text">{{if .data.online}} {{.data.online}} {{end}}</p>
            </td>
        </tr>
        <tr style="height: 46px;width: 100px">
            <td style="color: tomato"><span class="label label-primary">feature</span></td>
            <td>
                <p class="my-text">{{if .data.stable}} {{.data.stable}} {{end}}</p>
            </td>
        </tr>
        <tr style="height: 46px;width: 100px">
            <td style="color: tomato"><span class="label label-primary">small</span></td>
            <td>
                <p class="my-text">{{if .data.gray}} {{.data.gray}} {{end}}</p>
            </td>
        </tr>
    </table>
</div>
{{end}}


{{end}}
{{end}}