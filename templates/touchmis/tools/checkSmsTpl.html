{{ define "touchmis/tools/checkSmsTpl.html"}}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证短信模板</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #1a2a6c, #2c3e50);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        header {
            text-align: center;
            padding: 30px 0;
            color: white;
            position: relative;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            display: inline-block;
        }

        h1:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(to right, #3498db, #2ecc71);
            border-radius: 2px;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
            padding: 25px;
            margin-bottom: 30px;
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-title {
            display: flex;
            align-items: center;
            font-size: 1.4rem;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }

        .card-title i {
            margin-right: 10px;
            color: #3498db;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            margin-bottom: 15px;
            position: relative;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .required label:after {
            content: '*';
            color: #e74c3c;
            margin-left: 4px;
        }

        input, select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s;
        }

        input:focus, select:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 8px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .radio-option input {
            width: auto;
            margin-right: 8px;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 14px 28px;
            font-size: 17px;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            background: #2980b9;
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .btn i {
            margin-right: 8px;
        }

        .btn-container {
            text-align: center;
            margin-top: 20px;
            position: relative;
        }

        .btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .results-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-top: 20px;
        }

        .result-section {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            padding: 15px 20px;
            font-weight: 600;
            font-size: 18px;
            display: flex;
            align-items: center;
        }

        .normal-header {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }

        .abnormal-header {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }

        .section-header i {
            margin-right: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background-color: #f8f9fa;
            text-align: left;
            padding: 16px 20px;
            font-weight: 600;
            color: #2c3e50;
        }

        td {
            padding: 14px 20px;
            border-top: 1px solid #eee;
        }

        tr:hover {
            background-color: #f9f9f9;
        }

        .template-content {
            line-height: 1.6;
            color: #555;
        }

        .error-msg {
            color: #e74c3c;
            font-weight: 500;
        }

        .student-id-section {
            display: block;
            margin-top: 15px;
            animation: fadeIn 0.5s;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        footer {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            margin-top: 30px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            transform: translateX(120%);
            transition: transform 0.4s ease;
            display: flex;
            align-items: center;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background: linear-gradient(to right, #e74c3c, #c0392b);
        }

        .notification.success {
            background: linear-gradient(to right, #2ecc71, #27ae60);
        }

        .notification i {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .results-container {
                grid-template-columns: 1fr;
            }

            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <header>
        <h1>验证短信模板</h1>
    </header>

    <form class="card" method="get" action="#" id="queryForm">
        <h2 class="card-title"><i class="fas fa-cogs"></i> 查询参数设置</h2>

        <div class="form-grid">
            <div class="form-group">
                <label for="assistantId"><i class="fas fa-credit-card"></i> *资产 ID</label>
                <input type="text" id="assistantId" name="assistantId" placeholder="请输入资产 ID">
            </div>

            <div class="form-group">
                <label for="courseId"><i class="fas fa-book"></i> *课程 ID</label>
                <input type="text" id="courseId" name="courseId" placeholder="请输入课程 ID">
            </div>

            <div class="form-group">
                <label for="lessonId"><i class="fas fa-bookmark"></i> 章节 ID(群发短信场景需要)</label>
                <input type="text" id="lessonId" name="lessonId" placeholder="请输入章节 ID">
            </div>
        </div>

        <div class="form-grid">
            <div class="form-group">
                <label for="sendType"><i class="fas fa-bullhorn"></i> 发送场景</label>
                <select id="sendType" name="sendType">
                    <option value="">-- 请选择发送场景(非必填) --</option>
                    <option value="76">LPC AI催到课</option>
                </select>
            </div>

            <div class="form-group">
                <label><i class="fas fa-tasks"></i> *发送类型</label>
                <div class="radio-group" id="sceneId">
                    <label class="radio-option">
                        <input type="radio" name="sceneId" value="1" checked> 催加微
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="sceneId" value="2"> 催到课
                    </label>
                </div>
            </div>

            <div class="form-group">
                <label><i class="fas fa-paper-plane"></i> *发送方式</label>
                <div class="radio-group" id="action">
                    <label class="radio-option">
                        <input type="radio" name="action" value="1" checked> 单发短信
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="action" value="2"> 群发短信
                    </label>
                </div>
            </div>

            <div class="form-group">
                <div class="student-id-section">
                    <label for="studentId"><i class="fas fa-user-graduate"></i> 学生 ID（填一个即可）</label>
                    <input type="text" id="studentId" name="studentId" placeholder="请输入学生 ID">
                </div>
            </div>
        </div>

        <div class="btn-container">
            <button type="submit" class="btn" id="submitBtn">
                <i class="fas fa-search"></i> 查询模板
            </button>
        </div>
    </form>

    <div class="results-container">
        <div class="result-section">
            <div class="section-header normal-header">
                <i class="fas fa-check-circle"></i> 正常模板
            </div>
            <table>
                <thead>
                <tr>
                    <th width="15%">模板 ID</th>
                    <th width="85%">模板内容</th>
                </tr>
                </thead>
                <tbody id="normal-templates">
                {{range .data.tplItems}}
                    <tr>
                        <td>{{.tplId}}</td>
                        <td class="template-content">{{.content}}</td>
                    </tr>
                {{end}}
                </tbody>
            </table>
        </div>
    </div>

    <div class="results-container">
        <div class="result-section">
            <div class="section-header abnormal-header">
                <i class="fas fa-exclamation-triangle"></i> 异常模板
            </div>
            <table>
                <thead>
                <tr>
                    <th width="15%">模板 ID</th>
                    <th width="55%">模板内容</th>
                    <th width="30%">错误信息</th>
                </tr>
                </thead>
                <tbody id="abnormal-templates">
                {{range .data.tplErrItems}}
                <tr>
                    <td>{{.tplId}}</td>
                    <td>{{.tplContent}}</td>
                    <td class="error-msg">{{.errMsg}}</td>
                </tr>
                {{end}}
                </tbody>
            </table>
        </div>
    </div>

    <div class="notification" id="notification">
        <i class="fas fa-check-circle"></i> 操作成功！
    </div>
</div>

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('queryForm');
        const submitBtn = document.getElementById('submitBtn');
        const notification = document.getElementById('notification');

        // 显示通知
        function showNotification(message, type) {
            notification.innerHTML = `<i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i> ${message}`;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 表单提交处理
        form.addEventListener('submit', function(e) {
            // 防止表单默认提交行为（我们使用自定义提交）
            e.preventDefault();

            // 禁用按钮防止多次点击
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 查询中...';

            // 收集表单数据
            const formData = {
                assistantId: document.getElementById('assistantId').value,
                courseId: document.getElementById('courseId').value,
                lessonId: document.getElementById('lessonId').value,
                sendType: document.getElementById('sendType').value,
                sceneId: document.querySelector('input[name="sceneId"]:checked').value,
                action: document.querySelector('input[name="action"]:checked').value,
                studentIds: document.getElementById('studentIds').value
            };
        });

        // 初始化表单值（如果有URL参数）
        const searchParams = new URLSearchParams(window.location.search);
        if (searchParams.get('assistantId')) {
            document.getElementById('assistantId').value = searchParams.get('assistantId');
        }
        if (searchParams.get('courseId')) {
            document.getElementById('courseId').value = searchParams.get('courseId');
        }
        if (searchParams.get('lessonId')) {
            document.getElementById('lessonId').value = searchParams.get('lessonId');
        }
        if (searchParams.get('sendType')) {
            document.getElementById('sendType').value = searchParams.get('sendType');
        }
        if (searchParams.get('sceneId')) {
            const sceneId = searchParams.get('sceneId');
            document.querySelectorAll('input[name="sceneId"]').forEach(radio => {
                if (radio.value === sceneId) radio.checked = true;
            });
        }
        if (searchParams.get('action')) {
            const action = searchParams.get('action');
            document.querySelectorAll('input[name="action"]').forEach(radio => {
                if (radio.value === action) radio.checked = true;
            });
        }
        if (searchParams.get('studentId')) {
            document.getElementById('studentId').value = searchParams.get('studentId');
        }
    });

    // 添加表单提交处理
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('queryForm');
        const submitBtn = form.querySelector('.btn');

        form.addEventListener('submit', function(e) {
            // 阻止默认表单提交行为
            e.preventDefault();

            // 禁用按钮防止重复点击
            submitBtn.disabled = true;
            submitBtn.textContent = '查询中...';

            // 获取表单数据并刷新页面
            const formData = new FormData(form);
            const params = new URLSearchParams(formData).toString();

            // 添加延迟让用户看到按钮状态变化
            setTimeout(() => {
                window.location.href = window.location.pathname + '?' + params;
            });
        });
    });
</script>
</body>
</html>
{{end}}