{{ define "touchmis/tools/checkFriend.html"}}
<!doctype html>
<html lang="zh-CN" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css"/>
    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/fwyytool/assets/js/bootstrap.min.js"></script>
    <script src="/fwyytool/assets/layer/layer.js"></script>
    <title>资产好友关系查询</title>
    <style>
        span.label {
            padding: 5px;
            margin-right: 10px;
            display: inline-block;
            width: 90px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="page-header">
        <h1>资产好友关系查询</h1>
    </div>
    <form style="overflow-wrap: anywhere;">
        <div class="col-lg-6 form-group">
            <div class="form-group">
                <label for="assistantUid">资产ID：</label>
                <input type="text" class="form-control" id="assistantUid" placeholder="请输入资产 ID" name="assistantUid">
                <br/>
                <label for="studentIds">学生ID：</label>
                <input type="text" class="form-control" id="studentIds" name="studentIds" placeholder="请输入学生ID(英文逗号分割)">
                <br/>
                <button type="submit" class="btn btn-success"> 检 查</button>
            </div>
            <br/>
            <ul class="list-group">
                <label for="studentIds">不存在好友关系列表：</label>
                {{range .data.noConfirmStudentIds}}
                    <li class="list-group-item">{{.}}</li>
                {{end}}
            </ul>
        </div>
    </form>
</div>
<script type="text/javascript">
    let searchParams = new URLSearchParams(new URL(window.location.href).search);
    if (searchParams.get('assistantUid')) {
        document.getElementById('assistantUid').value = searchParams.get('assistantUid');
    }
    if (searchParams.get('studentIds')) {
        document.getElementById('studentIds').value = searchParams.get('studentIds');
    }
</script>
</body>
</html>
{{end}}