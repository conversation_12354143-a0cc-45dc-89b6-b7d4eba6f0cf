{{ define "touchmis/tools/getCallRecordInfo.html" }}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>外呼结果查询</title>

    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?12223"/>
    <link rel="stylesheet" href="/fwyyyool/assets/css/index.css">

    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/fwyytool/assets/js/bootstrap.min.js"></script>
    <script src="/fwyytool/assets/layer/layer.js"></script>
    <script src="/fwyytool/assets/js/tool.js"></script>
    <style>
        table th {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #dedede;
        }

        table td {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #ffffff;
        }

        .tb-header {
            background-color: #d9d9d9;
            height: 46px;
            border: solid 1px #00a9ff !important;
        }

        div {
            margin-left: 10px;
        }

        .comment {
            color: gray;
            font-style: italic; /* 可选：使注释文本显示为斜体 */
        }

    </style>
</head>
<body>
<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">外呼结果查询</h3>
    <div class="form-group">
        <label for="callId">callId：</label>
        <input type="text" class="form-control" id="callId" placeholder="请输入callId" name="callId"
              value='{{printf "%.0f" .data.callId}}'>
    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #FD482C">{{.errMsg}}</label>
</form>

<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='2' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>外呼结果</th>
        </tr>
        <tr>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #2e6da4;color:white'><span>外呼属性</span></th>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #2e6da4;color:white'><span>属性值</span></th>
        </tr>
        <tr>
            <td class="tb-header">外呼Id</td>
            <td>{{printf "%.0f" .data.callId}}</td>
        </tr>
        <tr>
            <td class="tb-header">通话来源</td>
            <td>{{.data.sourceType}}</td>
        </tr>
        <tr>
            <td class="tb-header">业务线</td>
            <td>
                {{if eq .data.line 1.0}}
                    LPC
                {{else if eq .data.line 2.0}}
                    辅导
                {{else}}
                    <p>Unknown</p>
                {{end}}
                <p class="comment">【说明】1: LPC, 2: 辅导</p>
            </td>
        </tr>
        <tr>
            <td class="tb-header">通话类型</td>
            <td>
                {{if eq .data.callType 1.0}}
                呼出
                {{else if eq .data.callType 2.0}}
                呼入
                {{else}}
                Unknown
                {{end}}
                <p class="comment">【说明】1: 呼出, 2: 呼入</p>
            </td>
        </tr>
        <tr>
            <td class="tb-header">外呼类型</td>
            <td>
                {{if eq .data.callMode 9.0}}
                帮帮盾
                {{else if eq .data.callMode 10.0}}
                ivr
                {{else if eq .data.callMode 11.0}}
                sip外呼
                {{else}}
                Unknown
                {{end}}
                <p class="comment">【说明】9: 帮帮盾, 10: ivr, 11: sip外呼</p>
            </td>
        </tr>
        <tr>
            <td class="tb-header">外呼结果</td>
            <td>
                {{if eq .data.callResult 1.0}}
                正在呼叫
                {{else if eq .data.callResult 2.0}}
                呼叫正常结束（已接通）
                {{else if eq .data.callResult 3.0}}
                呼叫异常结束（未接通）
                {{else}}
                Unknown
                {{end}}
                <p class="comment">【说明】1: 正在呼叫, 2: 呼叫正常结束（已接通）, 3: 呼叫异常结束（未接通）</p>
            </td>
        </tr>
        <tr>
            <td class="tb-header">触发类型</td>
            <td>
                {{if eq .data.triggerType 1.0}}
                正常触发
                {{else if eq .data.triggerType 2.0}}
                自己触发
                {{else}}
                Unknown
                {{end}}
                <p class="comment">【说明】1: 正常触发, 2: 自己触发</p>
            </td>
        </tr>
        <tr>
            <td class="tb-header">外呼结果json</td>
            <td><pre id="json-container-attr" class="json-container">{{.dataJson}}</pre></td>
        </tr>
    </table>
    <br />
</div>
</body>
<script>
    let jcAttr = "json-container-attr";
    let jcSpec = "json-container-spec"
    let jcLabel = "json-container-label"

    highlightJson(document.getElementById(jcAttr).innerText, jcAttr)
    highlightJson(document.getElementById(jcSpec).innerText, jcSpec)
    highlightJson(document.getElementById(jcLabel).innerText, jcLabel)
</script>
</html>
{{ end }}