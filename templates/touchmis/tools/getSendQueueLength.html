{{ define "touchmis/tools/getSendQueueLength.html"}}
<!doctype html>
<html lang="zh-CN" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css"/>
    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/fwyytool/assets/js/bootstrap.min.js"></script>
    <script src="/fwyytool/assets/layer/layer.js"></script>
    <title>鲲鹏侧积压情况查询</title>
    <style>
        span.label {
            padding: 5px;
            margin-right: 10px;
            display: inline-block;
            width: 90px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="page-header">
        <h1>鲲鹏侧积压情况查询</h1>
    </div>
    <form style="overflow-wrap: anywhere;">
        <div class="col-lg-6 form-group">
            <div class="form-group">
                <label for="assistantUid">资产ID：</label>
                <input type="text" class="form-control" id="assistantUid" placeholder="请输入资产 ID" name="assistantUid">
                <br/>
                <button type="submit" class="btn btn-success">查 询</button>
            </div>
            <br/>
            <ul class="list-group">
                <b>积压长度：</b>
                <li class="list-group-item">
                    队列长：{{.data.QueueLengthInfo.queueLength}}
                </li>
                <li class="list-group-item">
                    紧急队列长：{{.data.QueueLengthInfo.emergencyQueueLength}}
                </li>
                <b>风险间隔：</b>
                {{range .data.RiskConfigInfo}}
                <li class="list-group-item">start: {{.start}}</li>
                <li class="list-group-item">end: {{.end}}</li>
                <li class="list-group-item">最小风险间隔: {{.riskIntervalMin}}</li>
                <li class="list-group-item">最大风险间隔: {{.riskIntervalMax}}</li>
                {{end}}

            </ul>
        </div>
    </form>
</div>
<script type="text/javascript">
    let searchParams = new URLSearchParams(new URL(window.location.href).search);
    if (searchParams.get('assistantUid')) {
        document.getElementById('assistantUid').value = searchParams.get('assistantUid');
    }
</script>
</body>
</html>
{{end}}