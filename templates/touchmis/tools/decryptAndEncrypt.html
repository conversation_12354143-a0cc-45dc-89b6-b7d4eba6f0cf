{{ define "touchmis/tools/decryptAndEncrypt.html"}}
<!doctype html>
<html lang="zh-CN" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css"/>
    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/fwyytool/assets/js/bootstrap.min.js"></script>
    <script src="/fwyytool/assets/layer/layer.js"></script>
    <title>KMS 加解密</title>
    <style>
        span.label {
            padding: 5px;
            margin-right: 10px;
            display: inline-block;
            width: 90px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="page-header">
        <h1>KMS 加解密</h1>
    </div>
    <form style="overflow-wrap: anywhere;">
        <div class="col-lg-6 form-group">
            <div class="input-group">
                <input type="text" class="form-control" id="encodeInput" name="encodeInput" placeholder="请输入原文">
                <span class="input-group-btn">
                    <button class="btn btn-primary" type="submit">加密</button>
                </span>
            </div>
            <ul class="list-group">
                <li class="list-group-item"><span class="label label-default">密文</span>{{.data.encodeValue}}</li>
            </ul>
        </div>
        <div class="col-lg-6 form-group">
            <div class="input-group">
                <input type="text" class="form-control" id="decodeInput" name="decodeInput" placeholder="请输入密文">
                <span class="input-group-btn">
                    <button class="btn btn-primary" type="submit">解密</button>
                </span>
            </div>
            <ul class="list-group">
                <li class="list-group-item"><span class="label label-default">原文</span>{{.data.decodeValue}}</li>
            </ul>
        </div>
    </form>
</div>
<script type="text/javascript">
    let searchParams = new URLSearchParams(new URL(window.location.href).search);
    if (searchParams.get('encodeInput')) {
        document.getElementById('encodeInput').value = searchParams.get('encodeInput');
    }
    if (searchParams.get('decodeInput')) {
        document.getElementById('decodeInput').value = searchParams.get('decodeInput');
    }
</script>
</body>
</html>
{{end}}