{{ define "desk/datadiff/diffdetail.html"}}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>接口Diff详情分析 - fwyytool</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    
    <style>
        :root {
            /* Apple Design System Colors */
            --apple-blue: #007AFF;
            --apple-blue-rgb: 0, 122, 255;
            --apple-blue-light: #5AC8FA;
            --apple-blue-dark: #0051D5;
            --apple-green: #34C759;
            --apple-orange: #FF9500;
            --apple-red: #FF3B30;
            --apple-purple: #AF52DE;
            --apple-pink: #FF2D92;
            --apple-yellow: #FFCC00;
            
            /* Neutral Colors - Apple Style */
            --apple-gray-1: #8E8E93;
            --apple-gray-2: #AEAEB2;
            --apple-gray-3: #C7C7CC;
            --apple-gray-4: #D1D1D6;
            --apple-gray-5: #E5E5EA;
            --apple-gray-6: #F2F2F7;
            
            /* Text Colors */
            --text-primary: #1D1D1F;
            --text-secondary: #86868B;
            --text-tertiary: #AEAEB2;
            --text-link: var(--apple-blue);
            
            /* Background Colors */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F5F5F7;
            --bg-tertiary: var(--apple-gray-6);
            --bg-elevated: #FFFFFF;
            
            /* Apple Spacing System */
            --spacing-2xs: 2px;
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 12px;
            --spacing-lg: 16px;
            --spacing-xl: 20px;
            --spacing-2xl: 24px;
            --spacing-3xl: 32px;
            --spacing-4xl: 40px;
            --spacing-5xl: 48px;
            
            /* Apple Border Radius */
            --radius-2xs: 2px;
            --radius-xs: 4px;
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-2xl: 20px;
            
            /* Apple Shadows */
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04);
            --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
            --shadow-xl: 0 16px 40px rgba(0, 0, 0, 0.16);
            --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.06);
            
            /* Apple Typography Scale */
            --font-size-xs: 11px;
            --font-size-sm: 13px;
            --font-size-base: 15px;
            --font-size-lg: 17px;
            --font-size-xl: 19px;
            --font-size-2xl: 22px;
            --font-size-3xl: 28px;
            --font-size-4xl: 34px;
            
            /* Font Weights - Apple Style */
            --font-weight-regular: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
        }
        
        body {
            background: var(--bg-secondary);
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
            color: var(--text-primary);
            font-size: var(--font-size-base);
            line-height: 1.47;
            letter-spacing: -0.022em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* Apple Typography Hierarchy */
        h1, .h1 { 
            font-size: var(--font-size-4xl); 
            font-weight: var(--font-weight-bold); 
            line-height: 1.12; 
            letter-spacing: -0.025em;
            margin-bottom: var(--spacing-lg);
        }
        h2, .h2 { 
            font-size: var(--font-size-3xl); 
            font-weight: var(--font-weight-semibold); 
            line-height: 1.18; 
            letter-spacing: -0.022em;
            margin-bottom: var(--spacing-md);
        }
        h3, .h3 { 
            font-size: var(--font-size-2xl); 
            font-weight: var(--font-weight-semibold); 
            line-height: 1.27; 
            letter-spacing: -0.019em;
            margin-bottom: var(--spacing-md);
        }
        h4, .h4 { 
            font-size: var(--font-size-xl); 
            font-weight: var(--font-weight-medium); 
            line-height: 1.32; 
            letter-spacing: -0.016em;
            margin-bottom: var(--spacing-sm);
        }
        h5, .h5 { 
            font-size: var(--font-size-lg); 
            font-weight: var(--font-weight-medium); 
            line-height: 1.35; 
            letter-spacing: -0.013em;
            margin-bottom: var(--spacing-sm);
        }
        h6, .h6 { 
            font-size: var(--font-size-base); 
            font-weight: var(--font-weight-semibold); 
            line-height: 1.4; 
            letter-spacing: -0.01em;
            margin-bottom: var(--spacing-xs);
        }
        
        /* Apple Text Styles */
        .text-large { font-size: var(--font-size-xl); line-height: 1.32; }
        .text-body { font-size: var(--font-size-base); line-height: 1.47; }
        .text-caption { font-size: var(--font-size-sm); line-height: 1.38; color: var(--text-secondary); }
        .text-footnote { font-size: var(--font-size-xs); line-height: 1.36; color: var(--text-tertiary); }
        
        .page-header {
            background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            padding: var(--spacing-3xl) 0 var(--spacing-2xl) 0;
            margin-bottom: var(--spacing-2xl);
            border-bottom: 1px solid var(--apple-gray-5);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }
        
        .page-header .container-fluid {
            position: relative;
        }
        
        .page-title {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
            letter-spacing: -0.025em;
        }
        
        .page-subtitle {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-regular);
            color: var(--text-secondary);
            margin-bottom: 0;
            letter-spacing: -0.016em;
        }
        
        .metric-card {
            background: var(--bg-elevated);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 0;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: 1px solid var(--apple-gray-5);
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
            height: 120px;
            display: flex;
            flex-direction: column;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }
        
        .metric-card-content {
            padding: var(--spacing-xl) var(--spacing-lg);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex: 1;
        }
        
        .metric-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }
        
        .metric-body {
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
        }
        
        /* 不同类型卡片的顶部装饰条颜色 */
        .metric-card.card-info::before {
            background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 100%);
        }
        
        .metric-card.card-danger::before {
            background: linear-gradient(90deg, #FF3B30 0%, #FF6B6B 100%);
        }
        
        .metric-card.card-success::before {
            background: linear-gradient(90deg, #34C759 0%, #30D158 100%);
        }
        
        .metric-card.card-warning::before {
            background: linear-gradient(90deg, #FF9500 0%, #FFCC00 100%);
        }
        
        .metric-icon {
            font-size: var(--font-size-lg);
            opacity: 0.8;
        }
        
        .metric-value {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            letter-spacing: -0.025em;
            color: var(--text-primary);
        }
        
        .metric-label {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            letter-spacing: -0.01em;
            margin: 0;
        }
        
        /* 图标颜色与装饰条保持一致 */
        .metric-card.card-info .metric-icon { color: var(--apple-blue); }
        .metric-card.card-danger .metric-icon { color: var(--apple-red); }
        .metric-card.card-success .metric-icon { color: var(--apple-green); }
        .metric-card.card-warning .metric-icon { color: var(--apple-orange); }
        
        .query-panel {
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-card);
            margin-bottom: var(--spacing-lg);
            overflow: hidden;
        }
        
        .panel-header {
            background: #f8f9fa;
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .panel-body {
            padding: var(--spacing-lg);
        }
        
        .enhanced-datatable {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-card);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }
        
        .table-controls {
            background: var(--bg-secondary);
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }
        
        .table-controls h5 {
            color: var(--text-primary);
            font-weight: 600;
            margin: 0;
        }
        
        .table-controls .badge {
            background: var(--info-color);
            color: white;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-sm);
        }
        
        .status-badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-xl);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-semibold);
            letter-spacing: -0.01em;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
        }
        
        .status-0 { 
            background: rgba(255, 149, 0, 0.1);
            color: var(--apple-orange); 
            border: 1px solid rgba(255, 149, 0, 0.2);
        }
        .status-1 { 
            background: rgba(52, 199, 89, 0.1);
            color: var(--apple-green); 
            border: 1px solid rgba(52, 199, 89, 0.2);
        }
        .status-2 { 
            background: rgba(255, 59, 48, 0.1);
            color: var(--apple-red); 
            border: 1px solid rgba(255, 59, 48, 0.2);
        }
        
        .diff-badge {
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .diff-has { background-color: #f8d7da; color: #721c24; }
        .diff-none { background-color: #d4edda; color: #155724; }
        
        .params-preview {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .stats-summary {
            background: var(--bg-elevated);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-card);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-2xl);
            border: 1px solid var(--apple-gray-5);
        }
        
        .time-filter-compact {
            background: rgba(255, 255, 255, 0.8);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--apple-gray-5);
            box-shadow: var(--shadow-sm);
        }
        
        .time-filter-compact .form-select,
        .time-filter-compact .form-control {
            background: var(--bg-primary);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-regular);
            padding: var(--spacing-xs) var(--spacing-sm);
            transition: all 0.2s ease;
        }
        
        .time-filter-compact .form-select:focus,
        .time-filter-compact .form-control:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            outline: none;
        }
        
        .time-filter-compact .btn {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
        }
        
        .table-controls .form-select-sm,
        .table-controls .form-control-sm {
            font-size: var(--font-size-sm);
            border-radius: var(--radius-sm);
            border: 1px solid var(--apple-gray-4);
            padding: var(--spacing-xs) var(--spacing-sm);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.2s ease;
        }
        
        .table-controls .form-select-sm:focus,
        .table-controls .form-control-sm:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            outline: none;
        }
        
        .table-controls .form-select-sm option {
            padding: var(--spacing-xs);
            color: var(--text-primary);
            background-color: var(--bg-primary);
        }
        
        /* DataTables 分页控件样式优化 */
        .dataTables_wrapper {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
        }
        
        .dataTables_length {
            margin-bottom: var(--spacing-sm);
        }
        
        .dataTables_length label {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .dataTables_length select {
            font-size: var(--font-size-sm);
            padding: var(--spacing-2xs) var(--spacing-xs);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            margin: 0 var(--spacing-xs);
            min-width: 60px;
        }
        
        .dataTables_info {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            padding: var(--spacing-2xs) 0;
            font-weight: var(--font-weight-regular);
        }
        
        .dataTables_paginate {
            padding: var(--spacing-2xs) 0;
        }
        
        .dataTables_paginate .paginate_button {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-xs);
            margin: 0 var(--spacing-2xs);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            background: var(--bg-primary);
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.2s ease;
            font-weight: var(--font-weight-medium);
        }
        
        .dataTables_paginate .paginate_button:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-3);
            color: var(--text-primary);
            transform: translateY(-1px);
        }
        
        .dataTables_paginate .paginate_button.current {
            background: var(--apple-blue);
            border-color: var(--apple-blue);
            color: white;
            font-weight: var(--font-weight-semibold);
        }
        
        .dataTables_paginate .paginate_button.disabled {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-5);
            color: var(--text-tertiary);
            cursor: not-allowed;
        }
        
        .dataTables_paginate .paginate_button.disabled:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-5);
            color: var(--text-tertiary);
            transform: none;
        }
        
        /* DataTables 底部布局优化 */
        .dataTables_wrapper .row {
            margin: 0;
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--bg-secondary);
            border-top: 1px solid var(--apple-gray-5);
        }
        
        .dataTables_wrapper .row:last-child {
            border-bottom-left-radius: var(--radius-xl);
            border-bottom-right-radius: var(--radius-xl);
        }
        
        .dataTables_wrapper .col-sm-12,
        .dataTables_wrapper .col-md-5,
        .dataTables_wrapper .col-md-7 {
            padding: 0;
        }
        
        .summary-toggle {
            cursor: pointer;
            color: var(--error-color);
            font-weight: bold;
            text-decoration: none;
        }
        
        .summary-toggle:hover {
            text-decoration: underline;
        }
        
        /* Apple Button System */
        .btn {
            border-radius: var(--radius-sm);
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-base);
            padding: var(--spacing-sm) var(--spacing-lg);
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: none;
            letter-spacing: -0.01em;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sm {
            font-size: var(--font-size-sm);
            padding: var(--spacing-xs) var(--spacing-md);
        }
        
        .btn-primary {
            background: var(--apple-blue);
            color: white;
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover {
            background: var(--apple-blue-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            color: white;
        }
        
        .btn-primary:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-outline-primary {
            border: 1px solid var(--apple-blue);
            color: var(--apple-blue);
            background: transparent;
        }
        
        .btn-outline-primary:hover {
            background: var(--apple-blue);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-outline-secondary {
            border: 1px solid var(--apple-gray-4);
            color: var(--text-secondary);
            background: var(--bg-primary);
        }
        
        .btn-outline-secondary:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-3);
            color: var(--text-primary);
            transform: translateY(-1px);
        }
        
        /* Apple Table System */
        .table {
            border-collapse: separate;
            border-spacing: 0;
            font-size: var(--font-size-base);
        }
        
        .table thead th {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: none;
            border-bottom: 1px solid var(--apple-gray-4);
            font-weight: var(--font-weight-semibold);
            font-size: var(--font-size-sm);
            padding: var(--spacing-md) var(--spacing-sm);
            letter-spacing: -0.01em;
            text-align: left;
        }
        
        .table tbody tr {
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border-bottom: 1px solid var(--apple-gray-5);
        }
        
        .table tbody tr:hover {
            background: rgba(0, 122, 255, 0.04);
            transform: translateY(-1px);
        }
        
        .table tbody tr:last-child {
            border-bottom: none;
        }
        
        .table tbody td {
            padding: var(--spacing-md) var(--spacing-sm);
            border-top: none;
            vertical-align: middle;
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }
        
        .enhanced-datatable {
            background: var(--bg-elevated);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-card);
            overflow: hidden;
            border: 1px solid var(--apple-gray-5);
        }

        /* ==================== 多选功能样式 ==================== */

        /* 选中行样式 */
        .table tbody tr.table-active {
            background: rgba(0, 122, 255, 0.08) !important;
            border-color: var(--apple-blue);
        }

        .table tbody tr.table-active:hover {
            background: rgba(0, 122, 255, 0.12) !important;
        }

        /* 批量操作工具栏样式 */
        .batch-toolbar {
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 复选框样式优化 */
        .form-check-input {
            border-radius: var(--radius-xs);
            border: 1.5px solid var(--apple-gray-4);
            transition: all 0.2s ease;
        }

        .form-check-input:checked {
            background-color: var(--apple-blue);
            border-color: var(--apple-blue);
        }

        .form-check-input:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .form-check-input:indeterminate {
            background-color: var(--apple-blue);
            border-color: var(--apple-blue);
        }

        /* 批量操作按钮样式 */
        .batch-toolbar .btn {
            font-size: var(--font-size-sm);
            padding: var(--spacing-xs) var(--spacing-md);
            border-radius: var(--radius-sm);
        }

        /* ==================== 筛选条件汇总样式 ==================== */
        .filter-summary-bar {
            border: 1px solid var(--apple-gray-4);
            transition: all 0.3s ease;
        }
        
        .filter-summary-bar:hover {
            border-color: var(--apple-blue);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
        }
        
        .filter-tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            background: var(--bg-secondary);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-xl);
            font-size: var(--font-size-sm);
            color: var(--text-primary);
            gap: 6px;
        }
        
        .filter-tag i {
            color: var(--apple-blue);
            font-size: 12px;
        }
        
        .filter-tag .filter-label {
            font-weight: var(--font-weight-medium);
        }
        
        .filter-tag .filter-value {
            color: var(--apple-blue);
        }
        
        /* ==================== 表格加载状态样式 ==================== */
        .table-loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(2px);
            -webkit-backdrop-filter: blur(2px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }
        
        .table-loading-content {
            text-align: center;
            padding: var(--spacing-xl);
        }
        
        .table-loading-spinner {
            width: 48px;
            height: 48px;
            border: 3px solid var(--apple-gray-5);
            border-top-color: var(--apple-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto var(--spacing-lg);
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .table-loading-text {
            color: var(--text-secondary);
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-medium);
            margin-top: var(--spacing-sm);
        }
        
        .table-loading-progress {
            color: var(--text-tertiary);
            font-size: var(--font-size-sm);
            margin-top: var(--spacing-xs);
        }
        
        /* ==================== 自定义多选组件样式 ==================== */

        .custom-multiselect {
            position: relative;
            width: 220px;
            font-size: var(--font-size-sm);
        }

        .status-filter .custom-multiselect {
            width: 140px;
        }

        .diff-type-filter .custom-multiselect {
            width: 140px;
        }

        .multiselect-trigger {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-xs) var(--spacing-sm);
            background: var(--bg-elevated);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: var(--font-size-sm);
        }

        .multiselect-trigger:hover {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .multiselect-trigger.active {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .multiselect-label {
            color: var(--text-primary);
            font-weight: var(--font-weight-regular);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            font-size: var(--font-size-sm);
        }

        .multiselect-label.has-selection {
            color: var(--apple-blue);
        }

        .multiselect-arrow {
            color: var(--apple-gray-3);
            font-size: 12px;
            transition: transform 0.2s ease;
            margin-left: 8px;
        }

        .multiselect-trigger.active .multiselect-arrow {
            transform: rotate(180deg);
        }

        .multiselect-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--bg-elevated);
            border: 1px solid var(--apple-gray-5);
            border-radius: var(--radius-sm);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            max-height: 340px;
            overflow: hidden;
            display: none;
            margin-top: 4px;
        }

        .multiselect-dropdown.show {
            display: block;
            animation: dropdownFadeIn 0.2s ease-out;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .multiselect-footer {
            padding: 8px 12px;
            border-top: 1px solid var(--apple-gray-5);
            background: var(--bg-secondary);
            display: flex;
            justify-content: flex-end;
        }

        .multiselect-search {
            padding: 8px;
            border-bottom: 1px solid var(--apple-gray-5);
        }

        .multiselect-search input {
            width: 100%;
            border: 1px solid var(--apple-gray-5);
            border-radius: var(--radius-xs);
            padding: 4px 8px;
            font-size: var(--font-size-sm);
        }

        .multiselect-options {
            max-height: 200px;
            overflow-y: auto;
            padding: 4px 0;
        }

        .multiselect-option {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            margin: 0;
            font-weight: normal;
        }

        .multiselect-option:hover {
            background: rgba(0, 122, 255, 0.08);
        }

        .multiselect-option input[type="checkbox"],
        .multiselect-option input[type="radio"] {
            display: none;
        }

        .checkmark {
            width: 16px;
            height: 16px;
            border: 2px solid var(--apple-gray-4);
            border-radius: var(--radius-xs);
            margin-right: 8px;
            position: relative;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }

        .multiselect-option input[type="checkbox"]:checked + .checkmark,
        .multiselect-option input[type="radio"]:checked + .checkmark {
            background: var(--apple-blue);
            border-color: var(--apple-blue);
        }

        .multiselect-option input[type="checkbox"]:checked + .checkmark::after {
            content: '';
            position: absolute;
            left: 4px;
            top: 1px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .multiselect-option input[type="radio"]:checked + .checkmark::after {
            content: '';
            position: absolute;
            left: 4px;
            top: 4px;
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
        }

        
        .option-text {
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 选中计数样式 */
        .selection-count {
            background: var(--apple-blue);
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 4px;
        }
        
        .table-controls {
            background: var(--bg-secondary);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-bottom: 1px solid var(--apple-gray-5);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }
        
        /* Apple Responsive Design */
        @media (max-width: 768px) {
            .page-header {
                padding: var(--spacing-2xl) 0 var(--spacing-xl) 0;
            }
            
            .page-title {
                font-size: var(--font-size-3xl);
            }
            
            .page-subtitle {
                font-size: var(--font-size-base);
            }
            
            .table-controls {
                flex-direction: column;
                align-items: stretch;
                padding: var(--spacing-lg);
            }
            
            .metric-card {
                height: 110px;
            }
            
            .metric-card-content {
                padding: var(--spacing-lg) var(--spacing-md);
            }
            
            .metric-value {
                font-size: var(--font-size-3xl);
            }
            
            .time-filter-compact .d-flex {
                flex-direction: column;
                gap: var(--spacing-sm);
            }
            
            .custom-time-range .d-flex {
                flex-direction: column;
                gap: var(--spacing-xs);
            }
            
            .time-filter-compact .form-select,
            .time-filter-compact .form-control {
                width: 100% !important;
            }
            
            /* 移动端表格内边距调整 */
            .table thead th,
            .table tbody td {
                padding: var(--spacing-lg) var(--spacing-md);
            }
            
            /* 移动端DataTables样式调整 */
            .dataTables_length label {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-xs);
            }
            
            .dataTables_length select {
                margin: 0;
                width: 100px;
            }
            
            .dataTables_paginate .paginate_button {
                padding: var(--spacing-xs) var(--spacing-sm);
                margin: 0 1px;
                font-size: var(--font-size-xs);
            }
        }
        
        @media (max-width: 480px) {
            .page-header .d-flex {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-lg);
            }
            
            .time-filter-compact {
                width: 100%;
            }
        }

        /* 排序功能样式 */
        .sortable {
            cursor: pointer;
            user-select: none;
            transition: background-color 0.2s ease;
        }

        .sortable:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }

        .sort-icon {
            opacity: 0.5;
            transition: all 0.2s ease;
        }

        .sortable:hover .sort-icon {
            opacity: 1;
        }

        .sortable.sort-asc .sort-icon:before {
            content: "\f0de"; /* fa-sort-up */
            opacity: 1;
        }

        .sortable.sort-desc .sort-icon:before {
            content: "\f0dd"; /* fa-sort-down */
            opacity: 1;
        }

        /* 自定义分页控件样式 */
        .custom-pagination-wrapper {
            background: var(--bg-secondary);
            border-top: 1px solid var(--apple-gray-5);
            border-bottom-left-radius: var(--radius-xl);
            border-bottom-right-radius: var(--radius-xl);
        }

        .pagination-info {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
        }

        .page-size-selector label {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            font-weight: var(--font-weight-medium);
        }

        .page-size-selector .form-select {
            font-size: var(--font-size-sm);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.2s ease;
        }

        .page-size-selector .form-select:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            outline: none;
        }

        /* 优化的分页大小选择器样式 */
        .page-size-selector .dropdown-toggle {
            font-size: var(--font-size-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.2s ease;
            font-weight: var(--font-weight-medium);
        }

        .page-size-selector .dropdown-toggle:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-3);
        }

        .page-size-selector .dropdown-toggle:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            outline: none;
        }

        .page-size-selector .dropdown-menu {
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: var(--spacing-xs);
            min-width: 80px;
        }

        .page-size-selector .dropdown-item {
            font-size: var(--font-size-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-xs);
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.2s ease;
            text-align: center;
        }

        .page-size-selector .dropdown-item:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .page-size-selector .dropdown-item.active {
            background: var(--apple-blue);
            color: white;
            font-weight: var(--font-weight-semibold);
        }

        .pagination .page-link {
            font-size: var(--font-size-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
            margin: 0 var(--spacing-2xs);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            background: var(--bg-primary);
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.2s ease;
            font-weight: var(--font-weight-medium);
        }

        .pagination .page-link:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-3);
            color: var(--text-primary);
            transform: translateY(-1px);
        }

        .pagination .page-item.active .page-link {
            background: var(--apple-blue);
            border-color: var(--apple-blue);
            color: white;
            font-weight: var(--font-weight-semibold);
        }

        .pagination .page-item.disabled .page-link {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-5);
            color: var(--text-tertiary);
            cursor: not-allowed;
        }

        .pagination .page-item.disabled .page-link:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-5);
            color: var(--text-tertiary);
            transform: none;
        }

        /* Diff配置管理弹框优化样式 */
        .modal-header.border-bottom {
            border-bottom: 2px solid var(--apple-gray-5) !important;
            padding: 1.25rem 1.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .modal-header .modal-title {
            font-weight: 600;
            font-size: 1.25rem;
        }

        .modal-header .modal-title i {
            font-size: 1.1rem;
        }

        /* 控制面板样式 */
        .modal-header .bg-light.rounded-pill {
            background: rgba(var(--apple-blue-rgb), 0.1) !important;
            border: 1px solid rgba(var(--apple-blue-rgb), 0.2);
            transition: all 0.2s ease;
        }

        .modal-header .bg-light.rounded-pill:hover {
            background: rgba(var(--apple-blue-rgb), 0.15) !important;
            border-color: rgba(var(--apple-blue-rgb), 0.3);
        }

        /* 按钮组样式优化 */
        .modal-header .btn-group .btn {
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .modal-header .btn-group .btn:first-child {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .modal-header .btn-group .btn:last-child {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: none;
        }

        .modal-header .btn-group .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .modal-header {
                flex-direction: column;
                align-items: stretch !important;
                gap: 1rem;
            }

            .modal-header .d-flex.flex-grow-1 {
                flex-direction: column;
                align-items: stretch !important;
            }

            .modal-header .modal-title {
                text-align: center;
                margin-bottom: 0.75rem !important;
                margin-right: 0 !important;
            }

            .modal-header .d-flex.gap-3 {
                justify-content: center;
                margin: 0 !important;
                flex-wrap: wrap;
                gap: 0.75rem !important;
            }

            .modal-header .bg-light.rounded-pill {
                flex: 1;
                min-width: 140px;
                justify-content: center;
            }

            .modal-header .btn-group {
                flex: 1;
                min-width: 120px;
            }

            .modal-header .btn-group .btn {
                flex: 1;
                font-size: 0.875rem;
            }

            .modal-header .btn-close {
                position: absolute;
                top: 1rem;
                right: 1rem;
            }
        }

        @media (max-width: 576px) {
            .modal-header .d-flex.gap-3 {
                flex-direction: column;
                align-items: stretch;
            }

            .modal-header .bg-light.rounded-pill,
            .modal-header .btn-group {
                width: 100%;
                min-width: unset;
            }

            .modal-header .btn-group .btn {
                padding: 0.5rem 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container-fluid">
                        <div class="d-flex align-items-center justify-content-between">
                <div>
                    <div class="d-flex align-items-center">
                        <h1 class="page-title mb-0 me-3">
                            接口回放Diff详情分析
                        </h1>
                        <button type="button" class="btn btn-primary btn-sm" onclick="loadDiffControlConfig()" title="配置管理">
                            <i class="fas fa-cog me-1"></i>配置管理
                        </button>
                    </div>
                </div>
                <div class="text-end">
                    <div class="d-flex align-items-center gap-3">
                        <!-- 时间范围筛选器 -->
                        <div class="time-filter-compact">
                            <div class="d-flex align-items-center gap-2">
                                <select class="form-select form-select-sm" id="timePreset" style="width: 120px;">
                                    <option value="24h">最近24小时</option>
                                    <option value="7d">最近7天</option>
                                    <option value="30d">最近30天</option>
                                    <option value="custom">自定义</option>
                                </select>
                                <div class="custom-time-range" id="customTimeRange" style="display: none;">
                                    <div class="d-flex align-items-center gap-1">
                                        <input type="datetime-local" class="form-control form-control-sm" id="startTime" style="width: 140px;">
                                        <span class="text-muted small">至</span>
                                        <input type="datetime-local" class="form-control form-control-sm" id="endTime" style="width: 140px;">
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary btn-sm" id="applyTimeFilter">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container-fluid">
        <!-- 数据概览 -->
        <div class="row mb-4 g-3">
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-info">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="metric-label">总任务数</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value" id="totalCount">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-danger">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="metric-label">有差异任务</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value" id="hasDiffCount">
                                <div class="spinner-border spinner-border-sm text-danger" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-success">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="metric-label">无差异任务</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value" id="noDiffCount">
                                <div class="spinner-border spinner-border-sm text-success" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-warning">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-hourglass-half"></i>
                            </div>
                            <div class="metric-label">未完成任务</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value" id="unfinishedCount">
                                <div class="spinner-border spinner-border-sm text-warning" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-summary mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0 fw-bold">
                    <i class="fas fa-chart-bar me-2 text-primary"></i>
                    统计概览
                    <small class="text-muted ms-2" id="statsTimeRange">（最近24小时）</small>
                </h6>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="toggleStatsOverview()">
                    <i class="fas fa-chevron-down me-1" id="statsToggleIcon"></i>
                    <span id="statsToggleText">展开</span>
                </button>
            </div>
            <div id="statsOverviewData" class="collapse">
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 增强数据表格 -->
        <div class="enhanced-datatable">
            <div class="table-controls">
                <div class="d-flex align-items-center justify-content-between w-100">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 me-3" style="font-weight: var(--font-weight-semibold); color: var(--text-primary);">
                        Diff结果列表
                    </h5>
                    <span id="searchStatus" class="badge ms-2" style="display: none; background: rgba(255, 149, 0, 0.1); color: var(--apple-orange); border: 1px solid rgba(255, 149, 0, 0.2); font-size: var(--font-size-xs); padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm);">
                        <i class="fas fa-filter me-1"></i>已应用搜索条件
                    </span>
                </div>
                    <div class="d-flex align-items-center gap-3">
                        <!-- 接口筛选 -->
                        <div class="interface-filter">
                            <div class="custom-multiselect" id="interfaceMultiselect">
                                <div class="multiselect-trigger">
                                    <span class="multiselect-label">选择接口</span>
                                    <i class="fas fa-chevron-down multiselect-arrow"></i>
                                </div>
                                <div class="multiselect-dropdown">
                                    <div class="multiselect-search">
                                        <input type="text" placeholder="搜索接口..." class="form-control form-control-sm">
                                    </div>
                                    <div class="multiselect-options" id="interfaceOptions">
                                        <!-- 选项通过loadInterfaceList()动态加载 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 状态筛选 -->
                        <div class="status-filter">
                            <div class="custom-multiselect" id="statusMultiselect">
                                <div class="multiselect-trigger">
                                    <span class="multiselect-label">选择状态</span>
                                    <i class="fas fa-chevron-down multiselect-arrow"></i>
                                </div>
                                <div class="multiselect-dropdown">
                                    <div class="multiselect-options" id="statusOptions">
                                        <label class="multiselect-option">
                                            <input type="checkbox" value="0" data-label="未完成">
                                            <span class="checkmark"></span>
                                            <span class="option-text">未完成</span>
                                        </label>
                                        <label class="multiselect-option">
                                            <input type="checkbox" value="1" data-label="已完成">
                                            <span class="checkmark"></span>
                                            <span class="option-text">已完成</span>
                                        </label>
                                        <label class="multiselect-option">
                                            <input type="checkbox" value="2" data-label="失败">
                                            <span class="checkmark"></span>
                                            <span class="option-text">失败</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 差异类型筛选 -->
                        <div class="diff-type-filter">
                            <div class="custom-multiselect" id="diffTypeMultiselect">
                                <div class="multiselect-trigger">
                                    <span class="multiselect-label">差异类型</span>
                                    <i class="fas fa-chevron-down multiselect-arrow"></i>
                                </div>
                                <div class="multiselect-dropdown">
                                    <div class="multiselect-options" id="diffTypeOptions">
                                        <label class="multiselect-option">
                                            <input type="radio" name="diffType" value="" data-label="全部" checked>
                                            <span class="checkmark"></span>
                                            <span class="option-text">全部</span>
                                        </label>
                                        <label class="multiselect-option">
                                            <input type="radio" name="diffType" value="1" data-label="有差异">
                                            <span class="checkmark"></span>
                                            <span class="option-text">有差异</span>
                                        </label>
                                        <label class="multiselect-option">
                                            <input type="radio" name="diffType" value="0" data-label="无差异">
                                            <span class="checkmark"></span>
                                            <span class="option-text">无差异</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 搜索框 -->
                <div class="table-search">
                    <div class="d-flex gap-1">
                                <input type="text" class="form-control form-control-sm" id="tableSearch" placeholder="搜索接口名、指纹..." style="width: 200px;">
                                <button class="btn btn-primary btn-sm" type="button" id="searchButton" title="搜索">
                                    <i class="fas fa-search"></i>
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" type="button" id="clearSearch" title="清除搜索">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 批量操作工具栏 -->
            <div id="batchToolbar" class="batch-toolbar mb-3" style="display: none;">
                <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                    <div class="d-flex align-items-center">
                        <span class="text-muted me-3">已选择 <span id="selectedCount" class="fw-bold text-primary">0</span> 项</span>
                        <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="clearSelection()">
                            <i class="fas fa-times me-1"></i>清空选择
                        </button>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="batchExport()">
                            <i class="fas fa-download me-1"></i>批量导出
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="batchRerun()">
                            <i class="fas fa-redo me-1"></i>批量重新运行
                        </button>
                    </div>
                </div>
            </div>

            <!-- 筛选条件汇总区域 -->
            <div id="filterSummaryBar" class="filter-summary-bar mb-3" style="display: none;">
                <div class="p-3 bg-white rounded shadow-sm">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center gap-3">
                            <span class="fw-bold text-secondary">
                                <i class="fas fa-filter me-2"></i>当前筛选条件：
                            </span>
                            <div id="filterSummaryContent" class="d-flex align-items-center gap-2 flex-wrap">
                                <!-- 筛选条件将动态插入这里 -->
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearAllFilters()" title="清除所有筛选条件">
                            <i class="fas fa-times me-1"></i>清除全部
                        </button>
                    </div>
                </div>
            </div>

            <div class="table-responsive" style="position: relative;">
                <div id="tableLoadingOverlay" class="table-loading-overlay" style="display: none;">
                    <div class="table-loading-content">
                        <div class="table-loading-spinner"></div>
                        <div class="table-loading-text">正在加载数据...</div>
                        <div class="table-loading-progress" id="tableLoadingProgress">第 1 页，每页 25 条</div>
                    </div>
                </div>
                <div id="tableErrorOverlay" class="table-loading-overlay" style="display: none;">
                    <div class="table-loading-content">
                        <div style="font-size: 48px; color: var(--apple-red); margin-bottom: var(--spacing-lg);">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="table-loading-text">数据加载失败</div>
                        <div class="table-loading-progress">请检查网络连接或刷新页面重试</div>
                        <button type="button" class="btn btn-primary mt-3" onclick="loadDataWithPagination()">
                            <i class="fas fa-redo me-2"></i>重新加载
                        </button>
                    </div>
                </div>
                <table id="diffDataTable" class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th width="50" class="text-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    <label class="form-check-label" for="selectAll"></label>
                                </div>
                            </th>
                            <th width="200">接口信息</th>
                            <th width="180">请求参数</th>
                            <th width="80">差异数</th>
                            <th width="150">旧数据</th>
                            <th width="150">新数据</th>
                            <th width="120">状态</th>
                            <th width="120" class="sortable" data-sort="createTime">
                                创建时间
                                <i class="fas fa-sort ms-1 sort-icon"></i>
                            </th>
                            <th width="120" class="sortable" data-sort="updateTime">
                                更新时间
                                <i class="fas fa-sort ms-1 sort-icon"></i>
                            </th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody id="diffTableBody">
                        <tr>
                            <td colspan="10" class="text-center py-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div class="mt-2 text-muted">正在加载数据...</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- 自定义分页控件 -->
            <div class="custom-pagination-wrapper">
                <div class="d-flex justify-content-between align-items-center p-3 bg-light border-top">
                    <div class="pagination-info">
                        <span class="text-muted">
                            显示第 <span id="currentPageStart">1</span> - <span id="currentPageEnd">25</span> 条，
                            共 <span id="totalRecords">0</span> 条记录
                        </span>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <!-- 每页条数选择 - 优化UI -->
                        <div class="page-size-selector d-flex align-items-center">
                            <span class="text-muted me-2">每页显示：</span>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="pageSizeDropdown" data-bs-toggle="dropdown" aria-expanded="false" style="min-width: 60px;">
                                    <span id="currentPageSize">25</span>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="pageSizeDropdown">
                                    <li><a class="dropdown-item page-size-option" href="#" data-size="10">10</a></li>
                                    <li><a class="dropdown-item page-size-option active" href="#" data-size="25">25</a></li>
                                    <li><a class="dropdown-item page-size-option" href="#" data-size="50">50</a></li>
                                    <li><a class="dropdown-item page-size-option" href="#" data-size="100">100</a></li>
                                </ul>
                            </div>
                        </div>
                        <!-- 分页导航 -->
                        <nav aria-label="分页导航">
                            <ul class="pagination pagination-sm mb-0" id="customPagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 数据查看模态框 -->
    <div class="modal fade" id="dataModal" tabindex="-1" aria-labelledby="dataModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="dataModalLabel">
                        <i class="fas fa-code me-2"></i>
                        数据详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="json-viewer">
                        <pre id="dataModalContent"></pre>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="copyToClipboard()">
                        <i class="fas fa-copy me-1"></i>复制
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Diff结果模态框 -->
    <div class="modal fade" id="diffDetailModal" tabindex="-1" aria-labelledby="diffDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="diffDetailModalLabel">
                        <i class="fas fa-code-branch me-2"></i>
                        Diff结果对比
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="diffTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="params-tab" data-bs-toggle="tab" data-bs-target="#params" type="button" role="tab">
                                <i class="fas fa-cog me-1"></i>请求参数
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="old-data-tab" data-bs-toggle="tab" data-bs-target="#old-data" type="button" role="tab">
                                <i class="fas fa-history me-1"></i>旧数据
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="new-data-tab" data-bs-toggle="tab" data-bs-target="#new-data" type="button" role="tab">
                                <i class="fas fa-plus me-1"></i>新数据
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="diffTabContent">
                        <div class="tab-pane fade show active" id="params" role="tabpanel">
                            <div class="json-viewer mt-3">
                                <pre id="diffParams"></pre>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="old-data" role="tabpanel">
                            <div class="json-viewer mt-3">
                                <pre id="diffOldData"></pre>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="new-data" role="tabpanel">
                            <div class="json-viewer mt-3">
                                <pre id="diffNewData"></pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <a id="diffResultLink" href="#" class="btn btn-primary" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i>查看Diff结果
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS 和依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        let statsOverviewLoaded = false;
        let currentModalContent = '';
        let currentTimeRange = { preset: '24h', startTime: null, endTime: null };
        let currentSort = { field: 'updateTime', order: 'desc' };
        let pageDataLoaded = false; // 防止重复加载
        let dataTablesInitialized = false; // 防止重复初始化DataTables

        // 分页状态管理
        let currentPagination = {
            page: 1,
            pageSize: 25,
            total: 0,
            totalPages: 0
        };

        // 多选功能状态管理
        let selectedIds = []; // 存储选中的记录ID

        // 自定义多选组件状态管理
        let selectedStatuses = []; // 选中的状态
        let selectedInterfaces = []; // 选中的接口
        let selectedDiffType = ''; // 选中的差异类型（空字符串表示全部，1表示有差异，0表示无差异）

        // 初始化DataTables
        function initializeDataTables() {
            if (dataTablesInitialized) {
                console.log('DataTables已初始化，跳过');
                return;
            }

            try {
                // 检查表格是否存在且有正确的结构
                const table = $('#diffDataTable');
                if (table.length && table.find('thead').length && table.find('tbody').length) {
                    // 如果已经初始化过，先销毁
                    if ($.fn.DataTable.isDataTable('#diffDataTable')) {
                        $('#diffDataTable').DataTable().destroy();
                    }

                    $('#diffDataTable').DataTable({
                        responsive: true,
                        language: {
                            url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/zh.json'
                        },
                        order: [], // 禁用默认排序，使用后端排序
                        searching: false, // 禁用内置搜索，使用后端搜索
                        paging: false, // 禁用前端分页，使用后端分页
                        lengthChange: false, // 禁用每页条数选择，使用自定义控件
                        info: false, // 禁用信息显示，使用自定义信息
                        destroy: true, // 允许重新初始化
                        ordering: false // 禁用前端排序，使用后端排序
                    });

                    dataTablesInitialized = true;
                    console.log('DataTables 初始化成功');
                } else {
                    console.error('表格结构不完整，无法初始化DataTables');
                }
            } catch (error) {
                console.error('DataTables 初始化失败:', error);
            }
        }

        // 设置默认时间范围（最近24小时）
        function setDefaultTimeRange() {
            const endTime = moment();
            const startTime = moment().subtract(24, 'hours');

            currentTimeRange = {
                preset: '24h',
                startTime: startTime.unix(),
                endTime: endTime.unix()
            };

            console.log('设置默认时间范围:', currentTimeRange);

            // 设置UI显示
            $('#timePreset').val('24h');
            updateStatsTimeRangeDisplay('24h', startTime, endTime);
        }
        
        // 初始化页面
        $(document).ready(function() {
            // 初始化时间显示
            moment.locale('zh-cn');

            // 初始化全局时间筛选器
            initTimeFilter();

            // 从URL参数恢复时间范围
            initTimeRangeFromURL();

            // 不在这里初始化DataTables，等数据加载完成后再初始化
            console.log('跳过DataTables初始化，等待数据加载完成');

            // 绑定筛选事件
            bindFilterEvents();

            // 绑定排序事件
            bindSortEvents();

            // 初始化排序图标
            updateSortIcons();

            // 初始化自定义多选组件
            initCustomMultiselect();

            // 确保时间范围设置完成后再加载数据
            setTimeout(() => {
                console.log('=== 页面初始化开始 ===');
                console.log('当前时间范围:', currentTimeRange);

                // 确保时间范围已初始化
                if (!currentTimeRange.startTime || !currentTimeRange.endTime) {
                    console.log('时间范围未初始化，使用默认时间范围');
                    setDefaultTimeRange();
                }

                console.log('最终时间范围:', currentTimeRange);
                console.log('开始加载页面数据...');

                // 强制加载数据，即使有其他问题
                try {
                    loadPageData();
                } catch (error) {
                    console.error('loadPageData 执行失败:', error);
                    // 如果loadPageData失败，直接调用各个API
                    console.log('尝试直接调用各个API...');
                    loadOverviewData().catch(e => console.error('概览数据加载失败:', e));
                    loadDataWithPagination().catch(e => console.error('差异数据加载失败:', e));
                    loadInterfaceList().catch(e => console.error('接口列表加载失败:', e));
                }
            }, 300);
        });

        // 从URL参数初始化时间范围
        function initTimeRangeFromURL() {
            console.log('初始化时间范围从URL参数...');
            const urlParams = new URLSearchParams(window.location.search);
            const startTime = urlParams.get('startTime');
            const endTime = urlParams.get('endTime');

            console.log('URL参数 - startTime:', startTime, 'endTime:', endTime);

            if (startTime && endTime) {
                // 从URL参数恢复时间范围
                const startMoment = moment.unix(startTime);
                const endMoment = moment.unix(endTime);

                currentTimeRange = {
                    preset: 'custom', // 先设为custom，后面会自动检测
                    startTime: parseInt(startTime),
                    endTime: parseInt(endTime)
                };

                // 自动检测时间范围类型并设置UI
                detectTimeRangeType();

                // 如果是自定义时间范围，设置自定义时间输入框的值
                if (currentTimeRange.preset === 'custom') {
                    $('#startTime').val(startMoment.format('YYYY-MM-DDTHH:mm'));
                    $('#endTime').val(endMoment.format('YYYY-MM-DDTHH:mm'));
                }

                // 更新显示
                const preset = currentTimeRange.preset;
                if (preset === 'custom') {
                    updateStatsTimeRangeDisplay('custom', startMoment, endMoment);
                } else {
                    updateStatsTimeRangeDisplay(preset);
                }

                console.log('从URL恢复时间范围:', startMoment.format('YYYY-MM-DD HH:mm'), '至', endMoment.format('YYYY-MM-DD HH:mm'), '类型:', preset);
            } else {
                // 没有URL参数时使用默认时间范围
                console.log('没有URL参数，使用默认时间范围');
                setDefaultTimeRange();
            }
        }

        // 检测并设置时间范围类型
        function detectTimeRangeType() {
            if (!currentTimeRange.startTime || !currentTimeRange.endTime) {
                return;
            }

            const now = moment();
            const startMoment = moment.unix(currentTimeRange.startTime);
            const endMoment = moment.unix(currentTimeRange.endTime);

            // 检查是否匹配预设时间范围（允许5分钟误差）
            const tolerance = 5 * 60; // 5分钟

            // 检查24小时
            const start24h = now.clone().subtract(24, 'hours');
            if (Math.abs(startMoment.unix() - start24h.unix()) < tolerance &&
                Math.abs(endMoment.unix() - now.unix()) < tolerance) {
                $('#timePreset').val('24h');
                $('#customTimeRange').hide();
                currentTimeRange.preset = '24h';
                return;
            }

            // 检查7天
            const start7d = now.clone().subtract(7, 'days');
            if (Math.abs(startMoment.unix() - start7d.unix()) < tolerance &&
                Math.abs(endMoment.unix() - now.unix()) < tolerance) {
                $('#timePreset').val('7d');
                $('#customTimeRange').hide();
                currentTimeRange.preset = '7d';
                return;
            }

            // 检查30天
            const start30d = now.clone().subtract(30, 'days');
            if (Math.abs(startMoment.unix() - start30d.unix()) < tolerance &&
                Math.abs(endMoment.unix() - now.unix()) < tolerance) {
                $('#timePreset').val('30d');
                $('#customTimeRange').hide();
                currentTimeRange.preset = '30d';
                return;
            }

            // 如果都不匹配，则为自定义时间范围
            $('#timePreset').val('custom');
            $('#customTimeRange').show();
            currentTimeRange.preset = 'custom';

            console.log('检测为自定义时间范围，显示自定义时间输入框');
        }

        // 初始化时间筛选器
        function initTimeFilter() {
            $('#timePreset').on('change', function() {
                const preset = $(this).val();
                if (preset === 'custom') {
                    $('#customTimeRange').show();
                    setCustomTimeInputs();
                } else {
                    $('#customTimeRange').hide();
                    updateTimeRangeByPreset(preset);
                    // 预设时间范围变化时立即更新URL参数
                    updateURLParams();
                }
            });

            $('#applyTimeFilter').on('click', function() {
                applyTimeFilter();
            });
        }

        // 更新URL参数（不刷新页面）
        function updateURLParams() {
            if (currentTimeRange.startTime && currentTimeRange.endTime) {
                const params = new URLSearchParams();
                params.append('startTime', currentTimeRange.startTime);
                params.append('endTime', currentTimeRange.endTime);

                const newURL = window.location.pathname + '?' + params.toString();
                window.history.replaceState({}, '', newURL);

                console.log('更新URL参数:', newURL);
            }
        }

        // 设置默认时间范围
        function setDefaultTimeRange() {
            updateTimeRangeByPreset('24h');
            $('#timePreset').val('24h');
            updateURLParams();
        }
        
        // 根据预设更新时间范围
        function updateTimeRangeByPreset(preset) {
            const now = moment();
            let startTime, endTime;
            
            switch(preset) {
                case '24h':
                    startTime = now.clone().subtract(24, 'hours');
                    endTime = now;
                    break;
                case '7d':
                    startTime = now.clone().subtract(7, 'days');
                    endTime = now;
                    break;
                case '30d':
                    startTime = now.clone().subtract(30, 'days');
                    endTime = now;
                    break;
            }
            
            currentTimeRange = {
                preset: preset,
                startTime: startTime.unix(),
                endTime: endTime.unix()
            };
            
            updateStatsTimeRangeDisplay(preset);
        }
        
        // 设置自定义时间输入框
        function setCustomTimeInputs() {
            const now = moment();
            const yesterday = now.clone().subtract(1, 'day');
            
            $('#startTime').val(yesterday.format('YYYY-MM-DDTHH:mm'));
            $('#endTime').val(now.format('YYYY-MM-DDTHH:mm'));
        }
        
        // 应用时间筛选
        function applyTimeFilter() {
            const preset = $('#timePreset').val();

            if (preset === 'custom') {
                const startTimeStr = $('#startTime').val();
                const endTimeStr = $('#endTime').val();

                if (!startTimeStr || !endTimeStr) {
                    alert('请选择开始时间和结束时间');
                    return;
                }

                const startTime = moment(startTimeStr);
                const endTime = moment(endTimeStr);

                if (startTime.isAfter(endTime)) {
                    alert('开始时间不能晚于结束时间');
                    return;
                }

                currentTimeRange = {
                    preset: 'custom',
                    startTime: startTime.unix(),
                    endTime: endTime.unix()
                };

                updateStatsTimeRangeDisplay('custom', startTime, endTime);
            } else {
                updateTimeRangeByPreset(preset);
            }

            console.log('应用时间筛选，新的时间范围:', currentTimeRange);

            // 更新URL参数
            updateURLParams();

            // 重新加载所有数据
            reloadAllData();
        }
        
        // 更新统计时间范围显示
        function updateStatsTimeRangeDisplay(preset, startTime = null, endTime = null) {
            let displayText = '';
            switch(preset) {
                case '24h':
                    displayText = '（最近24小时）';
                    break;
                case '7d':
                    displayText = '（最近7天）';
                    break;
                case '30d':
                    displayText = '（最近30天）';
                    break;
                case 'custom':
                    if (startTime && endTime) {
                        displayText = `（${startTime.format('MM-DD HH:mm')} 至 ${endTime.format('MM-DD HH:mm')}）`;
                    }
                    break;
            }
            $('#statsTimeRange').text(displayText);
        }
        
        // 加载页面数据（分别调用三个接口）
        function loadPageData() {
            if (pageDataLoaded) {
                console.log('页面数据已加载，跳过重复加载');
                return;
            }

            console.log('开始加载页面数据...', currentTimeRange);

            // 确保时间范围已设置
            if (!currentTimeRange.startTime || !currentTimeRange.endTime) {
                console.error('时间范围未设置，无法加载数据');
                return;
            }

            pageDataLoaded = true;

            // 并发加载数据，使用新的统一数据加载逻辑
            Promise.all([
                loadOverviewData(),
                loadDataWithPagination(), // 使用新的统一数据加载函数
                loadInterfaceList() // 单独加载接口列表
            ]).then(() => {
                console.log('所有页面数据加载完成');
                // 更新差异数量显示
                updateDiffCountDisplay();
            }).catch(error => {
                console.error('加载页面数据失败:', error);
                pageDataLoaded = false; // 重置标志，允许重试
            });
        }

        // 加载概览数据
        function loadOverviewData() {
            return new Promise((resolve, reject) => {
                // 构建查询参数
                const params = new URLSearchParams();
                if (currentTimeRange.startTime) {
                    params.append('startTime', currentTimeRange.startTime);
                }
                if (currentTimeRange.endTime) {
                    params.append('endTime', currentTimeRange.endTime);
                }

                const url = '/fwyytool/tools/diff/overview' + (params.toString() ? '?' + params.toString() : '');
                console.log('请求概览数据:', url);

                fetch(url)
                    .then(response => {
                        console.log('概览数据响应状态:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('概览数据响应:', data);
                        if (data.errNo === 0 && data.data) {
                            updateOverviewMetrics(data.data);
                            resolve(data.data);
                        } else {
                            console.error('概览数据API响应错误:', data.errMsg || '未知错误');
                            showOverviewError();
                            reject(new Error(data.errMsg || '加载概览数据失败'));
                        }
                    })
                    .catch(error => {
                        console.error('请求概览数据失败:', error);
                        showOverviewError();
                        reject(error);
                    });
            });
        }

        // 更新概览指标
        function updateOverviewMetrics(data) {
            // 更新四个指标卡片
            document.getElementById('totalCount').textContent = data.total || 0;
            document.getElementById('hasDiffCount').textContent = data.hasDiffNum || 0;
            document.getElementById('noDiffCount').textContent = data.noDiffNum || 0;
            document.getElementById('unfinishedCount').textContent = data.unFinishedTask || 0;
            
            console.log('概览数据更新完成:', data);
        }

        // 显示概览数据加载错误
        function showOverviewError() {
            document.getElementById('totalCount').innerHTML = '<span class="text-danger">-</span>';
            document.getElementById('hasDiffCount').innerHTML = '<span class="text-danger">-</span>';
            document.getElementById('noDiffCount').innerHTML = '<span class="text-danger">-</span>';
            document.getElementById('unfinishedCount').innerHTML = '<span class="text-danger">-</span>';
        }

        // 加载接口列表
        function loadInterfaceList() {
            return new Promise((resolve, reject) => {
                const params = new URLSearchParams();
                if (currentTimeRange.startTime) {
                    params.append('startTime', currentTimeRange.startTime);
                }
                if (currentTimeRange.endTime) {
                    params.append('endTime', currentTimeRange.endTime);
                }

                const url = '/fwyytool/tools/diff/handlernames' + (params.toString() ? '?' + params.toString() : '');
                console.log('请求接口列表（新接口）:', url);

                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.errNo === 0 && data.data && data.data) {
                            updateInterfaceListFromArray(data.data.handlerNames);
                            resolve(data.data.handlerNames);
                        } else {
                            console.error('接口列表API响应错误:', data.errMsg || '未知错误');
                            reject(new Error(data.errMsg || '加载接口列表失败'));
                        }
                    })
                    .catch(error => {
                        console.error('请求接口列表失败，尝试降级到原接口:', error);
                    });
            });
        }

        // 从接口数组更新下拉框的函数
        function updateInterfaceListFromArray(interfaces) {
            const interfaceOptions = document.getElementById('interfaceOptions');
            if (!interfaceOptions) return;

            // 清空现有选项
            interfaceOptions.innerHTML = '';

            // 添加新选项
            interfaces.forEach(interfaceName => {
                const optionHtml = `
                    <label class="multiselect-option">
                        <input type="checkbox" value="${interfaceName}" data-label="${interfaceName}">
                        <span class="checkmark"></span>
                        <span class="option-text">${interfaceName}</span>
                    </label>
                `;
                interfaceOptions.insertAdjacentHTML('beforeend', optionHtml);
            });

            // 重新绑定事件
            const newCheckboxes = interfaceOptions.querySelectorAll('input[type="checkbox"]');
            newCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateInterfaceSelection();
                    // 移除自动触发搜索，改为手动触发
                });
            });
        }

        // 更新接口列表下拉框
        function updateInterfaceList(data) {
            if (!data || !data.dataDiffList) {
                console.error('接口列表数据格式错误');
                return;
            }

            const select = document.getElementById('interfaceFilter');
            if (!select) {
                console.error('未找到接口筛选下拉框');
                return;
            }

            // 清空现有选项（保留第一个空选项）
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }

            // 从diff结果中提取唯一的接口名列表
            const handlerNames = [...new Set(data.dataDiffList.map(item => item.handlerName || item.handler_name))];

            // 添加从API获取的选项
            handlerNames.forEach(handler => {
                const option = document.createElement('option');
                option.value = handler;
                option.textContent = handler;
                select.appendChild(option);
            });

            console.log('接口列表更新完成，共', handlerNames.length, '个接口');
        }

        // 统一的数据加载函数（支持分页、搜索、排序）
        function loadDataWithPagination(options = {}) {
            return new Promise((resolve, reject) => {
                // 显示加载遮罩
                const loadingOverlay = document.getElementById('tableLoadingOverlay');
                const errorOverlay = document.getElementById('tableErrorOverlay');
                if (loadingOverlay && errorOverlay) {
                    loadingOverlay.style.display = 'flex';
                    errorOverlay.style.display = 'none';
                    
                    // 更新加载进度文本
                    const progressText = document.getElementById('tableLoadingProgress');
                    if (progressText && options.page && options.pageSize) {
                        progressText.textContent = `第 ${options.page} 页，每页 ${options.pageSize} 条`;
                    }
                }
                // 合并默认选项
                const defaultOptions = {
                    page: currentPagination.page,
                    pageSize: currentPagination.pageSize,
                    search: $('#tableSearch').val().trim(),
                    status: selectedStatuses || [], // 使用自定义多选组件的值
                    interfaceFilter: selectedInterfaces || [], // 使用自定义多选组件的值
                    hasDiff: selectedDiffType, // 差异类型筛选（空字符串表示全部，1表示有差异，0表示无差异）
                    sortBy: currentSort.field,
                    sortOrder: currentSort.order,
                    resetPage: false // 是否重置到第一页
                };

                const finalOptions = { ...defaultOptions, ...options };

                // 如果需要重置页码
                if (finalOptions.resetPage) {
                    currentPagination.page = 1;
                    finalOptions.page = 1;
                }

                // 构建查询参数
                const params = new URLSearchParams();

                // 时间范围参数
                if (currentTimeRange.startTime) {
                    params.append('startTime', currentTimeRange.startTime);
                }
                if (currentTimeRange.endTime) {
                    params.append('endTime', currentTimeRange.endTime);
                }

                // 分页参数
                params.append('page', finalOptions.page);
                params.append('pageSize', finalOptions.pageSize);

                // 搜索参数
                if (finalOptions.search) {
                    params.append('search', finalOptions.search);
                }

                // 状态过滤 - 多选值用逗号拼接
                if (finalOptions.status && finalOptions.status.length > 0) {
                    params.append('status', finalOptions.status.join(','));
                }

                // 接口过滤 - 多选值用逗号拼接
                if (finalOptions.interfaceFilter && finalOptions.interfaceFilter.length > 0) {
                    params.append('handlerNames', finalOptions.interfaceFilter.join(','));
                }

                // 差异类型过滤
                if (finalOptions.hasDiff !== '') {
                    params.append('hasDiff', finalOptions.hasDiff);
                }

                // 排序参数
                params.append('sortBy', finalOptions.sortBy);
                params.append('sortOrder', finalOptions.sortOrder);

                const url = '/fwyytool/tools/diff/res' + (params.toString() ? '?' + params.toString() : '');
                console.log('请求数据:', url, '参数:', finalOptions);

                fetch(url)
                    .then(response => {
                        console.log('差异数据响应状态:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('数据响应:', data);
                        // 隐藏加载遮罩
                        if (loadingOverlay) {
                            loadingOverlay.style.display = 'none';
                        }
                        
                        if (data.errNo === 0 && data.data) {
                            // 更新分页信息
                            currentPagination.page = finalOptions.page;
                            currentPagination.pageSize = finalOptions.pageSize;
                            currentPagination.total = data.data.total || 0;
                            currentPagination.totalPages = Math.ceil(currentPagination.total / currentPagination.pageSize);

                            // 更新表格和分页UI
                            updateDiffTable(data.data);
                            updatePaginationUI();
                            resolve(data.data);
                        } else {
                            console.error('数据API响应错误:', data.errMsg || '未知错误');
                            if (errorOverlay) {
                                errorOverlay.style.display = 'flex';
                            }
                            reject(new Error(data.errMsg || '加载数据失败'));
                        }
                    })
                    .catch(error => {
                        console.error('请求差异数据失败:', error);
                        // 隐藏加载遮罩，显示错误遮罩
                        if (loadingOverlay && errorOverlay) {
                            loadingOverlay.style.display = 'none';
                            errorOverlay.style.display = 'flex';
                        }
                        reject(error);
                    });
            });
        }

        // 原有的 loadDiffData 函数改为调用新的统一函数
        function loadDiffData() {
            return loadDataWithPagination();
        }

        // 更新分页UI
        function updatePaginationUI() {
            // 更新信息显示
            const start = (currentPagination.page - 1) * currentPagination.pageSize + 1;
            const end = Math.min(currentPagination.page * currentPagination.pageSize, currentPagination.total);

            $('#currentPageStart').text(currentPagination.total > 0 ? start : 0);
            $('#currentPageEnd').text(end);
            $('#totalRecords').text(currentPagination.total);

            // 更新每页条数选择器显示
            $('#currentPageSize').text(currentPagination.pageSize);
            $('.page-size-option').removeClass('active');
            $(`.page-size-option[data-size="${currentPagination.pageSize}"]`).addClass('active');

            // 生成分页按钮
            generatePaginationButtons();
        }

        // 生成分页按钮
        function generatePaginationButtons() {
            const pagination = $('#customPagination');
            pagination.empty();

            if (currentPagination.totalPages <= 1) {
                return; // 只有一页或没有数据时不显示分页
            }

            const currentPage = currentPagination.page;
            const totalPages = currentPagination.totalPages;

            // 上一页按钮
            const prevDisabled = currentPage <= 1;
            pagination.append(`
                <li class="page-item ${prevDisabled ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}" ${prevDisabled ? 'tabindex="-1"' : ''}>
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `);

            // 页码按钮逻辑
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, currentPage + 2);

            // 确保显示5个页码（如果可能）
            if (endPage - startPage < 4) {
                if (startPage === 1) {
                    endPage = Math.min(totalPages, startPage + 4);
                } else if (endPage === totalPages) {
                    startPage = Math.max(1, endPage - 4);
                }
            }

            // 第一页和省略号
            if (startPage > 1) {
                pagination.append(`<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`);
                if (startPage > 2) {
                    pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
                }
            }

            // 页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                pagination.append(`
                    <li class="page-item ${isActive ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `);
            }

            // 最后一页和省略号
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
                }
                pagination.append(`<li class="page-item"><a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a></li>`);
            }

            // 下一页按钮
            const nextDisabled = currentPage >= totalPages;
            pagination.append(`
                <li class="page-item ${nextDisabled ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}" ${nextDisabled ? 'tabindex="-1"' : ''}>
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `);
        }

        // 更新差异数据表格
        function updateDiffTable(data) {
            const tbody = document.getElementById('diffTableBody');

            if (!data.dataDiffList || data.dataDiffList.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-info-circle me-2"></i>
                                暂无数据
                            </div>
                        </td>
                    </tr>
                `;
                // 清空选择状态
                clearSelection();
                return;
            }

            let rows = '';
            data.dataDiffList.forEach(diffInfo => {
                const statusText = diffInfo.status === 0 ? '未完成' :
                                 diffInfo.status === 1 ? '已完成' :
                                 diffInfo.status === 2 ? '失败' : '未知';

                const diffTypeText = diffInfo.diffType === 0 ? '新老对比' : '环比对比';

                const paramsPreview = diffInfo.params ?
                    (diffInfo.params.length > 100 ? diffInfo.params.substring(0, 100) + '...' : diffInfo.params) :
                    '';

                const recordId = diffInfo.id || diffInfo.ID;
                const isSelected = selectedIds.includes(recordId);

                rows += `
                    <tr data-id="${recordId}" data-status="${diffInfo.status}" data-diff-type="${diffInfo.diffType}" class="${isSelected ? 'table-active' : ''}">
                        <td class="text-center">
                            <div class="form-check">
                                <input class="form-check-input row-checkbox" type="checkbox"
                                       data-id="${recordId}"
                                       ${isSelected ? 'checked' : ''}
                                       onchange="toggleSelectItem(${recordId})">
                            </div>
                        </td>
                        <td>
                            <div class="fw-bold text-primary" style="cursor: pointer;" onclick="showInterfaceConfig('${escapeHtml(diffInfo.handlerName || diffInfo.handler_name || '')}', '${recordId}')" title="点击查看接口配置">
                                ${diffInfo.handlerName || diffInfo.handler_name || ''}
                            </div>
                            <small class="text-muted font-monospace">${diffInfo.fingerprint || ''}</small>
                            <div class="mt-1">
                                <span class="badge bg-secondary">${diffTypeText}</span>
                            </div>
                        </td>
                        <td>
                            <div class="params-preview" onclick="showParamsModal('${recordId}', '${escapeHtml(diffInfo.params || '')}')" title="点击查看完整参数">
                                ${paramsPreview ? paramsPreview : '<span class="text-muted">无参数</span>'}
                            </div>
                        </td>
                        <td>
                            <span class="badge ${diffInfo.diffNum > 0 ? 'bg-danger' : 'bg-success'} fs-6">
                                ${diffInfo.diffNum || 0}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-outline-secondary btn-sm" onclick="showDataModal('${recordId}', 'old', '${escapeHtml(diffInfo.oldData || '')}')">
                                <i class="fas fa-eye me-1"></i>查看
                            </button>
                        </td>
                        <td>
                            <button class="btn btn-outline-secondary btn-sm" onclick="showDataModal('${recordId}', 'new', '${escapeHtml(diffInfo.newData || '')}')">
                                <i class="fas fa-eye me-1"></i>查看
                            </button>
                        </td>
                        <td>
                            <span class="status-badge status-${diffInfo.status}">
                                ${statusText}
                            </span>
                        </td>
                        <td>
                            <div class="text-nowrap">
                                ${formatTime(diffInfo.createTime)}
                            </div>
                        </td>
                        <td>
                            <div class="text-nowrap">
                                ${formatTime(diffInfo.updateTime)}
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewDiffDetail('${recordId}', '${escapeHtml(diffInfo.params || '')}', '${escapeHtml(diffInfo.oldData || '')}', '${escapeHtml(diffInfo.newData || '')}', '${diffInfo.diffResult || ''}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${diffInfo.diffResult ? `
                                <a href="${diffInfo.diffResult}" class="btn btn-outline-success btn-sm" target="_blank" title="查看Diff结果">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                ` : ''}
                                <button class="btn btn-outline-warning btn-sm" onclick="singleRerun(${recordId})" title="重新运行">
                                    <i class="fas fa-redo"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = rows;
            console.log('差异数据表格更新完成，共', data.dataDiffList.length, '条记录');

            // 更新批量操作工具栏
            updateBatchToolbar();

            // 数据加载完成后初始化DataTables
            initializeDataTables();
        }

        // 显示差异表格加载错误
        function showDiffTableError() {
            const tbody = document.getElementById('diffTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center py-5">
                        <div class="text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            数据加载失败，请刷新页面重试
                        </div>
                    </td>
                </tr>
            `;
            // 清空选择状态
            clearSelection();
        }

        // HTML转义函数
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML.replace(/'/g, '&#39;').replace(/"/g, '&quot;');
        }

        // 时间格式化函数
        function formatTime(timestamp) {
            if (!timestamp) return '-';
            return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss');
        }

        // ==================== 多选功能相关函数 ====================

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const rowCheckboxes = document.querySelectorAll('.row-checkbox');
            const isChecked = selectAllCheckbox.checked;

            if (isChecked) {
                // 全选：将当前页面所有记录ID添加到选中列表
                rowCheckboxes.forEach(checkbox => {
                    const id = parseInt(checkbox.dataset.id);
                    if (!selectedIds.includes(id)) {
                        selectedIds.push(id);
                    }
                    checkbox.checked = true;
                    // 添加选中行样式
                    const row = checkbox.closest('tr');
                    if (row) row.classList.add('table-active');
                });
            } else {
                // 取消全选：移除当前页面所有记录ID
                rowCheckboxes.forEach(checkbox => {
                    const id = parseInt(checkbox.dataset.id);
                    const index = selectedIds.indexOf(id);
                    if (index > -1) {
                        selectedIds.splice(index, 1);
                    }
                    checkbox.checked = false;
                    // 移除选中行样式
                    const row = checkbox.closest('tr');
                    if (row) row.classList.remove('table-active');
                });
            }

            updateBatchToolbar();
        }

        // 单项选择/取消选择
        function toggleSelectItem(id) {
            const checkbox = document.querySelector(`.row-checkbox[data-id="${id}"]`);
            const row = checkbox.closest('tr');

            if (checkbox.checked) {
                // 选中：添加到选中列表
                if (!selectedIds.includes(id)) {
                    selectedIds.push(id);
                }
                if (row) row.classList.add('table-active');
            } else {
                // 取消选中：从选中列表移除
                const index = selectedIds.indexOf(id);
                if (index > -1) {
                    selectedIds.splice(index, 1);
                }
                if (row) row.classList.remove('table-active');
            }

            // 更新全选复选框状态
            updateSelectAllCheckbox();
            updateBatchToolbar();
        }

        // 更新全选复选框状态
        function updateSelectAllCheckbox() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const rowCheckboxes = document.querySelectorAll('.row-checkbox');
            const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;

            if (checkedCount === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCount === rowCheckboxes.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        // 更新批量操作工具栏
        function updateBatchToolbar() {
            const toolbar = document.getElementById('batchToolbar');
            const countElement = document.getElementById('selectedCount');

            if (selectedIds.length > 0) {
                toolbar.style.display = 'block';
                countElement.textContent = selectedIds.length;
            } else {
                toolbar.style.display = 'none';
            }
        }

        // 清空选择
        function clearSelection() {
            selectedIds = [];

            // 取消所有复选框选中状态
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                checkbox.checked = false;
                const row = checkbox.closest('tr');
                if (row) row.classList.remove('table-active');
            });

            // 更新全选复选框
            const selectAllCheckbox = document.getElementById('selectAll');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }

            updateBatchToolbar();
        }

        // 获取选中的记录ID数组
        function getSelectedIds() {
            return [...selectedIds];
        }

        // 批量导出功能
        function batchExport() {
            if (selectedIds.length === 0) {
                alert('请先选择要导出的记录');
                return;
            }

            // 构建导出参数 - 现在只需要传ids
            const params = new URLSearchParams();

            // 添加选中的ID数组
            selectedIds.forEach(id => {
                params.append('ids', id);
            });

            // 构建导出URL
            const exportUrl = '/fwyytool/tools/diff/export?' + params.toString();

            // 创建下载链接
            const link = document.createElement('a');
            link.href = exportUrl;
            link.download = `diff_analysis_${moment().format('YYYY-MM-DD_HH-mm-ss')}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('批量导出选中记录:', selectedIds);
            
            // 显示导出提示
            showExportToast();
        }

        // 显示导出成功提示
        function showExportToast() {
            const toast = document.createElement('div');
            toast.className = 'toast-container position-fixed top-0 end-0 p-3';
            toast.innerHTML = `
                <div class="toast show" role="alert">
                    <div class="toast-header">
                        <i class="fas fa-download text-success me-2"></i>
                        <strong class="me-auto">导出成功</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        已导出 ${selectedIds.length} 条差异数据记录。
                        <br>
                        <small class="text-muted">
                            数据格式已优化，适合AI分析使用
                        </small>
                    </div>
                </div>
            `;
            document.body.appendChild(toast);
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 5000);
        }

        // ==================== 自定义多选组件功能 ====================

        // 初始化自定义多选组件
        function initCustomMultiselect() {
            // 初始化状态多选
            initStatusMultiselect();

            // 初始化接口多选
            initInterfaceMultiselect();

            // 初始化差异类型单选
            initDiffTypeMultiselect();

            // 绑定点击外部关闭下拉框
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.custom-multiselect')) {
                    closeAllDropdowns();
                }
            });
        }

        // 初始化状态多选组件
        function initStatusMultiselect() {
            const trigger = document.querySelector('#statusMultiselect .multiselect-trigger');
            const dropdown = document.querySelector('#statusMultiselect .multiselect-dropdown');
            const options = document.querySelectorAll('#statusOptions input[type="checkbox"]');

            // 绑定触发器点击事件
            trigger.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleDropdown('statusMultiselect');
            });

            // 绑定选项变化事件
            options.forEach(option => {
                option.addEventListener('change', function() {
                    updateStatusSelection();
                    // 移除自动触发搜索，改为手动触发
                });
            });
        }

        // 初始化接口多选组件
        function initInterfaceMultiselect() {
            const trigger = document.querySelector('#interfaceMultiselect .multiselect-trigger');
            const dropdown = document.querySelector('#interfaceMultiselect .multiselect-dropdown');
            const searchInput = document.querySelector('#interfaceMultiselect .multiselect-search input');

            // 绑定触发器点击事件
            trigger.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleDropdown('interfaceMultiselect');
            });

            // 绑定搜索功能
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    filterInterfaceOptions(this.value);
                });
            }
        }

        // 初始化差异类型单选组件
        function initDiffTypeMultiselect() {
            const trigger = document.querySelector('#diffTypeMultiselect .multiselect-trigger');
            const dropdown = document.querySelector('#diffTypeMultiselect .multiselect-dropdown');
            const options = document.querySelectorAll('#diffTypeOptions input[type="radio"]');

            // 绑定触发器点击事件
            trigger.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleDropdown('diffTypeMultiselect');
            });

            // 绑定选项变化事件
            options.forEach(option => {
                option.addEventListener('change', function(event) {
                    updateDiffTypeSelection(event);
                });
            });

            // 初始化显示文本
            updateDiffTypeLabel();
        }



        // 切换下拉框显示/隐藏
        function toggleDropdown(multiselectId) {
            const multiselect = document.getElementById(multiselectId);
            const trigger = multiselect.querySelector('.multiselect-trigger');
            const dropdown = multiselect.querySelector('.multiselect-dropdown');

            // 关闭其他下拉框
            closeAllDropdowns(multiselectId);

            // 切换当前下拉框
            const isActive = trigger.classList.contains('active');
            if (isActive) {
                trigger.classList.remove('active');
                dropdown.classList.remove('show');
            } else {
                trigger.classList.add('active');
                dropdown.classList.add('show');
            }
        }

        // 关闭所有下拉框
        function closeAllDropdowns(except = null) {
            const multiselectIds = ['statusMultiselect', 'interfaceMultiselect', 'diffTypeMultiselect'];
            multiselectIds.forEach(id => {
                if (id !== except) {
                    const multiselect = document.getElementById(id);
                    if (multiselect) {
                        const trigger = multiselect.querySelector('.multiselect-trigger');
                        const dropdown = multiselect.querySelector('.multiselect-dropdown');
                        trigger.classList.remove('active');
                        dropdown.classList.remove('show');
                    }
                }
            });
        }

        // 更新状态选择
        function updateStatusSelection() {
            const options = document.querySelectorAll('#statusOptions input[type="checkbox"]:checked');
            selectedStatuses = Array.from(options).map(option => option.value);

            const label = document.querySelector('#statusMultiselect .multiselect-label');
            if (selectedStatuses.length === 0) {
                label.textContent = '选择状态';
                label.classList.remove('has-selection');
            } else if (selectedStatuses.length === 1) {
                const selectedOption = document.querySelector(`#statusOptions input[value="${selectedStatuses[0]}"]`);
                label.innerHTML = selectedOption.dataset.label;
                label.classList.add('has-selection');
            } else {
                label.innerHTML = `已选择 <span class="selection-count">${selectedStatuses.length}</span> 项`;
                label.classList.add('has-selection');
            }
        }

        // 更新接口选择
        function updateInterfaceSelection() {
            const options = document.querySelectorAll('#interfaceOptions input[type="checkbox"]:checked');
            selectedInterfaces = Array.from(options).map(option => option.value);

            const label = document.querySelector('#interfaceMultiselect .multiselect-label');
            if (selectedInterfaces.length === 0) {
                label.textContent = '选择接口';
                label.classList.remove('has-selection');
            } else if (selectedInterfaces.length === 1) {
                label.textContent = selectedInterfaces[0];
                label.classList.add('has-selection');
            } else {
                label.innerHTML = `已选择 <span class="selection-count">${selectedInterfaces.length}</span> 项`;
                label.classList.add('has-selection');
            }
        }



        // 更新差异数量显示
        function updateDiffCountDisplay() {
            // 获取当前差异数量
            fetch('/fwyytool/tools/diff/count')
                .then(response => response.json())
                .then(data => {
                    if (data.errNo === 0 && data.data) {
                        // 更新标签中的差异数量
                        const diffTypeLabel = document.querySelector('#diffTypeMultiselect .multiselect-label');
                        if (diffTypeLabel && diffTypeLabel.classList.contains('has-selection')) {
                            const countSpan = diffTypeLabel.querySelector('.selection-count');
                            if (countSpan) {
                                // 根据当前选择的差异类型显示对应数量
                                let count = 0;
                                if (selectedDiffType === '1') {
                                    // 有差异的数量
                                    count = Object.values(data.data).reduce((sum, item) => sum + (item.hasDiffCnt || 0), 0);
                                } else if (selectedDiffType === '0') {
                                    // 无差异的数量
                                    count = Object.values(data.data).reduce((sum, item) => sum + (item.noDiffCnt || 0), 0);
                                }
                                countSpan.textContent = count;
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('获取差异数量失败:', error);
                });
        }

        // 应用差异类型筛选
        function applyDiffTypeFilter() {
            // 关闭下拉框
            closeAllDropdowns();
            
            // 更新差异数量显示
            updateDiffCountDisplay();
            
            // 触发搜索
            applyAdvancedSearch();
        }

        // 更新差异类型选择
        function updateDiffTypeSelection(event) {
            const selectedOption = event.target;
            selectedDiffType = selectedOption.value;

            updateDiffTypeLabel();
            // 不自动触发搜索，需要手动点击搜索按钮
        }

        // 更新差异类型标签显示
        function updateDiffTypeLabel() {
            const selectedOption = document.querySelector('#diffTypeOptions input[type="radio"]:checked');
            const label = document.querySelector('#diffTypeMultiselect .multiselect-label');

            if (!selectedOption) {
                label.textContent = '差异类型';
                label.classList.remove('has-selection');
            } else {
                if (selectedOption.value === '') {
                    label.textContent = '差异类型';
                    label.classList.remove('has-selection');
                } else {
                    const diffLabels = {
                        '1': '有差异',
                        '0': '无差异'
                    };
                    label.textContent = diffLabels[selectedOption.value];
                    label.classList.add('has-selection');
                }
            }
        }

        // 过滤接口选项
        function filterInterfaceOptions(searchTerm) {
            const options = document.querySelectorAll('#interfaceOptions .multiselect-option');
            const term = searchTerm.toLowerCase();

            options.forEach(option => {
                const text = option.querySelector('.option-text').textContent.toLowerCase();
                if (text.includes(term)) {
                    option.style.display = 'flex';
                } else {
                    option.style.display = 'none';
                }
            });
        }

        // 清空自定义多选组件选择
        function clearCustomMultiselect() {
            // 清空状态选择
            selectedStatuses = [];
            const statusCheckboxes = document.querySelectorAll('#statusOptions input[type="checkbox"]');
            statusCheckboxes.forEach(checkbox => checkbox.checked = false);
            updateStatusSelection();

            // 清空接口选择
            selectedInterfaces = [];
            const interfaceCheckboxes = document.querySelectorAll('#interfaceOptions input[type="checkbox"]');
            interfaceCheckboxes.forEach(checkbox => checkbox.checked = false);
            updateInterfaceSelection();

            // 清空差异类型选择
            selectedDiffType = '';
            const diffTypeRadios = document.querySelectorAll('#diffTypeOptions input[type="radio"]');
            diffTypeRadios.forEach(radio => {
                radio.checked = radio.value === '';
            });
            updateDiffTypeLabel();

            // 关闭所有下拉框
            closeAllDropdowns();
        }

        // 重新加载所有数据
        function reloadAllData() {
            console.log('重新加载所有数据...');

            // 重置加载状态
            pageDataLoaded = false;
            statsOverviewLoaded = false;

            const statsContainer = document.getElementById('statsOverviewData');
            if (statsContainer.classList.contains('show')) {
                statsContainer.classList.remove('show');
                document.getElementById('statsToggleText').textContent = '展开';
                document.getElementById('statsToggleIcon').className = 'fas fa-chevron-down me-1';
            }

            // 重新加载页面数据
            loadPageData();
        }
        
        // 绑定筛选事件
        function bindFilterEvents() {
            // 接口筛选
            $('#interfaceFilter').on('change', function() {
                applyAdvancedSearch();
            });

            // 状态筛选
            $('#statusFilter').on('change', function() {
                applyAdvancedSearch();
            });

            // 搜索按钮点击事件
            $('#searchButton').on('click', function() {
                applyAdvancedSearch();
            });

            // 搜索框回车事件
            $('#tableSearch').on('keypress', function(e) {
                if (e.which === 13) { // Enter键
                    applyAdvancedSearch();
                }
            });

            // 清除搜索按钮
            $('#clearSearch').on('click', function() {
                $('#tableSearch').val('');

                // 清空自定义多选组件
                clearCustomMultiselect();

                // 重置排序为默认状态
                currentSort = { field: 'updateTime', order: 'desc' };
                updateSortIcons();

                applyAdvancedSearch();
            });

            // 每页条数选择 - 新的下拉选择器
            $('.page-size-option').on('click', function(e) {
                e.preventDefault();
                const newPageSize = parseInt($(this).data('size'));

                // 更新UI显示
                $('#currentPageSize').text(newPageSize);
                $('.page-size-option').removeClass('active');
                $(this).addClass('active');

                // 更新分页状态并重新加载数据
                currentPagination.pageSize = newPageSize;
                currentPagination.page = 1; // 重置到第一页
                loadDataWithPagination({ pageSize: newPageSize, page: 1 });
            });

            // 分页按钮点击事件（使用事件委托）
            $('#customPagination').on('click', 'a.page-link', function(e) {
                e.preventDefault();
                const targetPage = parseInt($(this).data('page'));
                if (targetPage && targetPage !== currentPagination.page && targetPage > 0 && targetPage <= currentPagination.totalPages) {
                    loadDataWithPagination({ page: targetPage });
                }
            });
        }

        // 绑定排序事件
        function bindSortEvents() {
            $('.sortable').on('click', function() {
                const sortField = $(this).data('sort');

                // 切换排序方向
                if (currentSort.field === sortField) {
                    currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSort.field = sortField;
                    currentSort.order = 'desc'; // 默认降序
                }

                console.log('排序变更:', currentSort);

                // 更新排序图标
                updateSortIcons();

                // 使用后端排序重新加载数据
                loadDataWithPagination({
                    sortBy: currentSort.field,
                    sortOrder: currentSort.order
                });
            });
        }

        // 更新排序图标
        function updateSortIcons() {
            // 清除所有排序状态
            $('.sortable').removeClass('sort-asc sort-desc');

            // 设置当前排序状态
            const currentHeader = $(`.sortable[data-sort="${currentSort.field}"]`);
            currentHeader.addClass(currentSort.order === 'asc' ? 'sort-asc' : 'sort-desc');
        }

        // 应用高级搜索和过滤
        function applyAdvancedSearch() {
            const searchTerm = $('#tableSearch').val().trim();

            // 使用自定义多选组件的值
            const statusFilter = selectedStatuses || [];
            const interfaceFilter = selectedInterfaces || [];
            const diffTypeValue = selectedDiffType || '';
            const diffTypeFilter = diffTypeValue !== '';

            // 更新搜索状态指示器
            const hasUserSearchConditions = searchTerm || statusFilter.length > 0 || interfaceFilter.length > 0 || diffTypeFilter;
            const searchStatus = $('#searchStatus');
            if (hasUserSearchConditions) {
                searchStatus.show();
            } else {
                searchStatus.hide();
            }

            // 更新筛选条件汇总
            updateFilterSummaryBar({
                searchTerm,
                statusFilter,
                interfaceFilter,
                diffTypeFilter,
                diffTypeValue
            });

            // 重置到第一页并加载数据
            loadDataWithPagination({
                page: 1,
                resetPage: true,
                search: searchTerm,
                status: statusFilter,
                interfaceFilter: interfaceFilter,
                hasDiff: diffTypeValue
            });
        }

        // 更新筛选条件汇总栏
        function updateFilterSummaryBar(filters) {
            const filterSummaryBar = document.getElementById('filterSummaryBar');
            const filterSummaryContent = document.getElementById('filterSummaryContent');
            
            if (!filterSummaryBar || !filterSummaryContent) {
                return;
            }
            
            // 清空现有内容
            filterSummaryContent.innerHTML = '';
            
            // 构建筛选条件标签
            const filterTags = [];
            
            // 时间范围
            if (currentTimeRange.startTime && currentTimeRange.endTime) {
                const startMoment = moment.unix(currentTimeRange.startTime);
                const endMoment = moment.unix(currentTimeRange.endTime);
                
                if (currentTimeRange.preset === 'custom') {
                    filterTags.push({
                        icon: 'fa-clock',
                        label: '时间范围',
                        value: `${startMoment.format('MM-DD HH:mm')} - ${endMoment.format('MM-DD HH:mm')}`
                    });
                } else {
                    const presetLabels = {
                        '24h': '最近24小时',
                        '7d': '最近7天',
                        '30d': '最近30天'
                    };
                    filterTags.push({
                        icon: 'fa-clock',
                        label: '时间范围',
                        value: presetLabels[currentTimeRange.preset] || currentTimeRange.preset
                    });
                }
            }
            
            // 关键词搜索
            if (filters.searchTerm) {
                filterTags.push({
                    icon: 'fa-search',
                    label: '关键词',
                    value: filters.searchTerm
                });
            }
            
            // 状态筛选
            if (filters.statusFilter && filters.statusFilter.length > 0) {
                const statusLabels = {
                    '0': '未完成',
                    '1': '已完成',
                    '2': '失败'
                };
                const statusTexts = filters.statusFilter.map(status => statusLabels[status]).join('、');
                filterTags.push({
                    icon: 'fa-tasks',
                    label: '状态',
                    value: statusTexts
                });
            }
            
            // 接口筛选
            if (filters.interfaceFilter && filters.interfaceFilter.length > 0) {
                if (filters.interfaceFilter.length === 1) {
                    filterTags.push({
                        icon: 'fa-code-branch',
                        label: '接口',
                        value: filters.interfaceFilter[0]
                    });
                } else {
                    filterTags.push({
                        icon: 'fa-code-branch',
                        label: '接口',
                        value: `${filters.interfaceFilter.length}个`
                    });
                }
            }
            
            // 差异类型筛选
            if (filters.diffTypeFilter && filters.diffTypeValue) {
                const diffTypeLabels = {
                    '1': '有差异',
                    '0': '无差异'
                };
                const diffTypeText = diffTypeLabels[filters.diffTypeValue];
                if (diffTypeText) {
                    filterTags.push({
                        icon: 'fa-code-compare',
                        label: '差异类型',
                        value: diffTypeText
                    });
                }
            }
            
            // 生成标签HTML
            if (filterTags.length > 0) {
                filterTags.forEach(tag => {
                    const tagHtml = `
                        <div class="filter-tag">
                            <i class="fas ${tag.icon}"></i>
                            <span class="filter-label">${tag.label}:</span>
                            <span class="filter-value">${tag.value}</span>
                        </div>
                    `;
                    filterSummaryContent.insertAdjacentHTML('beforeend', tagHtml);
                });
                
                // 显示汇总栏
                filterSummaryBar.style.display = 'block';
            } else {
                // 隐藏汇总栏
                filterSummaryBar.style.display = 'none';
            }
        }

        // 清空所有筛选条件
        function clearAllFilters() {
            // 清空搜索框
            $('#tableSearch').val('');

            // 清空自定义多选组件
            clearCustomMultiselect();

            // 重置排序为默认状态
            currentSort = { field: 'updateTime', order: 'desc' };
            updateSortIcons();

            // 应用搜索（会清空所有条件）
            applyAdvancedSearch();
        }

        // 移除旧的搜索结果更新函数，现在直接通过 updateDiffTable 更新表格

        // 切换统计概览
        function toggleStatsOverview() {
            const container = document.getElementById('statsOverviewData');
            const toggleText = document.getElementById('statsToggleText');
            const toggleIcon = document.getElementById('statsToggleIcon');
            
            if (container.classList.contains('show')) {
                container.classList.remove('show');
                toggleText.textContent = '展开';
                toggleIcon.className = 'fas fa-chevron-down me-1';
                return;
            }
            
            if (statsOverviewLoaded) {
                container.classList.add('show');
                toggleText.textContent = '收起';
                toggleIcon.className = 'fas fa-chevron-up me-1';
                return;
            }
            
            // 构建请求参数（支持时间范围）
            const params = new URLSearchParams();
            if (currentTimeRange.startTime && currentTimeRange.endTime) {
                params.append('startTime', currentTimeRange.startTime);
                params.append('endTime', currentTimeRange.endTime);
            }
            
            // 加载数据
            const url = '/fwyytool/tools/diff/count' + (params.toString() ? '?' + params.toString() : '');
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.errNo === 0 && data.data) {
                        const statsData = data.data;
                        const sortableData = Object.entries(statsData).map(([key, value]) => ({
                            name: key,
                            ...value
                        })).sort((a, b) => b.hasDiffCnt - a.hasDiffCnt);
                        
                        let tableHtml = `
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-success">
                                        <tr>
                                            <th>接口名称</th>
                                            <th class="text-center">有差异</th>
                                            <th class="text-center">无差异</th>
                                            <th class="text-center">未完成</th>
                                            <th class="text-center">失败</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;
                        
                        sortableData.forEach(item => {
                            const hasDiffClass = item.hasDiffCnt > 0 ? 'text-danger fw-bold' : '';
                            tableHtml += `
                                <tr>
                                    <td class="font-monospace">${item.name}</td>
                                    <td class="text-center ${hasDiffClass}">${item.hasDiffCnt}</td>
                                    <td class="text-center">${item.noDiffCnt}</td>
                                    <td class="text-center">${item.unFinishCnt}</td>
                                    <td class="text-center">${item.failedCnt || item.failedCun || 0}</td>
                                </tr>
                            `;
                        });
                        
                        tableHtml += '</tbody></table></div>';
                        container.innerHTML = tableHtml;
                    } else {
                        container.innerHTML = '<div class="alert alert-warning">数据加载失败</div>';
                    }
                    
                    container.classList.add('show');
                    toggleText.textContent = '收起';
                    toggleIcon.className = 'fas fa-chevron-up me-1';
                    statsOverviewLoaded = true;
                })
                .catch(error => {
                    container.innerHTML = `<div class="alert alert-danger">获取数据失败: ${error.message}</div>`;
                    container.classList.add('show');
                    toggleText.textContent = '收起';
                    toggleIcon.className = 'fas fa-chevron-up me-1';
                });
        }
        
        // 显示参数模态框
        function showParamsModal(id, params) {
            try {
                const formatted = JSON.stringify(JSON.parse(params), null, 2);
                document.getElementById('dataModalContent').textContent = formatted;
                document.getElementById('dataModalLabel').innerHTML = '<i class="fas fa-cog me-2"></i>请求参数 - ID: ' + id;
                currentModalContent = formatted;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            } catch (e) {
                document.getElementById('dataModalContent').textContent = params;
                document.getElementById('dataModalLabel').innerHTML = '<i class="fas fa-cog me-2"></i>请求参数 - ID: ' + id;
                currentModalContent = params;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            }
        }
        
        // 显示数据模态框
        function showDataModal(id, type, data) {
            try {
                const formatted = JSON.stringify(JSON.parse(data), null, 2);
                document.getElementById('dataModalContent').textContent = formatted;
                const title = type === 'old' ? '旧数据' : '新数据';
                document.getElementById('dataModalLabel').innerHTML = `<i class="fas fa-database me-2"></i>${title} - ID: ${id}`;
                currentModalContent = formatted;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            } catch (e) {
                document.getElementById('dataModalContent').textContent = data;
                const title = type === 'old' ? '旧数据' : '新数据';
                document.getElementById('dataModalLabel').innerHTML = `<i class="fas fa-database me-2"></i>${title} - ID: ${id}`;
                currentModalContent = data;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            }
        }
        
        // 显示Diff详情
        function viewDiffDetail(id, params, oldData, newData, diffResult) {
            try {
                // 格式化JSON
                const formatJSON = (str) => {
                    try {
                        return JSON.stringify(JSON.parse(str), null, 2);
                    } catch {
                        return str;
                    }
                };
                
                document.getElementById('diffParams').textContent = formatJSON(params);
                document.getElementById('diffOldData').textContent = formatJSON(oldData);
                document.getElementById('diffNewData').textContent = formatJSON(newData);
                
                const linkBtn = document.getElementById('diffResultLink');
                if (diffResult) {
                    linkBtn.href = diffResult;
                    linkBtn.style.display = 'inline-block';
                } else {
                    linkBtn.style.display = 'none';
                }
                
                new bootstrap.Modal(document.getElementById('diffDetailModal')).show();
            } catch (e) {
                console.error('显示Diff详情错误:', e);
            }
        }
        
        // 复制到剪贴板
        function copyToClipboard() {
            navigator.clipboard.writeText(currentModalContent).then(() => {
                // 显示成功提示
                const toast = document.createElement('div');
                toast.className = 'toast-container position-fixed top-0 end-0 p-3';
                toast.innerHTML = `
                    <div class="toast show" role="alert">
                        <div class="toast-header">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong class="me-auto">成功</strong>
                            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                        </div>
                        <div class="toast-body">内容已复制到剪贴板</div>
                    </div>
                `;
                document.body.appendChild(toast);
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 3000);
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }

        // 备用初始化机制 - 确保页面一定会加载数据
        window.addEventListener('load', function() {
            console.log('=== Window Load 事件触发 ===');
            setTimeout(() => {
                if (!pageDataLoaded) {
                    console.log('检测到页面数据未加载，强制初始化...');

                    // 强制设置时间范围
                    if (!currentTimeRange.startTime || !currentTimeRange.endTime) {
                        const urlParams = new URLSearchParams(window.location.search);
                        const startTime = urlParams.get('startTime');
                        const endTime = urlParams.get('endTime');

                        if (startTime && endTime) {
                            currentTimeRange = {
                                preset: 'custom',
                                startTime: parseInt(startTime),
                                endTime: parseInt(endTime)
                            };
                            console.log('从URL强制设置时间范围:', currentTimeRange);
                        } else {
                            // 设置默认24小时
                            const now = moment();
                            const start = moment().subtract(24, 'hours');
                            currentTimeRange = {
                                preset: '24h',
                                startTime: start.unix(),
                                endTime: now.unix()
                            };
                            console.log('强制设置默认时间范围:', currentTimeRange);
                        }
                    }

                    // 强制调用API
                    console.log('强制调用API...');
                    loadOverviewData().catch(e => console.error('强制概览数据加载失败:', e));
                    loadDiffData().catch(e => console.error('强制差异数据加载失败:', e));
                    loadInterfaceList().catch(e => console.error('强制接口列表加载失败:', e));
                }
            }, 500);
        });

        // 加载Diff控制配置
        function loadDiffControlConfig() {
            // 如果没有event对象，使用按钮元素
            const btn = event && event.target ? event.target : document.querySelector('[onclick*="loadDiffControlConfig"]');
            const originalHtml = btn ? btn.innerHTML : '';
            
            if (btn) {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>加载中...';
                btn.disabled = true;
            }
            
            fetch('/fwyytool/tools/diff/getconfig')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.errNo === 0) {
                        showConfigModal(data.data);
                    } else {
                        alert('加载配置失败: ' + (data.errMsg || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('请求配置失败:', error);
                    alert('加载配置失败，请重试');
                })
                .finally(() => {
                    if (btn) {
                        btn.innerHTML = originalHtml;
                        btn.disabled = false;
                    }
                });
        }

        // 显示单个接口配置
        function showInterfaceConfig(handlerName, recordId) {
            console.log('查看接口配置:', handlerName, '记录ID:', recordId);
            
            // 先加载完整配置，然后显示对应接口的配置
            const btn = document.querySelector('[onclick*="loadDiffControlConfig"]');
            const originalHtml = btn ? btn.innerHTML : '';
            
            if (btn) {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>加载中...';
                btn.disabled = true;
            }
            
            fetch('/fwyytool/tools/diff/getconfig')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.errNo === 0) {
                        // 检查该接口的配置是否存在
                        if (data.data && data.data[handlerName]) {
                            // 创建只包含该接口配置的对象
                            const singleConfig = {};
                            singleConfig[handlerName] = data.data[handlerName];
                            
                            // 显示配置模态框，并传递接口名称以便高亮，同时保存完整配置
                            showConfigModal(singleConfig, handlerName, data.data);
                        } else {
                            alert('接口 "' + handlerName + '" 没有找到配置信息');
                        }
                    } else {
                        alert('加载配置失败: ' + (data.errMsg || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('请求配置失败:', error);
                    alert('加载配置失败，请重试');
                })
                .finally(() => {
                    if (btn) {
                        btn.innerHTML = originalHtml;
                        btn.disabled = false;
                    }
                });
        }

        // 显示配置模态框（支持查看和编辑）
        function showConfigModal(configData, highlightHandler = null, fullConfig = null) {
            // 配置数据已经是map[string]map[string]any格式
            const handlerNames = Object.keys(configData);
            
            // 创建模态框内容
            const modalId = 'configModal_' + Date.now();
            const modalHtml = `
                <div class="modal fade" id="${modalId}" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header border-bottom">
                                <div class="d-flex align-items-center flex-grow-1">
                                    <h5 class="modal-title mb-0 me-4">
                                        <i class="fas fa-cog me-2 text-primary"></i>
                                        ${highlightHandler ? `接口配置: ${highlightHandler}` : 'Diff控制配置管理'}
                                    </h5>
                                    
                                    <!-- 控制面板 -->
                                    <div class="d-flex align-items-center gap-3 ms-auto me-3">
                                        <!-- 视图切换组 -->
                                        <div class="d-flex align-items-center gap-2 px-3 py-1 bg-light rounded-pill">
                                            <small class="text-muted fw-medium">视图模式:</small>
                                            <div class="form-check form-switch mb-0">
                                                <input class="form-check-input" type="checkbox" id="viewMode_${modalId}" onchange="toggleConfigView('${modalId}')">
                                                <label class="form-check-label small" for="viewMode_${modalId}">
                                                    JSON
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <!-- 操作按钮组 -->
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="editConfig('${modalId}')" id="editBtn_${modalId}" data-bs-toggle="tooltip" title="编辑配置">
                                                <i class="fas fa-edit me-1"></i>编辑
                                            </button>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="copyToClipboardFromModal('${modalId}')" data-bs-toggle="tooltip" title="复制配置">
                                                <i class="fas fa-copy me-1"></i>复制
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                            </div>
                            <div class="modal-body">
                                <!-- 搜索框 -->
                                <div class="mb-3">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" placeholder="搜索Handler名称..." id="searchHandler_${modalId}" onkeyup="filterHandlers('${modalId}')">
                                    </div>
                                </div>
                                
                                <!-- 表格视图 -->
                                <div id="tableView_${modalId}">
                                    <div class="accordion" id="handlerAccordion_${modalId}">
                                        ${handlerNames.map(handlerName => `
                                            <div class="accordion-item ${highlightHandler === handlerName ? 'border-primary border-2' : ''}" data-handler-name="${handlerName.toLowerCase()}">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button ${handlerNames.length === 1 || highlightHandler === handlerName ? '' : 'collapsed'}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${handlerName.replace(/[^a-zA-Z0-9]/g, '_')}_${modalId}">
                                                        <strong>${handlerName}</strong>
                                                        <span class="badge bg-${configData[handlerName].disable ? 'danger' : 'success'} ms-2">
                                                            ${configData[handlerName].disable ? '已禁用' : '已启用'}
                                                        </span>
                                                        ${highlightHandler === handlerName ? '<span class="badge bg-primary ms-2">当前查看</span>' : ''}
                                                    </button>
                                                </h2>
                                                <div id="collapse${handlerName.replace(/[^a-zA-Z0-9]/g, '_')}_${modalId}" class="accordion-collapse collapse ${handlerNames.length === 1 || highlightHandler === handlerName ? 'show' : ''}" data-bs-parent="#handlerAccordion_${modalId}">
                                                    <div class="accordion-body">
                                                        <div class="table-responsive">
                                                            <table class="table table-sm table-striped">
                                                                <tbody>
                                                                    ${generateConfigRows(configData[handlerName], handlerName, modalId)}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                                
                                <!-- JSON视图 -->
                                <div id="jsonView_${modalId}" style="display: none;">
                                    <pre class="json-viewer">${JSON.stringify(configData, null, 2)}</pre>
                                </div>
                                
                                <!-- 编辑视图 -->
                                <div id="editView_${modalId}" style="display: none;">
                                    <textarea id="configEditor_${modalId}" class="form-control font-monospace" rows="20" style="font-size: 14px;">${JSON.stringify(fullConfig || configData, null, 2)}</textarea>
                                </div>
                                
                                <div id="configError_${modalId}" class="alert alert-danger mt-3" style="display: none;"></div>
                            </div>
                            <div class="modal-footer">
                                <div id="viewFooter_${modalId}">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                </div>
                                <div id="editFooter_${modalId}" style="display: none;">
                                    <button type="button" class="btn btn-secondary" onclick="cancelEdit('${modalId}')">取消</button>
                                    <button type="button" class="btn btn-primary" onclick="saveConfig('${modalId}')">
                                        <i class="fas fa-save me-1"></i>保存
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById(modalId));
            modal.show();
            
            // 保存当前配置内容和完整配置（如果有）
            window.currentConfigData = configData;
            window.currentFullConfig = fullConfig || configData;
            
            // 如果有高亮的接口，延迟滚动到该接口
            if (highlightHandler) {
                setTimeout(() => {
                    const targetElement = document.querySelector(`#collapse${highlightHandler.replace(/[^a-zA-Z0-9]/g, '_')}_${modalId}`);
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        
                        // 添加高亮动画效果
                        const accordionItem = targetElement.closest('.accordion-item');
                        if (accordionItem) {
                            accordionItem.style.transition = 'all 0.3s ease';
                            setTimeout(() => {
                                accordionItem.style.boxShadow = '0 0 20px rgba(0, 123, 255, 0.3)';
                            }, 100);
                            setTimeout(() => {
                                accordionItem.style.boxShadow = '';
                            }, 2000);
                        }
                    }
                }, 300);
            }
            
            // 模态框关闭后移除DOM
            document.getElementById(modalId).addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }

        // 生成配置行HTML
        function generateConfigRows(config, handlerName, modalId) {
            // 基础字段配置
            const basicFields = [
                { key: 'disable', label: '启用状态', type: 'boolean', tooltip: '是否禁用该配置' },
                { key: 'startTime', label: '开始时间', type: 'date', tooltip: '配置生效开始时间(YYYYMMDD)' },
                { key: 'endTime', label: '结束时间', type: 'date', tooltip: '配置生效结束时间(YYYYMMDD)' },
                { key: 'oneDayStartTime', label: '每日开始时间', type: 'hour', tooltip: '每天收集参数的开始时间（小时）' },
                { key: 'oneDayEndTime', label: '每日结束时间', type: 'hour', tooltip: '每天收集参数的结束时间（小时）' },
                { key: 'storeReq', label: '保存请求', type: 'boolean', tooltip: '是否保存请求参数' },
                { key: 'oneDayRecordMaxNum', label: '每日最大记录数', type: 'number', tooltip: '每天最大记录请求数' },
                { key: 'storeResp', label: '保存响应', type: 'boolean', tooltip: '是否保存响应结果' },
                { key: 'reExecutionDayAfter', label: '回放延迟天数', type: 'number', tooltip: '几天后进行回放' },
                { key: 'collectFrequency', label: '采集频率', type: 'percentage', tooltip: '采集的概率（百分比）' }
            ];
            
            // 接口相关字段
            const apiFields = [
                { key: 'oldPath', label: '旧接口路径', type: 'string', tooltip: '旧版接口路径' },
                { key: 'newPath', label: '新接口路径', type: 'string', tooltip: '新版接口路径' },
                { key: 'ralMethod', label: '请求方法', type: 'string', tooltip: 'HTTP请求方法' },
                { key: 'ralConverter', label: '请求格式', type: 'string', tooltip: '请求内容类型' },
                { key: 'cookie', label: 'Cookie', type: 'string', tooltip: '请求Cookie信息' }
            ];
            
            let rows = '';
            
            // 添加基础字段
            rows += basicFields.map(field => {
                return generateFieldRow(field, config);
            }).join('');
            
            // 添加接口相关字段
            const hasApiFields = apiFields.some(field => config[field.key] !== undefined);
            if (hasApiFields) {
                rows += `
                    <tr class="table-secondary">
                        <td colspan="2" class="text-center fw-bold">接口配置</td>
                    </tr>
                `;
                rows += apiFields.map(field => {
                    return generateFieldRow(field, config);
                }).join('');
            }
            
            // 添加复杂对象字段
            if (config.ignorePathArrayOrder && Object.keys(config.ignorePathArrayOrder).length > 0) {
                rows += `
                    <tr class="table-secondary">
                        <td colspan="2" class="text-center fw-bold">忽略路径数组顺序配置</td>
                    </tr>
                `;
                for (const [key, value] of Object.entries(config.ignorePathArrayOrder)) {
                    rows += `
                        <tr>
                            <td width="30%" class="align-middle">
                                <span data-bs-toggle="tooltip" title="忽略路径数组的顺序配置字段">${key}</span>
                            </td>
                            <td class="align-middle">
                                <code class="text-break">${value}</code>
                            </td>
                        </tr>
                    `;
                }
            }
            
            // 添加数组字段
            if (config.ignorePaths && config.ignorePaths.length > 0) {
                rows += `
                    <tr class="table-secondary">
                        <td colspan="2" class="text-center fw-bold">忽略路径列表</td>
                    </tr>
                `;
                config.ignorePaths.forEach((path, index) => {
                    rows += `
                        <tr>
                            <td width="30%" class="align-middle">路径 ${index + 1}</td>
                            <td class="align-middle">
                                <code class="text-break">${path}</code>
                            </td>
                        </tr>
                    `;
                });
            }
            
            if (config.diffString && config.diffString.length > 0) {
                rows += `
                    <tr class="table-secondary">
                        <td colspan="2" class="text-center fw-bold">差异字符串字段</td>
                    </tr>
                `;
                config.diffString.forEach((str, index) => {
                    rows += `
                        <tr>
                            <td width="30%" class="align-middle">字段 ${index + 1}</td>
                            <td class="align-middle">
                                <code class="text-break">${str}</code>
                            </td>
                        </tr>
                    `;
                });
            }
            
            return rows;
        }
        
        // 生成单个字段行
        function generateFieldRow(field, config) {
            let value = config[field.key];
            let displayValue = '';
            
            if (value === undefined || value === null) {
                displayValue = '<span class="text-muted">未设置</span>';
            } else if (field.type === 'boolean') {
                displayValue = value ? 
                    '<span class="badge bg-success">是</span>' : 
                    '<span class="badge bg-danger">否</span>';
            } else if (field.type === 'date') {
                // 处理YYYYMMDD格式日期
                if (value && value > 0) {
                    const dateStr = value.toString();
                    if (dateStr.length === 8) {
                        const year = dateStr.substring(0, 4);
                        const month = dateStr.substring(4, 6);
                        const day = dateStr.substring(6, 8);
                        displayValue = `${year}/${month}/${day}`;
                    } else {
                        displayValue = value;
                    }
                } else {
                    displayValue = '<span class="text-muted">未设置</span>';
                }
            } else if (field.type === 'timestamp') {
                // 处理时间戳格式化
                if (value && value > 0) {
                    // 如果是秒级时间戳
                    if (value < 10000000000) {
                        displayValue = new Date(value * 1000).toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });
                    } else {
                        // 如果是毫秒级时间戳
                        displayValue = new Date(value).toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });
                    }
                } else {
                    displayValue = '<span class="text-muted">未设置</span>';
                }
            } else if (field.type === 'percentage') {
                displayValue = value + '%';
            } else if (field.type === 'hour') {
                displayValue = value + ':00';
            } else if (typeof value === 'string' && (value.includes('/') || value.length > 50)) {
                // 长文本或路径使用代码样式显示
                displayValue = `<code class="text-break">${value}</code>`;
            } else {
                displayValue = value;
            }
            
            return `
                <tr>
                    <td width="30%" class="align-middle">
                        <span data-bs-toggle="tooltip" title="${field.tooltip}">${field.label}</span>
                    </td>
                    <td class="align-middle">${displayValue}</td>
                </tr>
            `;
        }

        // 切换配置视图
        function toggleConfigView(modalId) {
            const isJsonView = document.getElementById(`viewMode_${modalId}`).checked;
            const tableView = document.getElementById(`tableView_${modalId}`);
            const jsonView = document.getElementById(`jsonView_${modalId}`);
            
            if (isJsonView) {
                tableView.style.display = 'none';
                jsonView.style.display = 'block';
            } else {
                tableView.style.display = 'block';
                jsonView.style.display = 'none';
            }
        }

        // 过滤Handler
        function filterHandlers(modalId) {
            const searchTerm = document.getElementById(`searchHandler_${modalId}`).value.toLowerCase();
            const items = document.querySelectorAll(`#handlerAccordion_${modalId} .accordion-item`);
            
            items.forEach(item => {
                const handlerName = item.dataset.handlerName;
                if (handlerName.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 编辑配置
        function editConfig(modalId) {
            // 切换到编辑视图
            document.getElementById('tableView_' + modalId).style.display = 'none';
            document.getElementById('jsonView_' + modalId).style.display = 'none';
            document.getElementById('editView_' + modalId).style.display = 'block';
            
            // 切换底部按钮
            document.getElementById('viewFooter_' + modalId).style.display = 'none';
            document.getElementById('editFooter_' + modalId).style.display = 'block';
            
            // 隐藏编辑按钮
            document.getElementById('editBtn_' + modalId).style.display = 'none';
            
            // 禁用视图切换
            document.getElementById('viewMode_' + modalId).disabled = true;
            
            // 隐藏搜索框
            const searchBox = document.querySelector(`#searchHandler_${modalId}`).closest('.mb-3');
            if (searchBox) {
                searchBox.style.display = 'none';
            }
        }

        // 取消编辑
        function cancelEdit(modalId) {
            // 切换回表格视图
            document.getElementById('tableView_' + modalId).style.display = 'block';
            document.getElementById('jsonView_' + modalId).style.display = 'none';
            document.getElementById('editView_' + modalId).style.display = 'none';
            
            // 切换底部按钮
            document.getElementById('viewFooter_' + modalId).style.display = 'block';
            document.getElementById('editFooter_' + modalId).style.display = 'none';
            
            // 显示编辑按钮
            document.getElementById('editBtn_' + modalId).style.display = 'block';
            
            // 启用视图切换
            document.getElementById('viewMode_' + modalId).disabled = false;
            
            // 显示搜索框
            const searchBox = document.querySelector(`#searchHandler_${modalId}`).closest('.mb-3');
            if (searchBox) {
                searchBox.style.display = 'block';
            }
        }

        // 保存配置
        function saveConfig(modalId) {
            const newConfig = document.getElementById('configEditor_' + modalId).value;
            const errorDiv = document.getElementById('configError_' + modalId);
            
            // 验证JSON格式
            try {
                const parsedConfig = JSON.parse(newConfig);
                errorDiv.style.display = 'none';
                
                // 检查是否是单接口模式（只有一个接口配置）
                const configKeys = Object.keys(parsedConfig);
                let finalConfig = parsedConfig;
                
                if (configKeys.length === 1 && window.currentFullConfig && Object.keys(window.currentFullConfig).length > 1) {
                    // 单接口模式：将修改的配置合并到完整配置中
                    const handlerName = configKeys[0];
                    finalConfig = { ...window.currentFullConfig };
                    finalConfig[handlerName] = parsedConfig[handlerName];
                    
                    console.log('单接口模式，合并配置:', {
                        handlerName: handlerName,
                        originalConfigKeys: Object.keys(window.currentFullConfig).length,
                        mergedConfigKeys: Object.keys(finalConfig).length
                    });
                }
                
                // 确认保存
                if (!confirm('确定要保存配置更改吗？')) {
                    return;
                }
                
                // 发送保存请求
                fetch('/fwyytool/tools/diff/updateconfig', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        config: JSON.stringify(finalConfig)
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.errNo === 0) {
                        alert('配置保存成功');
                        bootstrap.Modal.getInstance(document.getElementById(modalId)).hide();
                    } else {
                        alert('保存失败: ' + (data.errMsg || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('保存配置失败:', error);
                    alert('保存配置失败，请重试');
                });
                
            } catch (e) {
                errorDiv.textContent = 'JSON格式错误: ' + e.message;
                errorDiv.style.display = 'block';
                return;
            }
        }

        // 从模态框复制内容
        function copyToClipboardFromModal(modalId) {
            // 优先复制完整配置，如果没有则复制当前配置
            const content = JSON.stringify(window.currentFullConfig || window.currentConfigData, null, 2);
            navigator.clipboard.writeText(content).then(() => {
                showToast('内容已复制到剪贴板');
            });
        }

        // 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'toast-container position-fixed top-0 end-0 p-3';
            toast.innerHTML = `
                <div class="toast show" role="alert">
                    <div class="toast-header">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong class="me-auto">成功</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">${message}</div>
                </div>
            `;
            document.body.appendChild(toast);
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        }

        // ==================== 重新运行Diff功能 ====================

        // 批量重新运行diff
        function batchRerun() {
            if (selectedIds.length === 0) {
                alert('请先选择要重新运行的记录');
                return;
            }

            if (!confirm(`确定要重新运行选中的 ${selectedIds.length} 条记录吗？`)) {
                return;
            }

            // 调用重新运行API
            rerunDiffRecords(selectedIds);
        }

        // 单次重新运行diff
        function singleRerun(id) {
            if (!confirm('确定要重新运行这条记录吗？')) {
                return;
            }

            // 调用重新运行API
            rerunDiffRecords([id]);
        }

        // 调用重新运行API
        function rerunDiffRecords(ids) {
            fetch('/fwyytool/tools/diff/rerun', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ids: ids
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.errNo === 0) {
                    // 显示成功提示
                    showRerunSuccessToast(ids.length);
                } else {
                    alert('重新运行失败: ' + (data.errMsg || '未知错误'));
                }
            })
            .catch(error => {
                console.error('重新运行失败:', error);
                alert('重新运行失败，请重试');
            });
        }

        // 显示重新运行成功提示
        function showRerunSuccessToast(count) {
            const toast = document.createElement('div');
            toast.className = 'toast-container position-fixed top-0 end-0 p-3';
            toast.innerHTML = `
                <div class="toast show" role="alert">
                    <div class="toast-header">
                        <i class="fas fa-redo text-success me-2"></i>
                        <strong class="me-auto">重新运行成功</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        已成功提交 ${count} 条记录的重新运行任务。
                        <br>
                        <strong class="text-primary">请稍后刷新页面查看结果</strong>
                    </div>
                </div>
            `;
            document.body.appendChild(toast);
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 5000);
        }
    </script>
</body>
</html>
{{end}}