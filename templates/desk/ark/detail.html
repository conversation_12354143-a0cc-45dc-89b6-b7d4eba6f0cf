{{ define "desk/ark/detail.html"}}
<!DOCTYPE HTML>
<head>
    <title>方舟 - 课程配置详情</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css"/>

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table.taskList td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }
    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }
</style>


<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">方舟课程信息</h3>
    <div class="form-group">
        <label for="courseID">课程ID{{.param.CourseID}}：</label>
        <input type="text" class="form-control" id="courseID" placeholder="请输入任务ID" name="courseID"
               {{if ne .params.CourseID 0}} value="{{.params.CourseID}}" {{end}}>
    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <button type="button" class="btn btn-primary" onclick="courseinfo()">课程信息</button>
    <br/>
    <label style="color: #ac2925">{{.error}}</label>
</form>

{{if ne .data.CourseID 0}}
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='10' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>
                课程信息
            </th>
        </tr>
        <tr style="background-color: #d9d9d9">
            <th>课程名称</th>
            <th>学段</th>
            <th>学年学季</th>
            <th>服务模式</th>
            <th>服务模版</th>

        </tr>
        <tr style="height: 46px">
            <td style="color: tomato"><a href="/fwyytool/desk/course/detail?courseID={{.data.CourseID}}" target="_blank">{{.data.CourseName}}</a> <span class="label label-primary">{{.data.CourseTypeName}}</span></td>
            <td>{{.data.Department}} - {{.data.DepartName}}</td>
            <td>{{.data.Year}} - {{.data.SeasonName}}</td>
            <td>( {{.data.CoursePriceTag}} ) {{.data.CoursePriceTagName}}</td>
            <td>
                {{range $key, $value := .data.ServiceList}}
                <a href="/assistantdesk/view/first-line-teacher/server-config-manage-new?tplId={{$value.TplId}}" target="_blank">{{$value.ServiceName}} ( {{$value.TplId}} )</a>
                {{end}}
            </td>
        </tr>
    </table>
</div>

<ul class="nav nav-tabs" id="tabService">
    {{range $key, $value := .data.ServiceList}}
    <li role="presentation" {{if eq $key 0}} class="active" {{end}}  serviceMark="{{$value.ID}}">
        <a class="chapterTaskList">{{$value.ServiceName}} ( {{$value.TplId}} )</a>
    </li>
    {{end}}
</ul>

<div>
{{range $key, $serviceValue := .data.ServiceList}}
<div id="view_{{$serviceValue.ID}}" style="overflow-y: scroll; {{if ne $key 0}}visibility: none;{{end}} margin-top: 8px;margin-right: 10px">
        {{range $fk, $filedMapTree := $serviceValue.FiledMapTreeList}}
        <h3>{{$filedMapTree.FieldTypeName}}</h3>
        <table class="table table-bordered table-striped table-hover taskList" style="font-size:16px;">
            <tbody>
            <tr style="background-color: #d9d9d9">
                <th>方舟ID - 规则名称</th>
                <th style="width:300px">方舟key</th>
                <th style="width:400px">feconfig</th>
                <th style="width:400px">filterMap</th>
            </tr>
            {{range $sk, $secondGroupInfo := $filedMapTree.SecondGroup}}
            {{range $lk, $info := $secondGroupInfo.List}}
            <tr style="height: 46px; font-size: 14px;">
                <td style=""><a href="/assistantdesk/view/first-line-teacher/rd-config?ruleId={{$info.RuleId}}" target="_blank">{{$info.RuleId}}</a> - {{$info.RuleName}}</td>
                <td >{{$info.Key}}</td>
                <td><textarea style="overflow:scroll;height: 300px;width:380px">{{jsonPretty $info.FeConfig}}</textarea></td>
                <td><textarea style="overflow:scroll;height: 300px;width:380px">{{jsonPretty $info.FilterMap}}</textarea></td>
            </tr>
            {{end}}
            {{end}}
            </tbody>
        </table>
        {{end}}
</div>
{{end}}
</div>
{{else}}
<div style="height:34px;padding:6px;font-size:16px;text-align:center;background-color: darkred;color:white">没有查到课程</div>
{{end}}
<div style="display:none;">
    {{.dataJson}}
</div>
<script>
    $(document).ready(function () {
        $('#tabService li').click( function () {
                $(this).addClass("active");
                $(this).nextAll().removeClass("active");
                $(this).prevAll().removeClass("active");
                serviceMark = $(this).attr("serviceMark");
                $("#view_" + serviceMark).show();
                $("#view_" + serviceMark).nextAll().hide();
                $("#view_" + serviceMark).prevAll().hide();
            }
        );
    });
    function courseinfo() {
        var tempwindow = window.open('_blank');
        tempwindow.location = 'https://beidou.zuoyebang.cc/zbk/course{{if ne .params.CourseID 0}}?courseId={{.params.CourseID}}{{end}}';
    }

</script>
{{end}}