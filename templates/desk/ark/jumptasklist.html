{{ define "desk/ark/jumptasklist.html"}}
<!DOCTYPE HTML>
<head>
    <title>方舟 - 课程配置详情</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css"/>

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table.taskList td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }
    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }
</style>


<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">方舟课程信息</h3>
    <div class="form-group">
        <input type="text" class="form-control" id="courseId" placeholder="请输入课程ID" name="courseId">
        <input type="text" class="form-control" id="xuid" placeholder="请输入XUID" name="xuid">
    </div>
    <button type="button" class="btn btn-primary" id="submitJump">跳转到任务列表</button>
    <br/>
    <label style="color: #ac2925">{{.error}}</label>
</form>

<script>

    jQuery(document).ready(function() {
        //查询
        $("#submitJump").click(
            function () {
                courseId = $("#courseId").val()
                xuid = $("#xuid").val()

                if (courseId == "") {
                    console.log("param err")
                    return
                }
                var url = "/fwyytool/desk/ark/jumptasklistapi?courseId="+courseId

                if (xuid != "") {
                    url += "&xuid="+xuid
                }

                $.ajax({
                    url: url,
                    type: "GET",
                    success: function (resp) {
                        if(resp.errNo!=0){
                            alert("失败："+resp.errMsg)
                            return
                        }
                        targetAssistantDesk(resp.data.courseId, resp.data.year, resp.data.xuid, resp.data.assistantUid, resp.data.serviceId, resp.data.courseServiceType, resp.data.courseServiceTypeName)
                    },
                    error:function () {
                        alert("失败")
                    }
                });
            }
        );// onclick
    })

    function targetAssistantDesk(courseId, year, xuid, assistantUid, serviceId, serviceType, serviceTypeName) {
        const oneLevelDomain = window.location.hostname.split(".").slice(-2).join(".");
        const path = '/';
        const maxAge = 60 * 60 * 24 * 365; // one year

        document.cookie = `XUID=${xuid};domain=${window.location.hostname};path=${path};expires=Thu, 01 Jan 1970 00:00:00 GMT`;
        document.cookie = `XUID=${xuid};domain=${oneLevelDomain};path=${path};max-age=${maxAge}`;


        $.get("/assistantdesk/deskv1/user/changeselectedassistant?assistantUid=" + assistantUid, function(data){
            console.log(data)
        });

        const courseDataTaskV2 = {
            "year": year,
            "courseId": courseId,
            "checkedData": {
                "courseId": courseId,
                "serviceTypeLabel": serviceTypeName,
                "serviceTypeValue": serviceType
            },
            "serviceId": serviceId
        };
        console.log(courseDataTaskV2)
        sessionStorage.setItem('courseDataTaskV2', JSON.stringify(courseDataTaskV2));

        var tempwindow = window.open('_blank');
        tempwindow.location = '/assistantdesk/view/assistant-first-line-teacher-v2/first-line-teacher/task/crm-task-v2';
    }
</script>
{{end}}