{{ define "desk/ark/arkFieldDownGrade.html"}}
<!DOCTYPE HTML>
<head>
    <title>方舟字段降级配置</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css"/>
<meta charset="UTF-8">

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table.taskList td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }
    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }
</style>

<h3 style="margin-left: 5px">方舟字段降级字段配置查询</h3><br/>
<br>

    <label for="fieldKey">字段 key </label>
    <input type="text" id="fieldKey" placeholder="方舟字段 key">
    <br>
    <label for="timeout">超时时间 </label>
    <input type="number" id="timeout" placeholder="单位 ms">
    <br>
    <button style="color: red;" onclick="executeAction(event)">设置字段超时时间</button><br>
    <span id="data"></span>

<script>
    function executeAction(event) {
        event.preventDefault();

        const param1 = document.getElementById("fieldKey").value;
        const param2 = document.getElementById("timeout").value;

        fetch(`/assistantdesk/api/fwyytool/setdowngradefieldtimeoutconfig?fieldKey=${param1}&timeout=${param2}`)
            .then(response => response.text())
            .then(data => {
                // 这里可以对数据进行处理，例如格式化或其他操作
                document.getElementById("data").innerText = data;
            })
            .catch(error => console.error('Error:', error));
    }
</script>

<br><br>

    <label for="fieldKey2">字段 key </label>
    <input type="text" id="fieldKey2" placeholder="方舟字段 key">
    <br>
    <button style="color: red;" onclick="executeAction2(event)">设置降级字段</button>
    <br>
    <span id="data2"></span>


<script>
    function executeAction2(event) {
        event.preventDefault();

        const param3 = document.getElementById("fieldKey2").value;

        fetch(`/assistantdesk/api/fwyytool/setdowngradefield?fieldKey=${param3}`)
            .then(response => response.text())
            .then(data2 => {
                // 这里可以对数据进行处理，例如格式化或其他操作
                document.getElementById("data2").innerText = data2;
            })
            .catch(error => console.error('Error:', error));
    }
</script>

<br><br>


    <label for="fieldKey3">字段 key </label>
    <input type="text" id="fieldKey3" placeholder="方舟字段 key">
    <br>
    <button style="color: red;" onclick="executeAction3(event)">取消字段降级</button>
<br>
    <span id="data3"></span>

<script>
    function executeAction3(event) {
        event.preventDefault();

        const param4 = document.getElementById("fieldKey3").value;

        fetch(`/assistantdesk/api/fwyytool/deldowngradefield?fieldKey=${param4}`)
            .then(response => response.text())
            .then(data3 => {
                // 这里可以对数据进行处理，例如格式化或其他操作
                document.getElementById("data3").innerText = data3;
            })
            .catch(error => console.error('Error:', error));
    }
</script>

<br><br>

<div style="display:none;">
    {{.dataJson}}
</div>
<form class="form-inline" style="margin-left: 5px;">
    <button type="submit" class="btn btn-success"> 查询方舟字段降级详情</button>
    <br/>
    <label style="color: #ac2925">{{.error}}</label>
</form>

<div style="position: relative;">
    <table class="table table-bordered table-striped table-hover">
        <tr>
            <th colspan="13" style="text-align:center;background-color: #2a5839;color:white">字段降级配置</th>
        </tr>
        <tr class="active">
            <th style="width:120px; text-align:center;">已被降级字段列表</th>
            <th style="width:120px; text-align:center;">字段报警超时阈值</th>
        </tr>

        <tr>
            <td><textarea style="overflow:scroll;height: 450px;width:530px">{{jsonPrettyStruct .data.DownGradeConfig}}</textarea></td>
            <td><textarea style="overflow:scroll;height: 450px;width:530px">{{jsonPrettyStruct .data.TimeoutConfig}}</textarea></td>
        </tr>
    </table>
</div>

{{end}}










