{{ define "desk/cachetool/delcachebykey.html"}}
<!DOCTYPE HTML>
<head>
    <title>缓存清理工具</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css"/>
<meta charset="UTF-8">

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table.taskList td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }
    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }
</style>

<h3 style="margin-left: 5px">bzr 集群缓存清理</h3><br/>
<label for="cacheKeys">缓存keys</label>
<input type="text" id="cacheKeys" placeholder="逗号分隔" style="width: 66.66vw;"> <!-- 设置为屏幕宽度的 2/3 -->
<br>
<br>

<button style="color: red;" onclick="executeAction(event)">清理缓存</button><br>
<span id="data"></span>

<script>
    function executeAction(event) {
        event.preventDefault();

        const cacheKeysList = document.getElementById("cacheKeys").value.trim();

        // 将额外的字段 keys 转换为数组
        const keysArray = cacheKeysList.split(",").map(key => key.trim()).filter(key => key !== "");

        // 构造 POST 请求的参数
        const postData = {
            keys: keysArray, // 字段 keys 数组
        };

        // 发起 POST 请求
        fetch(`/arkgo/tool/delcache`, {
            method: "POST", // 指定请求方法为 POST
            headers: {
                "Content-Type": "application/json" // 设置请求头，表明发送的是 JSON 格式数据
            },
            body: JSON.stringify(postData) // 将请求体转换为 JSON 格式
        })
            .then(response => {
                if (!response.ok) { // 检查响应状态
                    throw new Error('Network response was not ok');
                }
                return response.text(); // 解析响应内容为文本
            })
            .then(data => {
                // 这里可以对数据进行处理，例如格式化或其他操作
                document.getElementById("data").innerText = data;
            })
            .catch(error => {
                console.error('Error:', error); // 捕获并处理错误
            });
    }
</script>


{{end}}










