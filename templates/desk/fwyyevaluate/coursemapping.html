{{ define "desk/fwyyevaluate/coursemapping.html"}}
<!doctype html>
<html lang="zh-CN" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
          name="viewport">
    <meta content="ie=edge" http-equiv="X-UA-Compatible">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <title>课程问卷绑定</title>
    <style>

    </style>
</head>
<body>
<div class="container">

    <h1 class="mb-4">课程问卷绑定</h1>

    <div class="row d-flex align-items-stretch p-2">
        <div class="col-md-3">
            <label class="form-label" for="courseIdSearch">课程ID：</label>
            <input class="form-control" id="courseIdSearch" type="text">
        </div>
        <div class="col-md-6 d-flex align-items-stretch p-2">
            <button type="button" class="btn btn-primary m-2" id="searchBtn">查询</button>
            <button type="button" class="btn btn-success m-2" id="addBtn">新增</button>
        </div>
    </div>

    <table class="table table-bordered">
        <thead>
        <tr>
            <th>课程ID</th>
            <th>问卷ID</th>
            <th>问卷名称</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody id="tableBody">
        </tbody>
    </table>

    <div id="pagination"></div>

    <p>
        文档：<a href="https://docs.zuoyebang.cc/doc/1868860078131089409" target="_blank">初中学科诊断技术方案</a>
    </p>

    <!-- 模态框 -->
    <div class="modal" id="dataModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">数据明细</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="dataId">
                    <input type="hidden" id="type">
                    <div class="form-group">
                        <label for="courseId">课程ID:</label>
                        <input type="number" class="form-control" id="courseId">
                    </div>
                    <div class="form-group">
                        <label for="evaluateId">问卷ID:</label>
                        <input type="number" class="form-control" id="evaluateId">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="saveData">保存</button>
                </div>
            </div>
        </div>
    </div>

</div>
</body>
<script src="https://www.jsviews.com/download/jsrender.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twbs-pagination/1.4.2/jquery.twbsPagination.min.js"></script>
<script>
    $(document).ready(function () {
        // 自定义分隔符
        $.views.settings.delimiters("<%", "%>");

        // 获取数据
        function fetchData(page = 1) {
            let url = `/fwyy-evaluate/probe/courseidevaluateidmapping/list`;
            let body = {
                "pn": page - 1,
                "rn": 20
            };
            let courseId = $("#courseIdSearch").val().trim();
            if (courseId && parseInt(courseId)) {
                body["courseId"] = parseInt(courseId);
            }
            $.ajax({
                type: "POST",
                url: url,
                contentType: "application/json",
                data: JSON.stringify(body),
                success: function (data) {
                    renderTableData(data);
                    renderPagination(Math.ceil(data.data.total / 20));
                }
            });
        }

        // 渲染表格数据
        function renderTableData(data) {
            let template = $.templates(`
            <%for data.list%>
            <tr data-id="<%:id%>">
                <td><%:courseId%></td>
                <td><%:evaluateId%></td>
                <td><%:evaluateName%></td>
                <td>
                    <button class="editBtn btn btn-primary btn-sm">编辑</button>
                    <button class="deleteBtn btn btn-danger btn-sm">删除</button>
                </td>
            </tr>
            <%/for%>
        `);

            data = dataAdapter(data);
            let html = template.render(data);
            $("#tableBody").html(html);
        }

        // 数据调整
        function dataAdapter(data) {
            for (let i = 0; i < data.data.list.length; i++) {
                let evaluateName = "";
                switch (data.data.list[i].evaluateId) {
                    case 1: evaluateName = "初一学情测评v1"; break;
                    case 2: evaluateName = "初二学情测评v1"; break;
                    case 3: evaluateName = "初三学情测评v1"; break;
                    case 4: evaluateName = "初一学科诊断测评v2"; break;
                    case 5: evaluateName = "初二学科诊断测评v2"; break;
                    case 6: evaluateName = "初三学科诊断测评v2"; break;
                }
                data.data.list[i].evaluateName = evaluateName;
            }
            return data;
        }

        // 渲染分页
        function renderPagination(totalPages) {
            $('#pagination').twbsPagination({
                totalPages: totalPages,
                visiblePages: 5,
                onPageClick: function (event, page) {
                    fetchData(page);
                }
            });
        }

        // 查询按钮点击事件
        $("#searchBtn").click(function () {
            fetchData();
        });
        // 新增
        $("#addBtn").click(function(){
            $('#dataId').val('');
            $('#type').val('add');
            $('#courseId').val('');
            $('#evaluateId').val('');
            $('#dataModal').modal('show');
        });
        // 修改
        $(document).on('click', '.editBtn', function(){
            let dataId = $(this).closest('tr').attr('data-id');
            let courseId = $(this).closest('tr').find('td:eq(0)').text();
            let evaluateId = $(this).closest('tr').find('td:eq(1)').text();

            $('#dataId').val(dataId);
            $('#courseId').val(courseId).prop('readonly', true);
            $('#evaluateId').val(evaluateId);
            $('#type').val('edit');
            $('#dataModal').modal('show');
        });
        // 保存按钮点击事件
        $('#saveData').click(function(){
            let type = $('#type').val();
            let dataId = $('#dataId').val();
            let courseId = $('#courseId').val();
            let evaluateId = $('#evaluateId').val();
            if (type === 'edit') {
                let url = `/fwyy-evaluate/probe/courseidevaluateidmapping/update`;
                $.ajax({
                    type: "POST",
                    url: url,
                    contentType: "application/json",
                    data: JSON.stringify({
                        "id": parseInt(dataId),
                        "courseId": parseInt(courseId),
                        "evaluateId": parseInt(evaluateId)
                    }),
                    success: function () {
                        fetchData();
                    }
                });
            } else if (type === 'add') {
                let url = `/fwyy-evaluate/probe/courseidevaluateidmapping/insert`;
                $.ajax({
                    type: "POST",
                    url: url,
                    contentType: "application/json",
                    data: JSON.stringify({
                        "courseIdList": [parseInt(courseId)],
                        "evaluateId": parseInt(evaluateId)
                    }),
                    success: function () {
                        fetchData();
                    }
                });
            }

            // 关闭模态框
            $('#dataModal').modal('hide');
        });
        // 删除按钮点击事件
        $(document).on('click', '.deleteBtn', function(){
            let courseId = $(this).closest('tr').find('td:eq(0)').text();
            if (confirm("确认删除？")) {
                let url = `/fwyy-evaluate/probe/courseidevaluateidmapping/delete`;
                $.ajax({
                    type: "POST",
                    url: url,
                    contentType: "application/json",
                    data: JSON.stringify({
                        "courseIdList": [parseInt(courseId)]
                    }),
                    success: function () {
                        fetchData();
                    }
                });
            }
        });

        // 初始化页面加载数据
        fetchData();
    });
</script>
</html>
{{end}}