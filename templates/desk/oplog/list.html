{{ define "desk/oplog/list.html" }}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>操作记录</title>

    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?12223"/>

    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/fwyytool/assets/js/bootstrap.min.js"></script>
    <script src="/fwyytool/assets/layer/layer.js"></script>
    <style>
        table.taskList th {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #dedede;
        }

        table.taskList td {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #ffffff;
        }

        div {
            margin-left: 10px;
        }

        .nav {
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
        }

        ol, ul {
            margin-top: 0;
        }

        a {
            color: #555;
            font-size: 16px;
        }

        .active {
            border-bottom: 3px solid #409c19;
            font-weight: bolder;
        }
        textarea {
            resize: both; /* 允许调整大小 */
            overflow: hidden; /* 防止内容溢出 */
            min-width: 100px; /* 最小宽度 */
            min-height: 50px; /* 最小高度 */
            max-width: 100%; /* 最大宽度 */
            width: 100%; /* 宽度100% */
            height: 200px; /* 高度自适应 */
            box-sizing: border-box; /* 包含padding和border在内的总宽度 */
            padding: 10px; /* 内边距 */
        }
    </style>
</head>
<body>
<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">操作记录</h3>
    <div class="form-group">
        <label for="personUid">真人ID：</label>
        <input type="text" class="form-control" id="personUid" placeholder="请输入真人ID" name="personUid"
               {{if ne .params.PersonUid 0}} value="{{.params.PersonUid}}" {{end}}>
        <label for="relationId">业务ID：</label>
        <input type="text" class="form-control" id="relationId" placeholder="请输入真人ID" name="relationId"
               {{if .params.RelationId }} value="{{.params.RelationId}}" {{end}}>
        <label for="relationType">业务类型：</label>
        <input type="text" class="form-control" id="relationType" placeholder="请输入业务ID类型" name="relationType"
               {{if .params.RelationType }} value="{{.params.RelationType}}" {{end}}>

    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #524848">{{.errMsg}}</label>
</form>

{{if ne (len .data) 0}}
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='13' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>
                操作记录
            </th>
        </tr>
        <tr style="background-color: #d9d9d9">
            <th>服务</th>
            <th>模块</th>
            <th>功能</th>
            <th>关联Id</th>
            <th>关联类型</th>
            <th>描述</th>
            <th>内容</th>
            <th>修改前</th>
            <th>真人ID</th>
            <th>资产id</th>
            <th>TraceID</th>
            <th>ReqID</th>
            <th>操作时间</th>
        </tr>
        {{range $key,$oplog := .data}}
        <tr style="height: 46px">
            <td>{{$oplog.Refer}}</td>
            <td>{{$oplog.Module}}</td>
            <td>{{$oplog.Service}}</td>
            <td>{{$oplog.RelationId}}</td>
            <td>{{$oplog.RelationType}}</td>
            <td><textarea style="overflow:scroll;height: 10px;width:380px">{{$oplog.Remark}}</textarea></td>
            <td><textarea style="overflow:scroll;height: 10px;width:380px">{{$oplog.Content}}</textarea></td>
            <td><textarea style="overflow:scroll;height: 10px;width:380px">{{$oplog.Before}}</textarea></td>
            <td>{{$oplog.PersonUid}}</td>
            <td>{{$oplog.AssistantUid}}</td>
            <td>{{$oplog.LogId}}</td>
            <td>{{$oplog.RequestId}}</td>
            <td><textarea style="overflow:hidden;height: 10px;width:220px">{{$oplog.OperateTimeFormat}}</textarea></td>
        </tr>
        {{end}}
    </table>
</div>
{{end}}


<div style="display:none;">
    {{.dataJson}}
</div>
<script>

</script>
</body>
</html>
{{ end }}