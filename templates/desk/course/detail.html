{{ define "desk/course/detail.html"}}
<!DOCTYPE HTML>
<head>
    <title>课程详情</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?12223"/>

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table.taskList td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }
    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }
</style>


<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">课程信息</h3>
    <div class="form-group">
        <label for="courseID">课程ID{{.param.CourseID}}：</label>
        <input type="text" class="form-control" id="courseID" placeholder="请输入任务ID" name="courseID"
               {{if ne .params.CourseID 0}} value="{{.params.CourseID}}" {{end}}>
    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <button type="button" class="btn btn-primary" onclick="courseinfo()">北斗课程信息</button>
    <br/>
    <label style="color: #ac2925">{{.error}}</label>
</form>

{{if ne .data.CourseID 0}}
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='10' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>
                课程信息
            </th>
        </tr>
        <tr style="background-color: #d9d9d9">
            <th>课程名称</th>
            <th>学科</th>
            <th>学段</th>
            <th>学年 - 学季</th>
            <th>是否内部课</th>
        </tr>
        <tr style="height: 46px">
            <td style="color: tomato">{{.data.CourseName}} <span class="label label-primary">{{.data.CourseTypeName}}</span></td>
            <td>{{.data.SubjectName}}</td>
            <td>{{.data.GradeName}}</td>
            <td>{{.data.Year}} - {{.data.SeasonName}}</td>
            <td>{{.data.IsInner}}</td>
        </tr>
        <tr style="background-color: #d9d9d9">
            <td>上课时间</td>
            <td>cpuID</td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr style="height: 46px">
            <td style="color: tomato">{{.data.OnlineFormatTime}}</td>
            <td>{{.data.CpuID}}</td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </table>
</div>

<ul class="nav nav-tabs" id="tabService">
    <li role="presentation" class="active" >
        <a class="chapterTaskList">章节</a>
    </li>
</ul>

<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
        <table class="table table-bordered table-striped table-hover taskList" style="font-size:16px;">
            <tbody>
            <tr style="background-color: #d9d9d9">
                <th style="width:200px">章节Id</th>
                <th style="width:200px">章节名称</th>
                <th style="width:200px">章节类型</th>
                <th style="width:120px">大纲Id</th>
                <th style="width:120px">直播类型</th>
                <th style="width:260px">上课时间</th>
                <th style="width:120px">章节状态</th>
            </tr>
            {{range $key, $lessonInfo := .data.LessonList}}
            <tr style="height: 46px; font-size: 14px;">
                <td><a href="https://beidou.zuoyebang.cc/zbk/lesson?lessonId={{$lessonInfo.LessonID}}" target="_blank">{{$lessonInfo.LessonID}}</a></td>
                <td>{{$lessonInfo.LessonName}}</td>
                <td>{{$lessonInfo.LessonTypeName}}</td>
                <td>{{$lessonInfo.OutlineID}}</td>
                <td>{{$lessonInfo.PlayTypeName}}</td>
                <td>{{$lessonInfo.LessonDate}}</td>
                <td>{{$lessonInfo.StatusName}}</td>
            </tr>
            {{end}}
            </tbody>
        </table>
</div>
{{else}}
<div style="height:34px;padding:6px;font-size:16px;text-align:center;background-color: darkred;color:white">没有查到课程</div>
{{end}}
<div style="display:none;">
    {{.dataJson}}
</div>
<script>
    $(document).ready(function () {
    });
    function courseinfo() {
        var tempwindow = window.open('_blank');
        tempwindow.location = 'https://beidou.zuoyebang.cc/zbk/course{{if ne .params.CourseID 0}}?courseId={{.params.CourseID}}{{end}}';
    }

</script>
{{end}}