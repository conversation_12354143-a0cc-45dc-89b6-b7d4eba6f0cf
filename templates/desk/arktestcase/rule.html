{{ define "desk/arktestcase/rule.html"}}
<!DOCTYPE HTML>
<head>
    <title>方舟 - 课程配置详情</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css"/>

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table.taskList td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }
    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }
</style>


<div style="display:none;">
    {{.dataJson}}
</div>
<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">方舟测试case查询</h3>
    <div class="form-group">
        <label for="ruleId">{{.param.RuleId}}</label>
        <input type="text" class="form-control" id="ruleId" placeholder="请输入规则id" name="ruleId"
               {{if .params.RuleId}} value="{{.params.RuleId}}" {{end}}>
        <select class="form-control" name="isInner">
            <option value="0" data-type="0" {{if eq .params.IsInner 0}} selected="selected" {{end}}>正式课</option>
            <option value="1" data-type="1" {{if eq .params.IsInner 1}} selected="selected" {{end}}>内部课</option>
        </select>
    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #ac2925">{{.error}}</label>
</form>

{{range $key, $arkTestCaseDetail := .data.DetailList}}
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class="taskList table table-bordered table-striped table-hover">
        <tbody><tr>
            <th colspan="7" style="text-align:center;background-color: #269abc;color:white">{{$arkTestCaseDetail.Name}}(Key:{{$arkTestCaseDetail.RuleKey}}，RuleID:{{$arkTestCaseDetail.RuleId}})</th>
        </tr>

        <tr>
            <th>方舟规则ID</th><td>{{$arkTestCaseDetail.RuleId}}</td>
            <th>方舟规则Key</th><td>{{$arkTestCaseDetail.RuleKey}}</td>
            <th>Function</th><td>{{$arkTestCaseDetail.Rule.Function}}</td>
        </tr>
        <tr>
            <th>方舟规则名称</th><td>{{$arkTestCaseDetail.Name}}</td>
            <th>customName</th><td>{{$arkTestCaseDetail.Rule.CustomName}}</td>
            <th>Info</th><td>{{$arkTestCaseDetail.Rule.Info}}</td>
        </tr>
        </tbody></table>
</div>

<div style="position: relative;">
    <table class="table table-bordered table-striped table-hover">
        <thead style="position: sticky; top: 0; z-index: 1;">
        <tr>
            <th colspan="13" style="text-align:center;background-color: #2a5839;color:white">case列表</th>
        </tr>
        <tr class="active">
            <th style="width:120px; text-align:center;">课程ID</th>
            <th style="width:120px; text-align:center;">学年</th>
            <th style="width:120px;text-align:center;">模版ID</th>
            <th style="width:300px;text-align:center;">服务名称</th>
            <th>XUID</th>
        </tr>
        </thead>
        <tbody>

        {{range $idx, $courseDetail := $arkTestCaseDetail.BindCourse}}
        <tr>
            <td style="text-align:center;">{{$courseDetail.CourseId}}</td>
            <td style="text-align:center;">{{$courseDetail.Year}}</td>
            <td style="text-align:center;">{{$courseDetail.TplId}}</td>
            <td style="text-align:center;">{{$courseDetail.ServiceName}} ({{$courseDetail.ServiceId}})</td>
            <td >{{range $index, $teacherDetail := $courseDetail.TeacherList}}{{if ne $index 0 }}, {{end}}<a href="javascript:jump({{$courseDetail.CourseId}}, {{$teacherDetail.AssistantUid}}, {{$courseDetail.ServiceId}})">{{$teacherDetail.AssistantUid}}{{end}}</a></td>
        </tr>
        {{end}}
        </tbody>
    </table>
</div>
{{end}}

<script>
    function jump(courseId, xuid, serviceId) {
        var url = "/fwyytool/desk/ark/jumptasklistapi?courseId="+courseId
        if (xuid != "") {
            url += "&xuid="+xuid
        }

        $.ajax({
            url: url,
            type: "GET",
            success: function (resp) {
                if(resp.errNo!=0){
                    alert("失败："+resp.errMsg)
                    return
                }
                targetAssistantDesk(resp.data.courseId, resp.data.year, resp.data.xuid, resp.data.assistantUid, serviceId, resp.data.courseServiceType, resp.data.courseServiceTypeName)
            },
            error:function () {
                alert("失败")
            }
        });
    }

    function targetAssistantDesk(courseId, year, xuid, assistantUid, serviceId, serviceType, serviceTypeName) {
        const oneLevelDomain = window.location.hostname.split(".").slice(-2).join(".");
        const path = '/';
        const maxAge = 60 * 60 * 24 * 365; // one year

        document.cookie = `XUID=${xuid};domain=${window.location.hostname};path=${path};expires=Thu, 01 Jan 1970 00:00:00 GMT`;
        document.cookie = `XUID=${xuid};domain=${oneLevelDomain};path=${path};max-age=${maxAge}`;


        $.get("/assistantdesk/deskv1/user/changeselectedassistant?assistantUid=" + assistantUid, function(data){
            console.log(data)
        });

        const courseDataTaskV2 = {
            "year": year,
            "courseId": courseId,
            "checkedData": {
                "courseId": courseId,
                "serviceTypeLabel": serviceTypeName,
                "serviceTypeValue": serviceType
            },
            "serviceId": serviceId
        };
        console.log(courseDataTaskV2)
        sessionStorage.setItem('courseDataTaskV2', JSON.stringify(courseDataTaskV2));

        var tempwindow = window.open('_blank');
        tempwindow.location = '/assistantdesk/view/assistant-first-line-teacher-v2/first-line-teacher/task/crm-task-v2';
    }
</script>
{{end}}