{{ define "allocate/apis/getCourseInfo.html" }}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>获取课程已排班资产信息</title>

    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?12223"/>

    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/fwyytool/assets/js/bootstrap.min.js"></script>
    <script src="/fwyytool/assets/layer/layer.js"></script>
    <style>
        table.taskList th {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #dedede;
        }

        table.taskList td {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #ffffff;
        }

        div {
            margin-left: 10px;
        }

        .nav {
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
        }

        ol, ul {
            margin-top: 0;
        }

        a {
            color: #555;
            font-size: 16px;
        }

        .active {
            border-bottom: 3px solid #409c19;
            font-weight: bolder;
        }
        textarea {
            resize: both; /* 允许调整大小 */
            overflow: hidden; /* 防止内容溢出 */
            min-width: 100px; /* 最小宽度 */
            min-height: 50px; /* 最小高度 */
            max-width: 100%; /* 最大宽度 */
            width: 100%; /* 宽度100% */
            height: 200px; /* 高度自适应 */
            box-sizing: border-box; /* 包含padding和border在内的总宽度 */
            padding: 10px; /* 内边距 */
        }
    </style>
</head>
<body>
<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">排班课程信息</h3>
    <div class="form-group">
        <label for="courseId">课程ID：</label>
        <input type="text" class="form-control" id="courseId" placeholder="请输入任务ID" name="courseId"
               {{if ne .params.CourseId 0}} value="{{.params.CourseId}}" {{end}}>
    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <button type="button" class="btn btn-primary" onclick="courseinfo()">北斗课程信息</button>
    <br/>
    <label style="color: #ac2925">{{.errMsg}}</label>
</form>

{{if ne .data.CourseId 0}}
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='10' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>
                课程信息
            </th>
        </tr>
        <tr style="background-color: #d9d9d9">
            <th>课程id</th>
            <th>课程类型</th>
            <th>课程服务模式</th>
            <th>课程例子类型</th>
            <th>学年 - 学季</th>
            <th>学部 - 年级</th>
        </tr>
        <tr style="height: 46px">
            <td style="color: tomato">{{.data.CourseId}}</td>
            <td>{{.data.NewCourseType}}</td>
            <td>{{.data.PriceTagId}}</td>
            <td>{{.data.SaleMode}}</td>
            <td>{{.data.Year}} - {{.data.Season}}</td>
            <td>{{.data.Department}} - {{.data.GradeId}}</td>
        </tr>
    </table>
</div>

{{if ne (len .data.AcList) 0}}
<ul class="nav nav-tabs" id="tabService">
    <li role="presentation" class="active" >
        <a class="chapterTaskList">排班资产</a>
    </li>
</ul>

<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class="table table-bordered table-striped table-hover taskList" style="font-size:16px;">
        <tbody>
        <tr style="background-color: #d9d9d9">
            <th style="width:200px">资产uid</th>
<!--            <th style="width:200px">资产名称</th>-->
<!--            <th style="width:200px">资产手机号</th>-->
<!--            <th style="width:120px">真人uid</th>-->
<!--            <th style="width:120px">真人名称</th>-->
            <th style="width:200px">小班id</th>
            <th style="width:120px">小班编号</th>
            <th style="width:120px">小班上限人数</th>
        </tr>
        {{range $idx, $acInfo := .data.AcList}}
            {{$c := true}}
            {{range $idx2, $classInfo := $acInfo.ClassList}}
            <tr style="height: 46px; font-size: 14px;">
                {{if $c}}
                <td rowspan="{{len $acInfo.ClassList}}">{{$acInfo.AssistantUid}}</td>
                {{end}}
                {{$c = false}}
                <td>{{$classInfo.ClassId}}</td>
                <td>{{$classInfo.Code}}</td>
                <td>{{$classInfo.StudentMaxCnt}}</td>
            </tr>
            {{end}}
        {{end}}
        </tbody>
    </table>
</div>
{{else}}
<div style="height:34px;padding:6px;font-size:16px;text-align:center;background-color: darkred;color:white">没有已排班的资产</div>
{{end}}
{{else}}
<div style="height:34px;padding:6px;font-size:16px;text-align:center;background-color: darkred;color:white">没有查到课程</div>
{{end}}
<div style="display:none;">
    {{.dataJson}}
</div>
<script>
    $(document).ready(function () {
    });
    function courseinfo() {
        var tempwindow = window.open('_blank');
        tempwindow.location = 'https://beidou.zuoyebang.cc/zbk/course{{if ne .params.CourseId 0}}?courseId={{.params.CourseId}}{{end}}';
    }

</script>
</body>
</html>
{{ end }}