<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件文件列表</title>
    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css" />
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            border-bottom: 2px solid #2e6da4;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2e6da4;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 16px;
        }
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .file-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
            background: #fff;
        }
        .file-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .file-icon {
            font-size: 48px;
            text-align: center;
            margin-bottom: 15px;
        }
        .file-name {
            font-size: 18px;
            font-weight: 600;
            color: #2e6da4;
            margin-bottom: 10px;
            word-break: break-all;
        }
        .file-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .download-btn {
            display: inline-block;
            background: #2e6da4;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: background 0.3s ease;
            width: 100%;
            text-align: center;
            box-sizing: border-box;
        }
        .download-btn:hover {
            background: #1e4d7a;
            color: white;
            text-decoration: none;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        .empty-state .icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #2e6da4;
            text-decoration: none;
            font-weight: 500;
        }
        .back-link:hover {
            color: #1e4d7a;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/fwyytool/index" class="back-link">← 返回主页</a>
        
        <div class="header">
            <h1>📦 插件文件列表</h1>
            <p>以下是所有可下载的插件文件，点击下载按钮即可获取文件</p>
        </div>

        <div id="file-list">
            <div class="empty-state">
                <div class="icon">⏳</div>
                <h3>正在加载文件列表...</h3>
                <p>请稍候，正在获取可用的插件文件</p>
            </div>
        </div>
    </div>

    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script>
        $(document).ready(function() {
            loadFileList();
        });

        function loadFileList() {
            $.ajax({
                url: '/fwyytool/common/download/list',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    displayFiles(response.files || []);
                },
                error: function() {
                    displayError();
                }
            });
        }

        function displayFiles(files) {
            const container = $('#file-list');
            
            if (files.length === 0) {
                container.html(`
                    <div class="empty-state">
                        <div class="icon">📭</div>
                        <h3>暂无可下载文件</h3>
                        <p>当前没有可用的插件文件，请联系管理员添加</p>
                    </div>
                `);
                return;
            }

            const fileGrid = $('<div class="file-grid"></div>');
            
            files.forEach(function(file) {
                const fileCard = $(`
                    <div class="file-card">
                        <div class="file-icon">📦</div>
                        <div class="file-name">${file.name}</div>
                        <div class="file-info">
                            <div>大小: ${formatFileSize(file.size)}</div>
                            <div>修改时间: ${file.modTime}</div>
                        </div>
                        <a href="/fwyytool/common/download/plugin?file=${encodeURIComponent(file.name)}" 
                           class="download-btn" target="_blank">
                            ⬇️ 下载文件
                        </a>
                    </div>
                `);
                fileGrid.append(fileCard);
            });
            
            container.html(fileGrid);
        }

        function displayError() {
            $('#file-list').html(`
                <div class="empty-state">
                    <div class="icon">❌</div>
                    <h3>加载失败</h3>
                    <p>无法获取文件列表，请刷新页面重试</p>
                    <button onclick="loadFileList()" style="margin-top: 15px; padding: 10px 20px; background: #2e6da4; color: white; border: none; border-radius: 5px; cursor: pointer;">重新加载</button>
                </div>
            `);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
