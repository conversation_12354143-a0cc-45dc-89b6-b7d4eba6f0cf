{{ define "genke/attend/detail.html"}}
<!DOCTYPE HTML>
<head>
    <title>跟课学生到课 - 课程配置详情</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css"/>

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
        vertical-align:middle;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }
    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }
</style>


<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">到课信息</h3>
    <div class="form-group">
        <label for="lessonID">lessonID{{.param.LessonID}}：</label>
        <input type="text" class="form-control" id="lessonID" placeholder="lessonID" name="lessonID"
               {{if ne .params.LessonID 0}} value="{{.params.LessonID}}" {{end}}>
        <label for="studentUid">学生UID{{.param.StudentUid}}：</label>
        <input type="text" class="form-control" id="studentUid" placeholder="学生uid" name="studentUid"
               {{if ne .params.StudentUid 0}} value="{{.params.StudentUid}}" {{end}}>
    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #ac2925">{{.error}}</label>
</form>
{{if ne .data.CourseID 0}}
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='10' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>
                课程信息
            </th>
        </tr>
        <tr style="background-color: #d9d9d9">
            <th>章节名称</th>
            <th>课程id</th>
            <th>RoomID</th>
            <th>老师UID - 老师姓名</th>
            <th>服务状态</th>

        </tr>
        <tr style="height: 46px">
            <td style="color: tomato">{{.data.LessonName}}</td>
            <td>{{.data.CourseID}}</td>
            <td>{{.data.LiveRoomId}}</td>
            <td>{{.data.TeacherUid}} - {{.data.TeacherName}}</td>
            <td>{{.data.AttentStatusString}}</td>
        </tr>
    </table>
</div>

<div  style="overflow-y: scroll;  margin-top: 8px;margin-right: 10px">
    <h3>到课记录</h3>
    <table class="table table-bordered table-striped table-hover taskList" style="font-size:16px;">
        {{range $key, $attendTime := .data.AttendTimeData}}
        <tr style="height: 46px; font-size: 14px;">
            <td>{{$attendTime.Explain}}</td>
        </tr>
        {{end}}
    </table>
</div>

<div style="overflow-y: scroll;  margin-top: 8px;margin-right: 10px">
    <h3>直播间进出打点</h3>
    <table class="table table-bordered table-striped table-hover taskList" style="font-size:16px;">
        {{range $key, $attendTime := .data.AttendOutInTimeData}}
        <tr style="height: 46px; font-size: 14px;">
            <td>{{$attendTime}}</td>
        </tr>
        {{end}}
    </table>
</div>
<div id="dataJson" style="display:none;">
    {{.dataJson}}
</div>
{{else}}
<div style="height:34px;padding:6px;font-size:16px;text-align:center;background-color: darkred;color:white">没有查到课程</div>
{{end}}
{{end}}