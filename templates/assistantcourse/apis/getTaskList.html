{{ define "assistantcourse/apis/getTaskList.html" }}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>获取自主建课任务信息</title>

    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?12223"/>

    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/fwyytool/assets/js/bootstrap.min.js"></script>
    <script src="/fwyytool/assets/layer/layer.js"></script>
    <style>
        table.taskList th {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #dedede;
        }

        table.taskList td {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #ffffff;
        }

        div {
            margin-left: 10px;
        }

        .nav {
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
        }

        ol, ul {
            margin-top: 0;
        }

        a {
            color: #555;
            font-size: 16px;
        }

        .active {
            border-bottom: 3px solid #409c19;
            font-weight: bolder;
        }
        textarea {
            resize: both; /* 允许调整大小 */
            overflow: hidden; /* 防止内容溢出 */
            min-width: 100px; /* 最小宽度 */
            min-height: 50px; /* 最小高度 */
            max-width: 100%; /* 最大宽度 */
            width: 100%; /* 宽度100% */
            height: 200px; /* 高度自适应 */
            box-sizing: border-box; /* 包含padding和border在内的总宽度 */
            padding: 10px; /* 内边距 */
        }
    </style>
</head>
<body>
<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">自主建课任务信息</h3>
    <div class="form-group">
        <label for="personUid">真人ID：</label>
        <input type="text" class="form-control" id="personUid" placeholder="请输入真人ID" name="personUid"
               {{if ne .params.PersonUid 0}} value="{{.params.PersonUid}}" {{end}}>
        <select name="selectType" class="form-control" >
            <option value="0" {{if eq .params.SelectType 0}} selected {{end}}>课程任务</option>
            <option value="1" {{if eq .params.SelectType 1}} selected {{end}} >CPU任务</option>
        </select>
        <select name="status" class="form-control" >
            <option value="0" {{if eq .params.Status 0}} selected {{end}}>所有</option>
            <option value="1" {{if eq .params.Status 1}} selected {{end}} >仅失败</option>
        </select>
    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #524848">{{.errMsg}}</label>
</form>

{{if ne (len .data.CourseList) 0}}
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='10' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>
                课程任务信息
            </th>
        </tr>
        <tr style="background-color: #d9d9d9">
            <th>任务类型</th>
            <th>任务ID</th>
            <th>关联任务ID</th>
            <th>课程ID</th>
            <th>课程名称</th>
            <th>当前状态</th>
            <th>处理时间</th>
            <th>cpu任务id</th>
            <th>失败原因</th>
            <th>操作人</th>
        </tr>
        {{range $key,$course := .data.CourseList}}
            <tr style="height: 46px">
                <td>{{$course.TypeName}}</td>
                <td>{{$course.Id}}</td>
                <td>{{$course.TaskId}}</td>
                <td>{{$course.CourseId}}</td>
                <td>{{$course.CourseName}}</td>
                <td>{{$course.Current}}</td>
                <td>{{$course.ProcessTime}}</td>
                <td>{{$course.CpuTaskId}}</td>
                <td>{{$course.ErrInfo}}</td>
                <td>{{$course.OpName}}</td>
            </tr>
        {{end}}
    </table>
</div>
{{end}}

{{if ne (len .data.CpuList) 0}}
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='10' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>
                CPU任务信息
            </th>
        </tr>
        <tr style="background-color: #d9d9d9">
            <th>任务ID</th>
            <th>关联任务ID</th>
            <th>CpuID</th>
            <th>Cpu名称</th>
            <th>当前状态</th>
            <th>创建时间</th>
            <th>大纲时间</th>
            <th>发布时间</th>
            <th>失败原因</th>
            <th>操作人</th>
        </tr>
        {{range $key,$cpu := .data.CpuList}}
        <tr style="height: 46px">
            <td>{{$cpu.Id}}</td>
            <td>{{$cpu.CpuTaskId}}</td>
            <td>{{$cpu.CpuId}}</td>
            <td>{{$cpu.CpuName}}</td>
            <td>{{$cpu.Current}}</td>
            <td>{{$cpu.CpuCreateTime}}</td>
            <td>{{$cpu.CpuOutlinesTime}}</td>
            <td>{{$cpu.CpuPublishTime}}</td>
            <td>{{$cpu.ErrInfo}}</td>
            <td>{{$cpu.OpName}}</td>
        </tr>
        {{end}}
    </table>
</div>
{{end}}
<div style="display:none;">
    {{.dataJson}}
</div>
<script>

</script>
</body>
</html>
{{ end }}