{{ define "newgoodsplatform/skucourse/getCourseInfoBySkuId.html" }}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>商品Id获取对应课程信息</title>

    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?12223"/>
    <link rel="stylesheet" href="/fwyyyool/assets/css/index.css">

    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/fwyytool/assets/js/bootstrap.min.js"></script>
    <script src="/fwyytool/assets/layer/layer.js"></script>
    <script src="/fwyytool/assets/js/tool.js"></script>
    <style>
        table th {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #dedede;
        }

        table td {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #ffffff;
        }

        .tb-header {
            background-color: #d9d9d9;
            height: 46px;
            border: solid 1px #00a9ff !important;
        }

        div {
            margin-left: 10px;
        }

    </style>
</head>
<body>
<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">商品Id获取课程信息</h3>
    <div class="form-group">
        <label for="skuId">商品Id：</label>
        <input type="text" class="form-control" id="skuId" placeholder="请输入商品Id" name="skuId"
               {{if ne .params.SkuId 0}} value="{{.params.SkuId}}" {{end}}>
    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #FD482C">{{.errMsg}}</label>
</form>

{{if ne (len .data.List) 0}}
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    {{range $idx, $courseInfo := .data.List}}
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='2' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>课程信息</th>
        </tr>
        <tr>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #2e6da4;color:white'><span>课程属性</span></th>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #2e6da4;color:white'><span>属性值</span></th>
        </tr>
        <tr>
            <td class="tb-header">课程Id</td>
            <td>{{$courseInfo.CourseId}}</td>
        </tr>
        <tr>
            <td class="tb-header">课程名称</td>
            <td>{{$courseInfo.CourseName}}</td>
        </tr>
        <tr>
            <td class="tb-header">课程类型[NewCourseType]</td>
            <td>{{$courseInfo.NewCourseTypeDesc}}</td>
        </tr>
        <tr>
            <td class="tb-header">年级</td>
            <td>{{$courseInfo.MainGradeDesc}}</td>
        </tr>
        <tr>
            <td class="tb-header">学科</td>
            <td>{{$courseInfo.MainSubjectDesc}}</td>
        </tr>
        <tr>
            <td class="tb-header">学年</td>
            <td>{{$courseInfo.YearDesc}}</td>
        </tr>
        <tr>
            <td class="tb-header">学季期次</td>
            <td>{{$courseInfo.LearnSeasonDesc}}</td>
        </tr>
        <tr>
            <td class="tb-header">课程开始时间</td>
            <td>{{$courseInfo.StartTimeDesc}}</td>
        </tr>
        <tr>
            <td class="tb-header">课程结束时间</td>
            <td>{{$courseInfo.StopTimeDesc}}</td>
        </tr>
        <tr>
            <td class="tb-header">SkuId</td>
            <td>{{$courseInfo.SkuId}}</td>
        </tr>
        <tr>
            <td class="tb-header">上架状态</td>
            <td>{{$courseInfo.IsOnSaleDesc}}</td>
        </tr>
        <tr>
            <td class="tb-header">内部测试/正式课</td>
            <td>{{$courseInfo.IsInnerDesc}}</td>
        </tr>
        <tr>
            <td class="tb-header">主讲老师</td>
            <td>
                {{range $idx, $teacherDesc := $courseInfo.TeachersDesc}}
                    {{$teacherDesc}}<br />
                {{end}}
            </td>
        </tr>
        <tr>
            <td class="tb-header">辅导老师资产真人</td>
            <td>
                {{range $idx, $apDesc := $courseInfo.AssistantPersonDesc}}
                    {{$apDesc}}<br />
                {{end}}
            </td>
        </tr>
    </table>

    <br />
    {{end}}
</div>
{{end}}
<div style="display:none;">
    {{.dataJson}}
</div>
</body>
</html>
{{ end }}