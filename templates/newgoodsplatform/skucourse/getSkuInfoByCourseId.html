{{ define "newgoodsplatform/skucourse/getSkuInfoByCourseId.html" }}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>课程Id获取对应商品信息</title>

    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?12223"/>
    <link rel="stylesheet" href="/fwyyyool/assets/css/index.css">

    <script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/fwyytool/assets/js/bootstrap.min.js"></script>
    <script src="/fwyytool/assets/layer/layer.js"></script>
    <script src="/fwyytool/assets/js/tool.js"></script>
    <style>
        table th {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #dedede;
        }

        table td {
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #666666;
            background-color: #ffffff;
        }

        .tb-header {
            background-color: #d9d9d9;
            height: 46px;
            border: solid 1px #00a9ff !important;
        }

        div {
            margin-left: 10px;
        }

    </style>
</head>
<body>
<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">课程Id获取对应商品信息</h3>
    <div class="form-group">
        <label for="courseId">课程Id：</label>
        <input type="text" class="form-control" id="courseId" placeholder="请输入课程Id" name="courseId"
               {{if ne .params.CourseId 0}} value="{{.params.CourseId}}" {{end}}>
    </div>
    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #FD482C">{{.errMsg}}</label>
</form>

{{if ne (len .data.List) 0}}
<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    {{range $idx, $skuInfo := .data.List}}
    <table class='table table-bordered table-striped table-hover' style="font-size:16px;">
        <tr>
            <th colspan='2' style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>商品信息</th>
        </tr>
        <tr>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #2e6da4;color:white'><span>商品属性</span></th>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #2e6da4;color:white'><span>属性值</span></th>
        </tr>
        <tr>
            <td class="tb-header">商品Id[SkuId]</td>
            <td>{{$skuInfo.SkuId}}</td>
        </tr>
        <tr>
            <td class="tb-header">商品名称[SkuName]</td>
            <td>{{$skuInfo.SkuName}}</td>
        </tr>
        <tr>
            <td class="tb-header">业务线Id[Source]</td>
            <td>{{$skuInfo.Source}}</td>
        </tr>
        <tr>
            <td class="tb-header">业务线名称[SourceName]</td>
            <td>{{$skuInfo.SourceName}}</td>
        </tr>
        <tr>
            <td class="tb-header">商品分类Id[Category]</td>
            <td>{{$skuInfo.Category}}</td>
        </tr>
        <tr>
            <td class="tb-header">商品分类名称[CategoryName]</td>
            <td>{{$skuInfo.CategoryName}}</td>
        </tr>
        <tr>
            <td class="tb-header">售价（单位：分）[Price]</td>
            <td>{{$skuInfo.Price}}</td>
        </tr>
        <tr>
            <td class="tb-header">原价（单位：分）[SkuOriginPrice]</td>
            <td>{{$skuInfo.SkuOriginPrice}}</td>
        </tr>
        <tr>
            <td class="tb-header">商品上架状态（1=上架，0=下架）[OnSaleStatus]</td>
            <td>{{$skuInfo.OnSaleStatus}}</td>
        </tr>
        <tr>
            <td class="tb-header">商品上架时间[OnSaleStartTime]</td>
            <td>{{$skuInfo.OnSaleStartTime}}</td>
        </tr>
        <tr>
            <td class="tb-header">商品下架时间[OnSaleStopTime]</td>
            <td>{{$skuInfo.OnSaleStopTime}}</td>
        </tr>
        <tr>
            <td class="tb-header">内外部商品（1=内部，0=外部）[IsInner]</td>
            <td>{{$skuInfo.IsInner}}</td>
        </tr>
        <tr>
            <td class="tb-header">是否组合品[CombinationType]</td>
            <td>{{$skuInfo.CombinationType}}</td>
        </tr>
        <tr>
            <td class="tb-header">SPUId[SpuId]</td>
            <td>{{$skuInfo.SpuId}}</td>
        </tr>
        <tr>
            <td class="tb-header">商品类型[SkuMode]</td>
            <td>{{$skuInfo.SkuMode}}</td>
        </tr>
        <tr>
            <td class="tb-header">店铺Id[ShopId]</td>
            <td>{{$skuInfo.ShopId}}</td>
        </tr>
        <tr>
            <td class="tb-header">商品属性[AttributeTags]</td>
            <td><pre id="json-container-attr" class="json-container">{{$skuInfo.AttributeTags}}</pre></td>
        </tr>
        <tr>
            <td class="tb-header">商品规格[SpecTags]</td>
            <td><pre id="json-container-spec" class="json-container">{{$skuInfo.SpecTags}}</pre></td>
        </tr>
        <tr>
            <td class="tb-header">商品标签[LabelTags]</td>
            <td><pre id="json-container-label" class="json-container">{{$skuInfo.LabelTags}}</pre></td>
        </tr>
    </table>

    <br />
    {{end}}
</div>
{{end}}
<div style="display:none;">
    {{.dataJson}}
</div>
</body>
<script>
    let jcAttr = "json-container-attr";
    let jcSpec = "json-container-spec"
    let jcLabel = "json-container-label"

    highlightJson(document.getElementById(jcAttr).innerText, jcAttr)
    highlightJson(document.getElementById(jcSpec).innerText, jcSpec)
    highlightJson(document.getElementById(jcLabel).innerText, jcLabel)
</script>
</html>
{{ end }}