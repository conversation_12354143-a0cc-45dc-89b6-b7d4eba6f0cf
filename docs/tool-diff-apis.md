# 接口签名信息

## 概述

本文档提供了三个数据差异对比相关的 API 接口签名信息，方便其他模块进行调用。

## 1. 获取差异统计数量

### 接口信息
- **路径**: `GET /api/tool/getdiffcount`
- **描述**: 按接口名称分组统计差异对比的数量信息

### 请求参数
```go
type GetArkStudentDataDiffResParam struct {
    Handler      string   `json:"handler" form:"handler"`           // 单个接口名（已废弃，建议使用HandlerNames）
    StartTime    int64    `json:"startTime" form:"startTime"`       // 开始时间戳（Unix时间戳）
    EndTime      int64    `json:"endTime" form:"endTime"`           // 结束时间戳（Unix时间戳）
    Search       string   `json:"search" form:"search"`             // 模糊搜索：接口名、请求参数、指纹
    Status       []int    `json:"status" form:"status"`             // 状态过滤：0-未完成，1-已完成，2-失败
    HandlerNames []string `json:"handlerNames" form:"handlerNames"` // 接口名多选过滤
    HasDiff      *int     `json:"hasDiff" form:"hasDiff"`           // 是否存在差异：0-无差异，1-有差异
    SortBy       string   `json:"sortBy" form:"sortBy"`             // 排序字段
    SortOrder    string   `json:"sortOrder" form:"sortOrder"`       // 排序方向：asc, desc
    Page         int      `json:"page" form:"page"`                 // 页码，从1开始
    PageSize     int      `json:"pageSize" form:"pageSize"`         // 每页大小，默认20
}
```

### 响应数据
```go
type GetAPIDiffCountOutPutItem struct {
    AllCnt      int64 `json:"all"`           // 总数量
    HasDiffCnt  int64 `json:"hasDiffCnt"`    // 有差异数量
    NoDiffCnt   int64 `json:"noDiffCnt"`     // 无差异数量
    UnFinishCnt int64 `json:"unFinishCnt"`   // 未完成任务数量
    FailedCun   int64 `json:"failedCnt"`     // 失败数量
}
```

**返回格式**: `map[string]GetAPIDiffCountOutPutItem`
- key: `handler_name`（接口名称）
- value: 对应的统计数据

### 调用示例
```bash
curl -X GET "http://your-domain/api/tool/getdiffcount?startTime=1634567890&endTime=1634654290&status=1"
```

## 2. 获取差异结果列表

### 接口信息
- **路径**: `GET /api/tool/getdiffres`
- **描述**: 获取详细的差异对比结果列表，支持分页和多种过滤条件

### 请求参数
同获取差异统计数量接口

### 响应数据
```go
type GetArkStudentDataDiffResOutPut struct {
    Total        int64                                     `json:"total"`        // 总记录数
    DataDiffList []*GetArkStudentDataDiffResOutPutDataDiff `json:"dataDiffList"` // 差异数据列表
}

type GetArkStudentDataDiffResOutPutDataDiff struct {
    Params      string `json:"params"`       // 请求参数（JSON字符串）
    DiffNum     int    `json:"diffNum"`      // 差异数量
    OldData     string `json:"oldData"`      // 旧数据（JSON字符串）
    NewData     string `json:"newData"`      // 新数据（JSON字符串）
    DiffResult  string `json:"diffResult"`   // 差异结果文件名
    HandlerName string `json:"handler_name"` // 接口名
    CreateTime  int64  `json:"createTime"`   // 创建时间（Unix时间戳）
    UpdateTime  int64  `json:"updateTime"`   // 更新时间（Unix时间戳）
}
```

### 调用示例
```bash
curl -X GET "http://your-domain/api/tool/getdiffres?startTime=1634567890&endTime=1634654290&handlerNames=getUserInfo,getStudentList&page=1&pageSize=20"
```

## 3. 获取差异总览

### 接口信息
- **路径**: `GET /api/tool/getdiffoverview`
- **描述**: 获取差异对比的总体统计信息

### 请求参数
同获取差异统计数量接口

### 响应数据
```go
type GetArkStudentDataDiffOverviewOutPut struct {
    HasDiffNum     int64 `json:"hasDiffNum"`     // 有差异数量（已完成且有差异）
    NoDiffNum      int64 `json:"noDiffNum"`      // 无差异数量（已完成且无差异）
    UnFinishedTask int64 `json:"unFinishedTask"` // 未完成任务数量
    Total          int64 `json:"total"`          // 总数量
}
```

### 调用示例
```bash
curl -X GET "http://your-domain/api/tool/getdiffoverview?startTime=1634567890&endTime=1634654290"
```

## 通用参数说明

### 时间参数
- `startTime` 和 `endTime` 是 Unix 时间戳
- 如果不传这两个参数，默认查询最近 24 小时的数据

### 分页参数
- `page`: 从 1 开始
- `pageSize`: 默认 20，最大 100

### 状态值说明
- `0`: 未完成
- `1`: 已完成
- `2`: 失败

### 差异过滤
- `HasDiff`: 
  - `0`: 无差异
  - `1`: 有差异
  - `nil`: 不过滤

### 排序字段
- `createTime`: 创建时间
- `updateTime`: 更新时间
- `diffNum`: 差异数量
- 其他字段：默认按 ID 排序

## Go 调用示例

```go
package main

import (
    "context"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "net/http"
    "net/url"
    "time"
)

// 调用API的通用函数
func callAPI(baseURL, endpoint string, params map[string]interface{}) ([]byte, error) {
    u, err := url.Parse(baseURL + endpoint)
    if err != nil {
        return nil, err
    }
    
    q := u.Query()
    for k, v := range params {
        switch val := v.(type) {
        case string:
            q.Set(k, val)
        case int:
            q.Set(k, fmt.Sprintf("%d", val))
        case int64:
            q.Set(k, fmt.Sprintf("%d", val))
        case []int:
            for _, i := range val {
                q.Add(k, fmt.Sprintf("%d", i))
            }
        case []string:
            for _, s := range val {
                q.Add(k, s)
            }
        }
    }
    u.RawQuery = q.Encode()
    
    resp, err := http.Get(u.String())
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    return ioutil.ReadAll(resp.Body)
}

func main() {
    baseURL := "http://your-domain/api/tool"
    
    // 1. 获取差异统计数量
    countParams := map[string]interface{}{
        "startTime": time.Now().Add(-24 * time.Hour).Unix(),
        "endTime":   time.Now().Unix(),
        "status":    []int{1},
    }
    
    countData, err := callAPI(baseURL, "/getdiffcount", countParams)
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        return
    }
    
    var countResult map[string]map[string]int64
    json.Unmarshal(countData, &countResult)
    fmt.Printf("差异统计数量: %+v\n", countResult)
    
    // 2. 获取差异结果列表
    listParams := map[string]interface{}{
        "startTime":    time.Now().Add(-24 * time.Hour).Unix(),
        "endTime":      time.Now().Unix(),
        "handlerNames": []string{"getUserInfo", "getStudentList"},
        "page":         1,
        "pageSize":     20,
    }
    
    listData, err := callAPI(baseURL, "/getdiffres", listParams)
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        return
    }
    
    var listResult struct {
        Total        int64 `json:"total"`
        DataDiffList []struct {
            HandlerName string `json:"handler_name"`
            DiffNum     int    `json:"diffNum"`
            CreateTime  int64  `json:"createTime"`
        } `json:"dataDiffList"`
    }
    json.Unmarshal(listData, &listResult)
    fmt.Printf("差异结果列表: 总数=%d, 首条数据=%+v\n", listResult.Total, listResult.DataDiffList[0])
    
    // 3. 获取差异总览
    overviewParams := map[string]interface{}{
        "startTime": time.Now().Add(-24 * time.Hour).Unix(),
        "endTime":   time.Now().Unix(),
    }
    
    overviewData, err := callAPI(baseURL, "/getdiffoverview", overviewParams)
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        return
    }
    
    var overviewResult struct {
        HasDiffNum     int64 `json:"hasDiffNum"`
        NoDiffNum      int64 `json:"noDiffNum"`
        UnFinishedTask int64 `json:"unFinishedTask"`
        Total          int64 `json:"total"`
    }
    json.Unmarshal(overviewData, &overviewResult)
    fmt.Printf("差异总览: %+v\n", overviewResult)
}
```

## 注意事项

1. **差异结果文件**: `DiffResult` 字段返回的是文件名，需要通过文件服务获取实际内容
2. **参数兼容性**: `Handler` 参数已废弃，建议使用 `HandlerNames` 进行多接口查询
3. **时间范围**: 默认查询最近24小时数据，可根据需要调整
4. **数据格式**: `Params`、`OldData`、`NewData` 字段存储的是 JSON 字符串
5. **错误处理**: 所有接口返回统一的错误格式，调用方需要处理错误情况

## 错误响应格式

```json
{
    "code": -1,
    "message": "错误信息",
    "data": null
}
```

成功响应格式：
```json
{
    "code": 0,
    "message": "success",
    "data": {
        // 具体的响应数据
    }
}
```