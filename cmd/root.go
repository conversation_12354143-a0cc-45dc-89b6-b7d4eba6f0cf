package cmd

import (
	"fwyytool/cmd/toolkits"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
)

// RegisterCommand register command on the root command instance.
func RegisterCommand(engine *gin.Engine, cmd *cobra.Command) {
	//定时任务相关 go run main.go cron my-cron-job
	var cronCmd = &cobra.Command{
		Use:   "cron",
		Short: "crontab jobs",
	}

	//commands here
	cronCmd.AddCommand()

	cmd.AddCommand(cronCmd)

	//工具相关的脚本 go run main.go toolkit  some-tools
	var toolkitCmd = &cobra.Command{
		Use:   "toolkit",
		Short: "toolkits for development and something else.",
	}
	//commands here
	toolkitCmd.AddCommand(toolkits.ModelGenCommand(engine))

	cmd.AddCommand(toolkitCmd)
}
