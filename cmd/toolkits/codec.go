package toolkits

import (
	"fwyytool/helpers"
	"fwyytool/libs/codec"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
)

// ModelGenCommand return cobra.Command instance.
// 用来初始化补录信息表新增的字段，origin_category、props
func ModelGenCommand(engine *gin.Engine) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "gen-model",
		Short: "generate model file from the table",
		Args:  cobra.MinimumNArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			ctx := gin.CreateNewContext(engine)
			helpers.Job.RunSync(ctx, func(ctx *gin.Context) error {
				for _, tableName := range args {
					_ = codec.GenerateModel(ctx, helpers.MysqlClient, tableName)
				}
				return nil
			})
		},
	}
	return cmd
}
