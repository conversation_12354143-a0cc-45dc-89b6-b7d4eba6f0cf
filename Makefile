MODULE = $(shell go list -m)
VERSION ?= $(shell git describe --tags --always --dirty --match=v* 2> /dev/null || echo "1.0.0")
PACKAGES := $(shell go list ./... | grep -v /vendor/)
gitTag = $(shell if [ "`git describe --tags --abbrev=0 2>/dev/null`" != "" ];then git describe --tags --abbrev=0; else git log --pretty=format:'%h' -n 1; fi)
buildDate = $(shell TZ=Asia/Shanghai date +%FT%T%z)
gitCommit = $(shell git log --pretty=format:'%H' -n 1)
gitTreeState = $(shell if git status|grep -q 'clean';then echo clean; else echo dirty; fi)
LDFLAGS := -ldflags "-s -w -X main.Version=${VERSION} -X ${MODULE}/pkg/version.buildDate=${buildDate} -X ${MODULE}/pkg/version.gitTag=${gitTag} -X ${MODULE}/pkg/version.gitCommit=${gitCommit} -X ${MODULE}/pkg/version.gitTreeState=${gitTreeState}"
GOFILES := $(shell find . -name "*.go")
GOFMT ?= gofmt "-s"

.PHONY: default
default: help

# generate help info from comments: thanks to https://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
.PHONY: help
help: ## help information about make commands
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

.PHONY: test
test: ## run unit tests
	@echo "mode: count" > coverage-all.out
	@$(foreach pkg,$(PACKAGES), \
		go test -p=1 -cover -covermode=count -coverprofile=coverage.out ${pkg}; \
		tail -n +2 coverage.out >> coverage-all.out;)

.PHONY: test-cover
test-cover: test ## run unit tests and show test coverage information
	go tool cover -html=coverage-all.out

.PHONY: clean
clean: ## remove temporary files
	rm -rf coverage.out coverage-all.out dist output log

.PHONY: version
version: ## display the version of the API server
	@echo $(VERSION)

.PHONY: fmt
fmt: ## run "go fmt" on all Go packages
	@go fmt $(PACKAGES)

.PHONY: vet
vet: ## run "go vet" on all Go packages
	@go vet -composites=false $(PACKAGES)

.PHONY: fmt-check ## run format check
fmt-check:
	@diff=$$($(GOFMT) -d $(GOFILES)); \
	if [ -n "$$diff" ]; then \
		echo "Please run 'make fmt' and commit the result:"; \
		echo "$${diff}"; \
		exit 1; \
	fi;

.PHONY: init
init: ## initialize
	@echo "Install pre-commit hook"
	@ln -s $(shell pwd)/githooks/pre-commit $(shell pwd)/.git/hooks/pre-commit || true
	@chmod +x .git/hooks/pre-commit

.PHONY: wire
wire: ## wire generate
	@wire gen ./...

.PHONY: linit
lint: ## run golangci lint
	@golangci-lint run --timeout=5m

update:
	go get -u git.zuoyebang.cc/mark/mklib@master
	go mod tidy
