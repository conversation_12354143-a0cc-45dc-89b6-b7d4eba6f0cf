package utils

import (
	"context"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/middleware"

	"fwyytool/libs/json"
)

func TestProxyServiceCallHandler(t *testing.T) {
	engine := gin.New()
	engine.Use(middleware.AccessLog(middleware.LoggerConfig{}))

	rg := engine.Group("")
	rg.GET("/hello", ProxyServiceCallHandler(func(ctx *gin.Context, params map[string]interface{}) (*base.ApiResult, error) {
		assert.Contains(t, params, "name")

		params["add"] = 1

		r := base.DefaultRender{
			ErrNo:  0,
			ErrMsg: "OK",
			Data:   params,
		}
		b, _ := json.Marshal(r)

		ctx.Header("x-logid", "12311231")

		return &base.ApiResult{
			HttpCode: 200,
			Response: b,
			Ctx:      ctx,
		}, nil
	}))

	req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, "/hello?name=light", strings.NewReader(`{"age":12}`))
	rw := httptest.NewRecorder()

	engine.ServeHTTP(rw, req)

	assert.Equal(t, 200, rw.Code)
	b, _ := ioutil.ReadAll(rw.Body)
	var resp base.DefaultRender
	_ = json.Unmarshal(b, &resp)
	assert.Equal(t, 0, resp.ErrNo)
	assert.Contains(t, resp.Data, "add")
	t.Logf("response: %#v", resp)
	t.Log(rw.Header())
}

func TestNewReaderFromRemoteFile(t *testing.T) {
	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	r, err := NewReaderFromRemoteFile(ctx, "https://zyb-kunpeng-mkstatic-1253445850.cos.ap-beijing.myqcloud.com/mkstatic_f05c684d5e6675031c7b3dc15738a670.png")
	assert.Nil(t, err)

	b, _ := ioutil.ReadAll(r)
	t.Log(string(b))
}
