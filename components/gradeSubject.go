package components

//定义学段信息
const (
	DepartXiaoXue  = 1
	DepartChuZhong = 20
	DepartGaoZhong = 30
	DepartXueQian  = 60
	DepartChengRen = 70
	DepartDiYou    = 90
)

var DepartNameMap = map[int]string{
	DepartXiaoXue:  "小学",
	Depart<PERSON><PERSON><PERSON><PERSON>: "初中",
	Depart<PERSON><PERSON><PERSON><PERSON>: "高中",
	DepartXueQian:  "学前",
	DepartChengRen: "成人",
	DepartDiYou:    "低幼",
}

// GradeDepartMap 年级映射学段
var GradeDepartMap = map[int]int{
	1:  DepartXiaoXue,
	11: DepartXiaoXue,
	12: DepartXiaoXue,
	13: <PERSON>part<PERSON><PERSON>o<PERSON><PERSON>,
	14: DepartXiaoXue,
	15: DepartXiaoXue,
	16: Depart<PERSON>iao<PERSON><PERSON>,

	2:  Depar<PERSON><PERSON><PERSON><PERSON><PERSON>,
	3:  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	4:  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	20: <PERSON>par<PERSON><PERSON><PERSON><PERSON><PERSON>,
	5:  <PERSON>par<PERSON><PERSON><PERSON><PERSON><PERSON>,
	6:  Depart<PERSON><PERSON><PERSON><PERSON>,
	7:  Depar<PERSON><PERSON><PERSON><PERSON><PERSON>,
	30: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	31: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	32: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	33: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,

	60: DepartXueQian,
	61: DepartXueQian,
	62: DepartXueQian,
	63: DepartXueQian,
	64: DepartXueQian,

	70: DepartChengRen,
	71: DepartChengRen,

	90: DepartDiYou,
	91: DepartDiYou,
}

var SubjectNameMap = map[int]string{
	1:  "语文",
	2:  "数学",
	3:  "英语",
	4:  "物理",
	5:  "化学",
	6:  "生物",
	7:  "政治",
	8:  "历史",
	9:  "地理",
	10: "兴趣课",  //直播课使用
	11: "思想品德", //直播课使用
	12: "讲座",   //直播课使用
	13: "理综",   //试卷用
	14: "文综",   //试卷用
	15: "奥数",
	16: "科学",
	17: "口语",   //成人-实用英语
	18: "写作",   //成人-实用英语
	19: "阅读",   //成人-实用英语
	20: "词汇",   //成人-实用英语
	21: "语法",   //成人-实用英语
	22: "听力",   //成人-实用英语
	23: "综合",   //成人-实用英语
	24: "中级财会", //成人-中级财会
	25: "初级财会", //成人-财会
	26: "笔试",   //成人-教师资格证
	27: "面试",   //成人-教师资格证
	28: "省考",   //成人-公考
	29: "国考",   //成人-公考
	30: "普通话",
	31: "招录笔试",
	32: "CPA",
	33: "思维",
	34: "写字",
	35: "美术",
	36: "事业单位考试",
	37: "CMA",
	38: "税务师",
	39: "专业",
	40: "Scratch",
	41: "Python",
	42: "技术",
	43: "CPA",
	44: "副业",
	45: "兴趣",
	46: "职场",
	47: "口才",
	48: "道德与法治",
	49: "音乐",
	51: "信息技术",
	50: "美术",
	53: "素养",
	52: "思想政治",
	54: "记忆力",
	55: "逻辑力",
	56: "专注力",
	58: "围棋",
	57: "阅读力",
	59: "体能运动",
	60: "艺术素养",
	61: "思维逻辑",
	62: "科技创新",
	63: "语言文学",
	64: "传统文化",
	65: "社会实践",
	66: "人文",
	67: "语言",
	68: "家庭教育",
	69: "学习力",
	70: "大科学",
	71: "表达力",
	72: "体育与健康",
	73: "通用技术",
	74: "书法",
	75: "劳动",
	76: "日语",
	77: "西班牙语",
	78: "人文素养",
	79: "运动",
}
