package components

import "git.zuoyebang.cc/pkg/golib/v2/base"

// 4000000-4999999 参数检查错误
var ErrorParamInvalid = base.Error{
	ErrNo:  4000,
	ErrMsg: "param invalid",
}

// 5000000-5999999 内部逻辑错误
var ErrorSystemError = base.Error{
	ErrNo:  5000,
	ErrMsg: "system internal error",
}
var ErrorUserNotExist = base.Error{
	ErrNo:  5001,
	ErrMsg: "user not exist",
}
var ErrorHttpNotOk = base.Error{
	ErrNo:  5002,
	ErrMsg: "httpcode not 200",
}
var ErrorNoDutySchedule = base.Error{
	ErrNo:  5003,
	ErrMsg: "duty schedule does not exist",
}

// model层错误
var ErrorDbInsert = base.Error{
	ErrNo:  3101,
	ErrMsg: "db insert error: %s",
}
var ErrorDbUpdate = base.Error{
	ErrNo:  3102,
	ErrMsg: "db update error: %s",
}
var ErrorDbSelect = base.Error{
	ErrNo:  3103,
	ErrMsg: "db get error: %s",
}

// 第三方sdk错误

// redis
var ErrorRedisGet = base.Error{
	ErrNo:  3201,
	ErrMsg: "redis get error: %s",
}
var ErrorRedisSet = base.Error{
	ErrNo:  3202,
	ErrMsg: "redis set error: %s",
}

// hbase
var ErrorHbaseGetTableName = base.Error{
	ErrNo:  3301,
	ErrMsg: "get hbase table name error",
}
var ErrorHbaseQuery = base.Error{
	ErrNo:  3302,
	ErrMsg: "hbase query error",
}

// kafka
var ErrorKafkaPub = base.Error{
	ErrNo:  3401,
	ErrMsg: "kafka pub error",
}

// nmq
var ErrorNmqPub = base.Error{
	ErrNo:  3402,
	ErrMsg: "nmq pub error",
}

// es
var ErrorEsPing = base.Error{
	ErrNo:  3501,
	ErrMsg: "es ping error",
}
var ErrorEsGetVersion = base.Error{
	ErrNo:  3502,
	ErrMsg: "es getVersion error",
}
var ErrorEsInsert = base.Error{
	ErrNo:  3503,
	ErrMsg: "es insert error",
}
var ErrorEsQuery = base.Error{
	ErrNo:  3504,
	ErrMsg: "es query error",
}
var ErrorEsUpdate = base.Error{
	ErrNo:  3505,
	ErrMsg: "es update error",
}
var ErrorEsDel = base.Error{
	ErrNo:  3506,
	ErrMsg: "es del error",
}

// cos
var ErrorCosUpload = base.Error{
	ErrNo:  3600,
	ErrMsg: "cos upload error: %s",
}
var ErrorCosDownload = base.Error{
	ErrNo:  3602,
	ErrMsg: "cos download error: %s",
}
var ErrorCosGetData = base.Error{
	ErrNo:  3603,
	ErrMsg: "cos getMetaData error: %s",
}

var ErrorUserNotLogin = base.Error{
	ErrNo:  3700,
	ErrMsg: "user not login",
}

var ErrorUpmsPermissionDenied = base.Error{
	ErrNo:  3800,
	ErrMsg: "permission deneied",
}

var (
	ErrorMkTaskError = base.Error{ErrNo: 5527, ErrMsg: "任务信息异常"}
)

var (
	ErrorMarkupError = base.Error{ErrNo: 5800, ErrMsg: "任务暂无留痕配置，默认全题目留痕"}
)

type Error struct {
	ErrNo  int    `json:"errNo"`
	ErrStr string `json:"errStr"`
}

func (e Error) Error() string {
	return e.ErrStr
}

// SystemErr 系统错误
func SystemErr(msg string) base.Error {
	if msg == "" {
		msg = "system internal error"
	}
	return base.Error{
		ErrNo:  5000,
		ErrMsg: msg,
	}
}

// LoginErr 错误
func LoginErr(msg string) base.Error {
	if msg == "" {
		msg = "user not login"
	}
	return base.Error{
		ErrNo:  3700,
		ErrMsg: msg,
	}
}
