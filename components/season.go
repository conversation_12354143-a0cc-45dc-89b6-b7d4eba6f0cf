package components

import "strconv"

// 学期映射到学季
var fourSeasonMap = map[int]int{
	1:   1,
	11:  1,
	12:  1,
	13:  1,
	14:  1,
	15:  1,
	16:  1,
	17:  1,
	18:  1,
	19:  1,
	110: 1,
	111: 1,
	112: 1,
	113: 1,
	114: 1,
	115: 1,
	116: 1,
	117: 1,
	118: 1,
	119: 1,
	120: 1,
	121: 1,
	122: 1,
	123: 1,
	124: 1,
	125: 1,
	126: 1,
	127: 1,
	128: 1,
	129: 1,
	130: 1,

	2:   2,
	20:  2,
	21:  2,
	22:  2,
	23:  2,
	24:  2,
	25:  2,
	26:  2,
	27:  2,
	28:  2,
	29:  2,
	210: 2,
	211: 2,
	212: 2,
	213: 2,
	214: 2,
	215: 2,
	216: 2,
	217: 2,
	218: 2,
	219: 2,
	220: 2,
	221: 2,
	222: 2,
	223: 2,
	224: 2,
	225: 2,
	226: 2,
	227: 2,
	228: 2,
	229: 2,
	230: 2,

	3:   3,
	31:  3,
	32:  3,
	33:  3,
	34:  3,
	35:  3,
	36:  3,
	37:  3,
	38:  3,
	39:  3,
	310: 3,
	311: 3,
	312: 3,
	313: 3,
	314: 3,
	315: 3,
	316: 3,
	317: 3,
	318: 3,
	319: 3,
	320: 3,
	321: 3,
	322: 3,
	323: 3,
	324: 3,
	325: 3,
	326: 3,
	327: 3,
	328: 3,
	329: 3,
	330: 3,

	4:   4,
	40:  4,
	41:  4,
	42:  4,
	43:  4,
	44:  4,
	45:  4,
	46:  4,
	47:  4,
	48:  4,
	49:  4,
	410: 4,
	411: 4,
	412: 4,
	413: 4,
	414: 4,
	415: 4,
	416: 4,
	417: 4,
	418: 4,
	419: 4,
	420: 4,
	421: 4,
	422: 4,
	423: 4,
	424: 4,
	425: 4,
	426: 4,
	427: 4,
	428: 4,
	429: 4,
	430: 4,
}

var seasonMap = map[int]string{
	1: "春",
	2: "暑",
	3: "秋",
	4: "寒",
}

//暂时最多45个期次
const MAX_PERIOD = 45

func GetLearnSeanFromat(learnSeasnId int) string {
	if learnSeasnId <= 4 {
		return seasonMap[learnSeasnId]
	}
	periodMap := map[string]string{}
	for k, v := range seasonMap {
		for p := 1; p <= MAX_PERIOD; p++ {
			idx := strconv.Itoa(k) + strconv.Itoa(p)
			periodMap[idx] = v + strconv.Itoa(p)
		}
	}
	learnSeasnIdString := strconv.Itoa(learnSeasnId)
	if ret, ok := periodMap[learnSeasnIdString]; ok {
		return ret
	}
	return "未知期次"
}

func GetSeasonByLearnSeason(learnSeason int) int {
	return fourSeasonMap[learnSeason]
}
