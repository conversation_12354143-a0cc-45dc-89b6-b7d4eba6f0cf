package components

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"strconv"
	"strings"
)

var Array array

type array struct {
}

func (s array) InArrayInt(e int, array []int) bool {
	for _, element := range array {
		if e == element {
			return true
		}
	}

	return false
}

func (s array) InArrayInt64(e int64, array []int64) bool {
	for _, element := range array {
		if e == element {
			return true
		}
	}

	return false
}

func (s array) InArrayString(e string, array []string) bool {
	for _, element := range array {
		if e == element {
			return true
		}
	}

	return false
}

func (s array) ChunkArrayInt(array []int, size int) [][]int {
	if len(array) == 0 || size <= 0 {
		return nil
	}

	total := len(array) / size
	if len(array)%size != 0 {
		total++
	}

	result := make([][]int, 0, total)
	for i := 0; i < len(array); {
		stop := i + size
		if stop > len(array) {
			stop = len(array)
		}
		item := array[i:stop]
		result = append(result, item)

		i = stop
	}

	return result
}

func (s array) ConvertArrayIntToArrayString(input []int) []string {
	ret := make([]string, 0, len(input))
	for _, e := range input {
		ret = append(ret, strconv.Itoa(e))
	}

	return ret
}

func (s array) ConvertArrayInt64ToArrayString(input []int64) []string {
	ret := make([]string, 0, len(input))
	for _, e := range input {
		ret = append(ret, strconv.Itoa(int(e)))
	}
	return ret
}

func (s array) JoinArrayIntToString(input []int, seq string) string {
	strList := s.ConvertArrayIntToArrayString(input)
	return strings.Join(strList, seq)
}

func (s array) JoinArrayInt64ToString(input []int64, seq string) string {
	strList := s.ConvertArrayInt64ToArrayString(input)
	return strings.Join(strList, seq)
}

func (s array) ConvertArrayStringToArrayInt(input []string) ([]int, error) {
	ret := make([]int, 0, len(input))
	for _, e := range input {
		intVal, err := strconv.Atoi(e)
		if err != nil {
			return nil, err
		}

		ret = append(ret, intVal)
	}

	return ret, nil
}

func (s array) ConvertArrayFloat64ToArrayInt(input []float64) []int {
	ret := make([]int, 0, len(input))
	for _, e := range input {
		ret = append(ret, int(e))
	}

	return ret
}

func (s array) GetMinNum(ary []int) int {
	if len(ary) == 0 {
		return 0
	}

	minVal := ary[0]
	for i := 1; i < len(ary); i++ {
		if minVal > ary[i] {
			minVal = ary[i]
		}
	}

	return minVal
}

func (s array) UniqueInt(ids []int) []int {
	m := make(map[int]struct{})
	for _, id := range ids {
		m[id] = struct{}{}
	}

	result := make([]int, 0, len(m))
	for id := range m {
		result = append(result, id)
	}

	return result
}

func (s array) UniqueInt64(ids []int64) []int64 {
	m := make(map[int64]struct{})
	for _, id := range ids {
		m[id] = struct{}{}
	}

	result := make([]int64, 0, len(m))
	for id := range m {
		result = append(result, id)
	}

	return result
}

// 求交集
func (s array) IntSliceIntersect(a, b []int) []int {
	if 0 == len(a) || 0 == len(b) {
		return nil
	}
	aValMap := make(map[int]bool)
	for _, v := range a {
		aValMap[v] = true
	}

	result := make([]int, 0)
	for _, v := range b {
		if aValMap[v] {
			result = append(result, v)
		}
	}

	return result
}

func (s array) CalPage(items []int, pageSize int) int {
	pageNum := len(items) / pageSize
	if len(items)%pageSize > 0 {
		pageNum += 1
	}
	return pageNum
}

func (s array) StringArrUnique(arr []string) []string {
	ans := make([]string, 0)
	stringArrMap := map[string]struct{}{}
	for _, v := range arr {
		if _, ok := stringArrMap[v]; ok {
			continue
		}
		ans = append(ans, v)
		stringArrMap[v] = struct{}{}
	}
	return ans
}

// GroupColConcat 逐列拍平拼接
func (s array) GroupColConcat(arr [][]string, group int) [][]string {
	rows := len(arr)
	cols := len(arr[0])

	cn := cols * group
	rn := rows / group

	matrix := make([][]string, rn)
	for i := range matrix {
		matrix[i] = make([]string, cn)
	}

	for i := 0; i < group; i++ {
		start := i * (rows / group)
		end := (i+1)*(rows/group) - 1

		for r := start; r <= end; r++ {
			for c := 0; c < cols; c++ {
				rNew := r % (rows / group)
				cNew := r/(rows/group) + c*(group)
				matrix[rNew][cNew] = arr[r][c]
			}
		}
	}
	return matrix
}

// VerticalConcat 垂直拼接多个二维字符串数组
func (s array) VerticalConcat(arrays ...[][]string) ([][]string, error) {
	if len(arrays) == 0 {
		return nil, fmt.Errorf("no arrays provided")
	}

	// 获取第一个非空数组的列数，初始化列数为-1来标记
	numCols := -1
	for _, arr := range arrays {
		if len(arr) > 0 {
			numCols = len(arr[0])
			break
		}
	}

	// 如果所有数组都是空数组，返回空的结果数组
	if numCols == -1 {
		return [][]string{}, nil
	}

	// 验证所有数组列数是否相同
	for _, arr := range arrays {
		for _, row := range arr {
			if len(row) != numCols {
				return nil, fmt.Errorf("inconsistent number of columns")
			}
		}
	}

	// 拼接数组
	var result [][]string
	for _, arr := range arrays {
		for _, row := range arr {
			result = append(result, row)
		}
	}

	return result, nil
}

// ConcatStringArrays 按列拼接二维字符串数组，确保所有数组有相同的行数
func (s array) ConcatStringArrays(arrays ...[][]string) ([][]string, error) {
	// 检查是否没有提供任何数组
	if len(arrays) == 0 {
		return nil, fmt.Errorf("至少需要一个数组")
	}

	// 查找是否有非空数组以及确定行数
	var rowCount int
	for _, arr := range arrays {
		if len(arr) > 0 {
			if rowCount == 0 {
				rowCount = len(arr)
			} else if len(arr) != rowCount {
				return nil, fmt.Errorf("所有数组必须有相同的行数")
			}
		}
	}

	// 如果所有数组都是空的，则返回空结果
	if rowCount == 0 {
		return [][]string{}, nil
	}

	// 将结果数组初始化为合适的大小
	result := make([][]string, rowCount)

	// 拼接数组
	for i := 0; i < rowCount; i++ {
		for _, arr := range arrays {
			if len(arr) > 0 {
				result[i] = append(result[i], arr[i]...)
			}
		}
	}

	return result, nil
}

func AddPrefix(items []string, prefix string) []string {
	var result []string
	for _, item := range items {
		if item != "" {
			result = append(result, prefix+"_"+item)
		} else {
			result = append(result, item)
		}
	}
	return result
}

func UpdatePrefix(items []string, prefix, suffix string) []string {
	var result []string
	for _, field := range items {
		result = append(result, prefix+"_"+suffix+"_"+field)
	}
	return result
}

func GetKeys(m map[int64]int64) []int64 {
	keys := make([]int64, 0, len(m))
	for key := range m {
		keys = append(keys, key)
	}
	return keys
}

// ArrayIntersect 函数检查搜索字段是否在允许的字段列表中
func ArrayIntersect(searchFields []string, targetFields []string) bool {
	for _, field := range searchFields {
		if Array.InArrayString(field, targetFields) {
			return true
		}
	}
	return false
}

func ArrayDiff(slice1, slice2 []string) []string {
	m := make(map[string]bool)
	for _, item := range slice2 {
		m[item] = true
	}

	var diffSlice []string
	for _, item := range slice1 {
		if _, ok := m[item]; !ok {
			diffSlice = append(diffSlice, item)
		}
	}

	return diffSlice
}

func ChunkArrayInt64(array []int64, size int) [][]int64 {
	if len(array) == 0 || size <= 0 {
		return nil
	}

	total := len(array) / size
	if len(array)%size != 0 {
		total++
	}

	result := make([][]int64, 0, total)
	for i := 0; i < len(array); {
		stop := i + size
		if stop > len(array) {
			stop = len(array)
		}
		item := array[i:stop]
		result = append(result, item)

		i = stop
	}

	return result
}

func ConvertInt(ctx *gin.Context, interfaceArray []interface{}) []interface{} {
	res := make([]interface{}, 0)
	for _, value := range interfaceArray {
		num, err := strconv.Atoi(cast.ToString(value))
		if err != nil {
			res = append(res, value)
		} else {
			res = append(res, num)
		}
	}
	return res
}
