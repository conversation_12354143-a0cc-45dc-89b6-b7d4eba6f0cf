FROM image-docker.zuoyebang.cc/privbase/go-builder:1.19-alpine as builder

ARG APP_NAME
ENV APP_NAME=$APP_NAME

WORKDIR $GOPATH/${APP_NAME}/

COPY go.mod $GOPATH/${APP_NAME}/
COPY go.sum $GOPATH/${APP_NAME}/

RUN go mod download

ARG CI_COMMIT_SHA
COPY . $GOPATH/${APP_NAME}/
RUN go build -ldflags "-s -w -X 'git.zuoyebang.cc/mark/fwyytool/pkg/version.buildDate=$(date +%FT%T%z)' -X 'git.zuoyebang.cc/mark/fwyytool/pkg/version.gitCommit=${CI_COMMIT_SHA}'" \
    -tags musl -o /usr/local/bin/fwyytool \
    main.go

FROM image-docker.zuoyebang.cc/base/go-runner:2.0

ARG APP_NAME
ENV APP_NAME $APP_NAME

WORKDIR /usr/local/bin/

COPY --from=builder /usr/local/bin/fwyytool /usr/local/bin/
COPY ./conf/app /usr/local/bin/conf/app
COPY ./templates /usr/local/bin/templates
COPY ./assets /usr/local/bin/assets

RUN apk update --allow-untrusted

CMD ["/usr/local/bin/fwyytool"]

