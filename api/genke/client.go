package genke

import (
	"encoding/json"
	"fwyytool/api"
	"fwyytool/conf"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.Genke,
	}
	return c
}

const (
	GetTimelineByLessonStuAPI = "/genke/tool/jxnotice/gettimelinebylessonstu"
)

func (c *Client) GetTimelineByLessonStu(ctx *gin.Context, params GetTimelineByLessonStuParams) (resp GetTimelineByLessonStuResp, err error) {
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.Genke.HttpGet(ctx, GetTimelineByLessonStuAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}
