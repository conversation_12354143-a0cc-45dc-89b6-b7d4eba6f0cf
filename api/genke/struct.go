package genke

import "fwyytool/api"

type (
	GetTimelineByLessonStuParams struct {
		StudentUid int64 `json:"studentUid"`
		LessonId   int64 `json:"lessonId"`
	}
)

type GetTimelineByLessonStuResp struct {
	AssistantAttendStuTimeLines []api.AttendTimeLine `json:"assistantAttendStuTimeLines"`
	AttendTimeData              map[int64][]TimeLine `json:"attendTimeData"`
	FoucusTimeData              []StudentTimeLine    `json:"foucusTimeData"`
	GenkeTimeLines              []StudentTimeLine    `json:"genkeTimeLines"`
	HitDiffGray                 bool                 `json:"hitDiffGray"`
	HitGray                     bool                 `json:"hitGray"`
	LiveRoomId                  int64                `json:"liveRoomId"`
	OriginTimeLines             []StudentTimeLine    `json:"originTimeLines"`
	OriginTimeLinesV2           []StudentTimeLine    `json:"originTimeLinesV2"`
	Stats                       AttendStats          `json:"stats"`
	Teacher                     GenkeTeacher         `json:"teacher"`
	TeacherAttendTimeData       map[int64][]string   `json:"teacherAttendTimeData"`
	UseTimelineV2               bool                 `json:"useTimelineV2"`
}

type StudentTimeLine struct {
	UId                int        `json:"uId" mapstructure:"uId"`
	CurrentStatus      int        `json:"currentStatus" mapstructure:"currentStatus"`
	CurrentStatusStart int        `json:"currentStatusStart" mapstructure:"currentStatusStart"` //当前状态起始时间
	CameraStatus       int        `json:"cameraStatus" mapstructure:"cameraStatus"`             //摄像头状态
	SpeedModelStatus   int        `json:"speedModelStatus" mapstructure:"speedModelStatus"`     //极速模式状态
	PinpStatus         int        `json:"pinpStatus" mapstructure:"pinpStatus"`                 //画中画状态
	StatusTimeLine     []TimeLine `json:"statusTimeLine" mapstructure:"statusTimeLine"`
}

//必须 //@(-1) 离席。@(0).不专注 @(1).专注 @(3).在线（4）离线
type TimeLine struct {
	StartTime int    `json:"startTime" mapstructure:"startTime"` // 开始时间
	Status    int    `json:"status" mapstructure:"status"`       //@(-1) 离席。@(0).不专注 @(1).专注 @(3).在线（4）离线
	Explain   string `json:"explain" mapstructure:"explain"`     //存储在在离线format时间及lbp观看绝对时间
}

type AttendStats struct {
	AttendStudentUids   []int64
	NoAttendStudentUids []int64
	// 到课学生和未到课学生数
	AttendStudentCount   int
	NoAttendStudentCount int
	OfflineCount         int
	CurrentStatusNum     map[int]int
	OnlineRate           string
}

type GenkeTeacher struct {
	Duty          string `json:"duty"`
	Phone         string `json:"phone"`
	TeacherAvatar string `json:"teacherAvatar"`
	TeacherName   string `json:"teacherName"`
	TeacherUid    int64  `json:"teacherUid"`
}
