package moat

type OneOpenListReq struct {
	UserId   int64   `json:"userId"`
	Status   int64   `json:"status"`
	ShopIds  []int64 `json:"shopIds"`
	Page     int64   `json:"page"`
	PageSize int64   `json:"pageSize"`
}

type OneOpenListResp struct {
	UserId              int64                   `json:"userId"`
	OrderId             int64                   `json:"orderId"`
	OriginalOrderId     int64                   `json:"originalOrderId"`
	BusinessId          int64                   `json:"businessId"`
	BusinessType        int64                   `json:"businessType"`
	OrderType           int64                   `json:"orderType"`
	OrderFlag           []int64                 `json:"orderFlag"`
	OrderStatus         int64                   `json:"orderStatus"`
	OrderBusinessStatus int64                   `json:"orderBusinessStatus"`
	OrderValidStatus    int64                   `json:"orderValidStatus"`
	OrderPlat           int64                   `json:"orderPlat"`
	ShopId              int64                   `json:"shopId"`
	SaleChannel         int64                   `json:"saleChannel"`
	Quantity            int64                   `json:"quantity"`
	GoodsAmount         int64                   `json:"goodsAmount"`
	ExpressAmount       int64                   `json:"expressAmount"`
	GoodsDiscount       int64                   `json:"goodsDiscount"`
	ExpressDiscount     int64                   `json:"expressDiscount"`
	TransAmount         int64                   `json:"transAmount"`
	PayableAmount       int64                   `json:"payableAmount"`
	PaidAmount          int64                   `json:"paidAmount"`
	RemainAmount        int64                   `json:"remainAmount"`
	CurrencyType        int64                   `json:"currencyType"`
	AddressInfo         interface{}             `json:"addressInfo"`
	LogInfo             interface{}             `json:"logInfo"`
	OrderTime           int64                   `json:"orderTime"`
	PayTime             int64                   `json:"payTime"`
	FinishTime          int64                   `json:"finishTime"`
	SkuRowList          []OneOpenListSkuRowInfo `json:"skuRowList"`
}

type AddressInfo struct {
	Id           int64  `json:"id"`
	Uid          int64  `json:"uid"`
	Name         string `json:"name"`
	Phone        string `json:"phone"`
	Province     string `json:"province"`
	City         string `json:"city"`
	Prefecture   string `json:"prefecture"`
	Town         string `json:"town"`
	Address      string `json:"address"`
	IsDefault    int64  `json:"isDefault"`
	ProvinceId   int64  `json:"provinceId"`
	CityId       int64  `json:"cityId"`
	PrefectureId int64  `json:"prefectureId"`
	TownId       int64  `json:"townId"`
	Chosen       int64  `json:"chosen"`
}

type LogInfo struct {
	Lastfrom string `json:"lastfrom"`
}

type OneOpenListSkuRowInfo struct {
	SkuId            int64  `json:"skuId"`
	SkuRowId         int64  `json:"skuRowId"`
	Quantity         int64  `json:"quantity"`
	SkuName          string `json:"skuName"`
	MainSkuId        int64  `json:"mainSkuId"`
	SkuVersionId     int64  `json:"skuVersionId"`
	MainSkuVersionId int64  `json:"mainSkuVersionId"`
	ProductId        int64  `json:"productId"`
	IsGift           int64  `json:"isGift"`
	SkuType          int64  `json:"skuType"`
	ShopId           int64  `json:"shopId"`
	ExpressAmount    int64  `json:"expressAmount"`
	GoodsAmount      int64  `json:"goodsAmount"`
	PaidAmount       int64  `json:"paidAmount"`
	SkuMode          int64  `json:"skuMode"`
	Points           int64  `json:"points"`
	SkuServiceType   int64  `json:"skuServiceType"`
	SkuFlag          int64  `json:"skuFlag"`
	PackageTime      int64  `json:"packageTime"`
	PackageStatus    int64  `json:"packageStatus"`
}
