package moat

import (
	"fwyytool/api"
	"fwyytool/conf"
	"fwyytool/consts"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

const (
	apiOneOpenList = "/one/open/list"
)

func OneOpenList(ctx *gin.Context, req OneOpenListReq) (respList []*OneOpenListResp, err error) {
	if req.UserId == 0 {
		return
	}

	params := map[string]interface{}{
		"userId":   req.UserId,
		"status":   req.Status,
		"page":     req.Page,
		"pageSize": req.PageSize,
	}
	if len(req.ShopIds) > 0 {
		shopIds, _ := jsoniter.MarshalToString(req.ShopIds)
		params["shopIds"] = shopIds
	}
	params = api.GetMoatOpts(consts.<PERSON><PERSON><PERSON><PERSON>, consts.MoatAppSecret, params)

	url := apiOneOpenList
	opt := base.HttpRequestOptions{
		RequestBody: params,
	}
	res, err := conf.API.Moat.HttpPost(ctx, url, opt)
	if err != nil {
		zlog.Warnf(ctx, "OneOpenList.ral failed:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	if _, err = api.DecodeResponse(ctx, res, &respList); err != nil {
		return
	}

	return
}
