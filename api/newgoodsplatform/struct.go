package newgoodsplatform

type SkuBaseInfoReq struct {
	SkuIdList []int64 `json:"skuIdList,omitempty"`
	Source    int64   `json:"source,omitempty"`  // 业务线；非必填，默认0
	IsCache   int64   `json:"isCache,omitempty"` // 是否使用缓存；非必填，默认1
}

type SkuBaseInfoResp struct {
	SkuInfoList []SkuBaseInfoItem `json:"skuInfoList"`
}

type SkuBaseInfoItem struct {
	SkuId             int64                    `json:"skuId"`             // SKUID
	SkuName           string                   `json:"skuName"`           // SKU名称
	Source            int64                    `json:"source"`            // 业务线：4-一课，其他业务线参考http://ued.zuoyebang.cc/documents/docs/dds/source.html
	ItemId            int64                    `json:"itemId"`            // 老版本用来做幂等的ID，值同SKUID
	Category          int64                    `json:"category"`          // 分类ID
	Price             int64                    `json:"price"`             // 售价（人民币）单位（分）
	SkuOriginPrice    int64                    `json:"skuOriginPrice"`    // 原价（人民币）单位（分）
	KidPracticeCoin   int64                    `json:"kidPracticeCoin"`   // 鸭鸭习字币
	KidChineseCoin    int64                    `json:"kidChineseCoin"`    // 鸭鸭语文币
	KidMagicStones    int64                    `json:"kidMagicStones"`    // 鸭鸭英语币-魔法石
	StarCurrency      int64                    `json:"starCurrency"`      // 帮帮星
	Credit            int64                    `json:"credit"`            // 售卖学分
	Currency          int64                    `json:"currency"`          // 售卖帮帮币
	CombinationType   int64                    `json:"combinationType"`   // 是否是组合SKU：1：否 2：是
	SpuId             int64                    `json:"spuId"`             // SPUID
	AttributeTags     SkuBaseInfoAttributeTags `json:"attributeTags"`     // 商品属性：不同分类对应商品属性域不同，请参考https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=107942734
	SpecTags          SkuBaseInfoSpecTags      `json:"specTags"`          // 商品规格：不同分类对应商品规格域不同，请参考https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=107942734
	LabelTags         SkuBaseInfoLabelTags     `json:"labelTags"`         // 商品标签：不同分类对应商品标签域不同，请参考https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=107942734
	IsInner           int64                    `json:"isInner"`           // 是否是内部SKU（对内对外）：0-否 、1-是
	OrderLimit        int64                    `json:"orderLimit"`        // 限购数量
	IsDelete          int64                    `json:"isDelete"`          // 是否删除：0-否 、1-是
	ShortSkuName      string                   `json:"shortSkuName"`      // SKU短名称
	SkuMode           int64                    `json:"skuMode"`           // SKU mode类型：1-中台课程 、2-中台实物，其他枚举值请参考http://ued.zuoyebang.cc/documents/docs/dds/skuMode.html
	ShopId            int64                    `json:"shopId"`            // 店铺ID（同业务线）
	BusinessDomain    int64                    `json:"businessDomain"`    // 业务域：1-B2C、2-B2B
	AfterSaleTag      int64                    `json:"afterSaleTag"`      // 售后标记：枚举请参考http://ued.zuoyebang.cc/documents/docs/dds/aftersalerules.html
	TaxationCode      string                   `json:"taxationCode"`      // 税务编码
	FreightPriceTag   int64                    `json:"freightPriceTag"`   // 运费标记
	CostPrice         int64                    `json:"costPrice"`         // 成本价（人民币）单位（分）
	BusinessScopeType int64                    `json:"businessScopeType"` // 业务类型：1-自营商品、2-积分商品
}

type SkuBaseInfoAttributeTags struct {
	Grade              []string `json:"grade"`
	Subject            []string `json:"subject"`
	LearnSeason        []string `json:"learnSeason"`
	BookVer            []string `json:"bookVer"`
	SystemType         []string `json:"systemType"`
	ClassType          []string `json:"classType"`
	ModuleType         []string `json:"moduleType"`
	Year               int64    `json:"year"`
	CourseType         []string `json:"courseType"`
	Difficulty         []string `json:"difficulty"`
	FitCrowd           string   `json:"fitCrowd"`
	BrandId            []string `json:"brandId"`
	GCpuId             string   `json:"GCpuId"`
	CpuId              string   `json:"cpuId"`
	SuggestedGrades    []string `json:"suggestedGrades"`
	SuggestedSubjects  []string `json:"suggestedSubjects"`
	SpuCourseStartTime int64    `json:"spuCourseStartTime"`
	SpuCourseEndTime   int64    `json:"spuCourseEndTime"`
}

type SkuBaseInfoSpecTags struct {
	LessonCycle              []string `json:"lessonCycle"`
	TeacherUids              []int64  `json:"teacherUids"`
	TeacherNameList          []string `json:"teacherNameList"`
	FirstCoreLessonStartTime int64    `json:"firstCoreLessonStartTime"`
	OpenCourseTime           int64    `json:"openCourseTime"`
	CloseCourseTime          int64    `json:"closeCourseTime"`
	CycleCourseStartTime     int64    `json:"cycleCourseStartTime"`
	CycleCourseStopTime      int64    `json:"cycleCourseStopTime"`
	LessonDisplayText        string   `json:"lessonDisplayText"`
	ClientLessonDisplayText  string   `json:"clientLessonDisplayText"`
}

type SkuBaseInfoLabelTags struct {
	PullNewDuty          []string `json:"pullNewDuty"`
	ServicesList         []string `json:"servicesList"`
	SkuType              []string `json:"skuType"`
	AttendCourseTimeSlot []string `json:"attendCourseTimeSlot"`
	Semester             []string `json:"semester"`
	CourseStatus         []string `json:"courseStatus"`
	CourseDifficulty     []string `json:"courseDifficulty"`
	LessonNum            string   `json:"lessonNum"`
	CourseTags           []string `json:"courseTags"`
	CourseTimeGroup      int64    `json:"courseTimeGroup"`
	PlayForm             []string `json:"playForm"`
}

type GetCategoryFieldKVByCategoryIdListReq struct {
	CategoryIdList []int64 `json:"categoryIdList,omitempty"`
	BoolCache      bool    `json:"boolCache,omitempty"`
}

type GetCategoryFieldKVByCategoryIdListResp struct {
	CategoryId   int64   `json:"categoryId"`
	CategoryPid  int64   `json:"categoryPid"`
	CategoryRoot int64   `json:"categoryRoot"`
	CategoryName string  `json:"categoryName"`
	ProductType  []int64 `json:"productType"`
	SkuMode      []int64 `json:"skuMode"`
	IsDelete     int64   `json:"isDelete"` // 0-未删除 1-以删除
}
