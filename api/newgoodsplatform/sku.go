package newgoodsplatform

import (
	"encoding/json"
	"fwyytool/api"
	"fwyytool/conf"
	"fwyytool/consts"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

const (
	apiSkuBaseInfo                        = "/newgoodsplatform/sku/skubaseinfo"                                       // https://ssv.zuoyebang.cc/static/open-sell/index.html#/docs/list?releaseId=1622541349&node=4_67_70_&docType=1
	apiGetCategoryFieldKVByCategoryIdList = "/newgoodsplatform/goodscategoryfield/getcategoryfieldkvbycategoryidlist" // https://ssv.zuoyebang.cc/static/open-sell/index.html#/docs/list?releaseId=1621411650&node=4_67_70_&docType=1
)

func SkuBaseInfo(ctx *gin.Context, req SkuBaseInfoReq) (resp *SkuBaseInfoResp, err error) {
	params := map[string]interface{}{}
	temp, _ := json.Marshal(req)
	if err = json.Unmarshal(temp, &params); err != nil {
		zlog.Warnf(ctx, "SkuBaseInfo.jsonUnMarshalErr:%v", err)
		return
	}
	params["skuIdList"], _ = jsoniter.MarshalToString(req.SkuIdList)
	params = api.GetMoatOpts(consts.MoatAppKey, consts.MoatAppSecret, params)

	opts := base.HttpRequestOptions{RequestBody: params, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.Moat.HttpGet(ctx, apiSkuBaseInfo, opts)
	if err != nil {
		zlog.Warnf(ctx, "SkuBaseInfo.ral failed:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return
}

func GetCategoryFieldKVByCategoryIdList(ctx *gin.Context, req GetCategoryFieldKVByCategoryIdListReq) (resp []*GetCategoryFieldKVByCategoryIdListResp, err error) {
	params := map[string]interface{}{}
	temp, _ := json.Marshal(req)
	if err = json.Unmarshal(temp, &params); err != nil {
		zlog.Warnf(ctx, "GetCategoryFieldKVByCategoryIdList.jsonUnMarshalErr:%v", err)
		return
	}
	params["categoryIdList"], _ = jsoniter.MarshalToString(req.CategoryIdList)
	params = api.GetMoatOpts(consts.MoatAppKey, consts.MoatAppSecret, params)

	opts := base.HttpRequestOptions{RequestBody: params, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.Moat.HttpGet(ctx, apiGetCategoryFieldKVByCategoryIdList, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetCategoryFieldKVByCategoryIdList.ral failed:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return
}
