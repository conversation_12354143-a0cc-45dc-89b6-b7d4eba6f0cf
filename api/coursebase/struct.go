package coursebase

const (
	KeySource        = "source"
	KeyNewCourseType = "newCourseType"
	KeyGrade         = "grade"
	KeySubject       = "subject"
)

type Key string

type KeysReq struct {
	Keys []Key `json:"keys,omitempty" form:"keys"`
}

type KeysResp struct {
	AttrKey          string     `json:"attrKey"`
	AttrName         string     `json:"attrName"`
	AttrStatus       int64      `json:"attrStatus"`
	StorageLocation  int64      `json:"storageLocation"`
	DefaultValueType int64      `json:"defaultValueType"`
	AttrAppType      []int64    `json:"attrAppType"`
	EnumList         []KeysEnum `json:"enumList"`
}

type KeysEnum struct {
	Value   int64  `json:"value"`
	Name    string `json:"name"`
	Abandon int64  `json:"abandon"`
}
