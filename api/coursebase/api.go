package coursebase

import (
	"encoding/json"
	"fwyytool/api"
	"fwyytool/conf"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	apiKeys = "/coursebase/api/keys" // https://yapi.zuoyebang.cc/project/7140/interface/api/328271
)

func Keys(ctx *gin.Context, req KeysReq) (respMap map[Key]KeysResp, err error) {
	params := map[string]interface{}{}
	temp, _ := json.Marshal(req)
	if err = json.Unmarshal(temp, &params); err != nil {
		zlog.Warnf(ctx, "Keys.jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: params, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.CourseBase.HttpGet(ctx, apiKeys, opts)
	if err != nil {
		zlog.Warnf(ctx, "Keys.ral failed:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	if _, err = api.DecodeResponse(ctx, res, &respMap); err != nil {
		return
	}

	return
}

func KeysMap(ctx *gin.Context, req KeysReq) (keysMap map[Key]map[int64]string, err error) {
	var respMap map[Key]KeysResp
	respMap, err = Keys(ctx, req)
	if err != nil {
		return
	}

	keysMap = make(map[Key]map[int64]string)
	for k, v := range respMap {
		if len(v.EnumList) == 0 {
			keysMap[k] = map[int64]string{}
			continue
		}

		enumMap := make(map[int64]string)
		for _, enum := range v.EnumList {
			if enum.Abandon != 0 {
				continue
			}
			enumMap[enum.Value] = enum.Name
		}
		keysMap[k] = enumMap
	}
	return
}
