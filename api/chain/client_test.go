package chain

import (
	"fmt"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestAnalysisTrace(t *testing.T) {
	// 创建测试数据，模拟 trace.txt 的结构
	traceData := []*TraceSpanNode{
		{
			TraceID:       "fe5eb6167210ef25",
			SpanID:        "2699d8bfba037520",
			ParentSpanID:  "fe5eb6167210ef25",
			Ns:            "saas",
			App:           "ips-server",
			OperationName: "GET /ips-server/gate/session",
			StartTime:     1751596335761000,
			Duration:      5590,
			Tags: []*Tag{
				{Key: "url", Value: "/ips-server/gate/session"},
				{Key: "http_code", Value: "200"},
			},
		},
		{
			TraceID:       "fe5eb6167210ef25",
			SpanID:        "0d5bc49799a624f8",
			ParentSpanID:  "2699d8bfba037520", // 这是第一层调用
			Ns:            "redis",
			App:           "redis-saascore",
			OperationName: "redis hGetAll",
			StartTime:     1751596335987899,
			Duration:      622,
			Tags: []*Tag{
				{Key: "key", Value: "ips_service_userprofile"},
				{Key: "cluster", Value: "k8s.tips.tx.bj6"},
			},
		},
		{
			TraceID:       "fe5eb6167210ef25",
			SpanID:        "38518eef66261129",
			ParentSpanID:  "2699d8bfba037520", // 这也是第一层调用
			Ns:            "redis",
			App:           "redis-saascore",
			OperationName: "redis ttl",
			StartTime:     1751596335989399,
			Duration:      833,
			Tags: []*Tag{
				{Key: "key", Value: "IPS:ACCESSTOKEN:userprofile"},
				{Key: "cluster", Value: "k8s.tips.tx.bj6"},
			},
		},
	}

	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)

	// 创建客户端
	client := &Client{}

	// 测试分析调用链路
	result, err := client.AnalysisTrace(ctx, "/ips-server/gate/session", traceData)
	if err != nil {
		t.Fatalf("AnalysisTrace failed: %v", err)
	}

	if len(result.TraceReportNodeList) != 2 {
		t.Errorf("Expected 2 first level calls in slice, got %d", len(result.TraceReportNodeList))
	}

}

func TestAnalysisTraceByDomain(t *testing.T) {
	// 创建测试数据
	traceData := []*TraceSpanNode{
		{
			TraceID:       "fe5eb6167210ef25",
			SpanID:        "fe5eb6167210ef25",
			ParentSpanID:  "0000000000000000",
			Ns:            "zyb-ingress",
			App:           "arkgo-ingress-internal",
			OperationName: "GET /arkgo",
			StartTime:     1751596335767000,
			Duration:      640000,
			Tags: []*Tag{
				{Key: "host", Value: "assistantdesk.zuoyebang.cc"},
				{Key: "url", Value: "https://assistantdesk.zuoyebang.cc/arkgo"},
			},
		},
		{
			TraceID:       "fe5eb6167210ef25",
			SpanID:        "2699d8bfba037520",
			ParentSpanID:  "fe5eb6167210ef25", // 第一层调用
			Ns:            "saas",
			App:           "ips-server",
			OperationName: "GET /ips-server/gate/session",
			StartTime:     1751596335761000,
			Duration:      5590,
			Tags: []*Tag{
				{Key: "url", Value: "/ips-server/gate/session"},
			},
		},
	}

	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)

	// 创建客户端
	client := &Client{}

	// 测试按域名分析调用链路
	result, err := client.AnalysisTraceByDomain(ctx, traceData, "assistantdesk.zuoyebang.cc")
	if err != nil {
		t.Fatalf("AnalysisTraceByDomain failed: %v", err)
	}
	fmt.Println(result)
}
