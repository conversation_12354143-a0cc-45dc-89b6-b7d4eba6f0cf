package chain

type GetTraceDetailParams struct {
	RequestID int64 `json:"requestID"`
}

// Tag 表示 trace 中的标签
type Tag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// TraceSpanNode 表示单个 span 信息
type TraceSpanNode struct {
	TraceID       string `json:"traceID"`
	SpanID        string `json:"spanID"`
	ParentSpanID  string `json:"parentSpanID"`
	Ns            string `json:"ns"`
	App           string `json:"app"`
	OperationName string `json:"operationName"`
	StartTime     int64  `json:"startTime"`
	Duration      int64  `json:"duration"`
	Tags          []*Tag `json:"tags"`
}

// GetTraceDetailResp 表示 trace 详情响应
type GetTraceDetailData struct {
	Total  int64            `json:"total"`
	Result []*TraceSpanNode `json:"result"`
}

type TraceReport struct {
	TraceID             string              `json:"traceID"`
	ApiDuration         int64               `json:"apiDuration"`
	NodeNum             int64               `json:"nodeNum"`
	TraceReportNodeList []*TraceRrportNode  `json:"traceReportNodeList"`
	RepeatNodeReport    []*RepeatNodeReport `json:"repeatNodeReport"`
}

type RepeatNodeReport struct {
	RequestUrl          string             `json:"requestUrl"`
	ApiDuration         int64              `json:"apiDuration"`
	NodeNum             int                `json:"nodeNum"`
	TraceReportNodeList []*TraceRrportNode `json:"traceReportNodeList"`
}

type TraceRrportNode struct {
	App           string `json:"app"`
	OperationName string `json:"operationName"`
	StartTime     int64  `json:"startTime"`
	Duration      int64  `json:"duration"`
}
