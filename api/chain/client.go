package chain

import (
	"fwyytool/api"
	"fwyytool/components"
	"fwyytool/conf"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"net/url"
	"sort"
	"strings"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.AssistantDesk,
	}
	return c
}

const (
	GetTraceInfoAPI = "/chain/api/gettraceinfo"
)

func (c *Client) GetTraceDetail(ctx *gin.Context, requestID string) (traceSpanNodeList []*TraceSpanNode, err error) {
	resp := &GetTraceDetailData{}
	params := map[string]interface{}{
		"traceid": requestID,
	}

	opts := base.HttpRequestOptions{RequestBody: params, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.Chain.HttpGet(ctx, GetTraceInfoAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestTraceDetailErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	// 直接解析完整的响应结构
	if _, err = api.DecodeResponse(ctx, res, resp); err != nil {
		return
	}

	return resp.Result, nil
}

// AnalysisTrace 分析调用链路，找到给定接口对下游的调用记录（第一层）
func (c *Client) AnalysisTrace(ctx *gin.Context, traceID, targetURL string, traceSpanNodeList []*TraceSpanNode) (nodeReport *TraceReport, err error) {
	nodeReport = &TraceReport{
		TraceID: traceID,
	}
	nodeList, err := c.getNodeList(ctx, targetURL, traceSpanNodeList)
	if err != nil {
		return
	}

	traceReportNodeList := make([]*TraceRrportNode, 0)
	for _, node := range nodeList {
		traceReportNodeList = append(traceReportNodeList, &TraceRrportNode{
			App:           node.App,
			OperationName: node.OperationName,
			StartTime:     node.StartTime,
			Duration:      node.Duration,
		})
	}

	nodeReport.ApiDuration = c.getApiDuration(ctx, nodeList)
	nodeReport.TraceReportNodeList = traceReportNodeList
	nodeReport.NodeNum = int64(len(nodeReport.TraceReportNodeList))
	nodeReport.RepeatNodeReport, _ = c.getRepeatRequestList(ctx, nodeList)
	return nodeReport, nil
}

func (c *Client) getRepeatRequestList(ctx *gin.Context, nodeList []*TraceSpanNode) (repeatNodeReport []*RepeatNodeReport, err error) {
	repeatNodeReport = []*RepeatNodeReport{}
	requestUrlMap := map[string][]*TraceSpanNode{}
	for _, node := range nodeList {
		for _, tag := range node.Tags {
			if tag.Key == "url" {
				urlDetail, _ := url.Parse(tag.Value)
				requestApi := urlDetail.Path
				requestUrlMap[requestApi] = append(requestUrlMap[requestApi], node)
			}
		}
	}

	for requestUrl, repeatNodeList := range requestUrlMap {
		if len(repeatNodeList) < 2 {
			continue
		}
		sort.Slice(repeatNodeList, func(i, j int) bool {
			return repeatNodeList[i].StartTime < repeatNodeList[j].StartTime
		})

		traceReportNodeList := make([]*TraceRrportNode, 0)
		for _, node := range repeatNodeList {
			traceReportNodeList = append(traceReportNodeList, &TraceRrportNode{
				App:           node.App,
				OperationName: node.OperationName,
				StartTime:     node.StartTime,
				Duration:      node.Duration,
			})
		}

		repeatNodeReport = append(repeatNodeReport, &RepeatNodeReport{
			RequestUrl:          requestUrl,
			ApiDuration:         c.getApiDuration(ctx, repeatNodeList),
			NodeNum:             len(repeatNodeList),
			TraceReportNodeList: traceReportNodeList,
		})
	}
	sort.Slice(repeatNodeReport, func(i, j int) bool {
		return repeatNodeReport[i].NodeNum > repeatNodeReport[j].NodeNum
	})
	return
}

func (c *Client) getApiDuration(ctx *gin.Context, nodeList []*TraceSpanNode) (duration int64) {
	var beginTime, endTime int64 = 0, 0
	if len(nodeList) == 0 {
		return 0
	}

	beginTime = nodeList[0].StartTime
	endTime = nodeList[0].StartTime + nodeList[0].Duration

	for index := range nodeList {
		endTime = components.Util.MaxInt64(nodeList[index].StartTime+nodeList[index].Duration, endTime)
	}

	return endTime - beginTime
}

func (c *Client) getNodeList(ctx *gin.Context, targetURL string, traceSpanNodeList []*TraceSpanNode) (nodeList []*TraceSpanNode, err error) {
	if len(traceSpanNodeList) == 0 {
		return
	}

	// 1. 找到匹配目标URL的根节点
	var rootSpan *TraceSpanNode
	for i := range traceSpanNodeList {
		span := traceSpanNodeList[i]
		// 查找tags中key为'url'的值
		for _, tag := range span.Tags {
			if tag.Key == "url" && strings.Contains(tag.Value, targetURL) {
				rootSpan = span
				break
			}
		}
		if rootSpan != nil {
			break
		}
	}

	if rootSpan == nil {
		return
	}

	// 2. 找到所有parentSpanID等于根节点spanID的记录（第一层调用）
	nodeList = make([]*TraceSpanNode, 0)
	for _, span := range traceSpanNodeList {
		if span.ParentSpanID == rootSpan.SpanID {
			for _, tag := range span.Tags {
				if tag.Key == "url" {
					nodeList = append(nodeList, span)
					break
				}
			}
		}
	}
	sort.Slice(nodeList, func(i, j int) bool {
		return nodeList[i].StartTime < nodeList[j].StartTime
	})
	return
}

// AnalysisTraceByDomain 根据域名分析调用链路
func (c *Client) AnalysisTraceByDomain(ctx *gin.Context, traceSpanNodeList []*TraceSpanNode, domain string) (nodeList []*TraceSpanNode, err error) {
	if len(traceSpanNodeList) == 0 {
		return
	}

	// 1. 找到匹配域名的根节点
	var rootSpan *TraceSpanNode
	for i := range traceSpanNodeList {
		span := traceSpanNodeList[i]
		// 查找tags中key为'url'的值，检查是否包含指定域名
		for _, tag := range span.Tags {
			if tag.Key == "url" && strings.Contains(tag.Value, domain) {
				rootSpan = span
				break
			}
		}
		if rootSpan != nil {
			break
		}
	}

	if rootSpan == nil {
		// 没有找到匹配的span，返回空结果
		return
	}

	// 2. 找到所有parentSpanID等于根节点spanID的记录（第一层调用）
	var firstLevelCalls []*TraceSpanNode
	for _, span := range traceSpanNodeList {
		if span.ParentSpanID == rootSpan.SpanID {
			firstLevelCalls = append(firstLevelCalls, span)
		}
	}

	return firstLevelCalls, nil
}
