package courseSearch

import (
	"errors"
	"fwyytool/api"
	"fwyytool/components"
	"fwyytool/conf"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const DOC_TYPE_LESSON = 4

const PATH_COURSE_SEARCH_QUERY_API = "/coursesearch/api/query"

const maxQueryRow = 1000

type LessonData struct {
	List  []LessonInfo `json:"list"`
	Total int          `json:"total"`
	Rn    int          `json:"rn"`
	Pn    int          `json:"pn"`
}
type LessonInfo struct {
	LessonCourseId int      `json:"lessonCourseId"`
	CourseName     string   `json:"courseName"`
	LessonId       int      `json:"lessonId"`
	LessonName     string   `json:"lessonName"`
	PlayType       int      `json:"playType"`
	StartTime      int      `json:"startTime"`
	StopTime       int      `json:"stopTime"`
	Status         int      `json:"status"`
	IsInner        int      `json:"isInner"`
	GradeId        int      `json:"gradeId"`
	Year           int      `json:"year"`
	SubjectId      int      `json:"subjectId"`
	TeacherUid     []string `json:"teacherUid"`
	TeacherName    []string `json:"teacherName"`
	CreateTime     int      `json:"createTime"`
	UpdateTime     int      `json:"updateTime"`
	DocType        int      `json:"docType"`
}

/*
 * https://yapi.zuoyebang.cc/project/4613/interface/api/149187
 * 教务搜索课程
 */
func GetTodayAllLessonList(ctx *gin.Context, fields []string) (lessonList []LessonInfo, err error) {
	resp, err := GetTodayLessonList(ctx, fields, 0)
	if err != nil {
		return
	}
	maxPn := resp.Total / maxQueryRow
	if resp.Total%maxQueryRow > 0 {
		maxPn++
	}
	lessonList = append(lessonList, resp.List...)

	for pn := 1; pn < maxPn; pn++ {
		resp, err = GetTodayLessonList(ctx, fields, pn*maxQueryRow)
		if err != nil {
			return
		}
		lessonList = append(lessonList, resp.List...)
	}
	return lessonList, nil
}

func GetTodayLessonList(ctx *gin.Context, fields []string, pn int) (resp LessonData, err error) {
	todayTime := components.Util.GetTodayTimestamp()
	cond2 := map[string]interface{}{
		"type": 0,
		"conds": map[string]interface{}{
			"key":   "startTime",
			"value": todayTime,
			"exps":  "gt",
		},
	}

	cond4 := map[string]interface{}{
		"type": 0,
		"conds": map[string]interface{}{
			"key":   "stopTime",
			"value": todayTime + 24*3600,
			"exps":  "lt",
		},
	}

	cond5 := map[string]interface{}{
		"type": 0,
		"conds": map[string]interface{}{
			"key":   "isInner",
			"value": 0,
			"exps":  "eq",
		},
	}

	params := map[string]interface{}{
		"arrFields": fields,
		"arrConds": map[string]interface{}{
			"op": "and",
			"aggs": []map[string]interface{}{
				cond2,
				cond4,
				cond5,
			},
		},
		"docType": DOC_TYPE_LESSON,
		"orderBy": map[string]interface{}{
			"lessonId": "asc",
		},
		"pn": pn,
		"rn": maxQueryRow,
	}

	opts := base.HttpRequestOptions{RequestBody: params, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.CourseSearch.HttpGet(ctx, PATH_COURSE_SEARCH_QUERY_API, opts)
	if err != nil {
		zlog.Warnf(ctx, "Keys.ral failed:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, err
}

func Query(ctx *gin.Context, req QueryReq) (resp QueryResp, err error) {
	if req.rn > maxQueryRow {
		err = errors.New("CourseSearch.Query params over limit 1000")
		return
	}

	params := map[string]interface{}{
		"arrFields": req.formatArrFields(),
		"arrConds":  req.formatArrCond(),
		"docType":   req.docType,
		"orderBy":   req.formatOrderBy(),
		"pn":        req.pn,
		"rn":        req.rn,
	}

	opts := base.HttpRequestOptions{RequestBody: params, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.CourseSearch.HttpGet(ctx, PATH_COURSE_SEARCH_QUERY_API, opts)
	if err != nil {
		zlog.Warnf(ctx, "CourseSearch.ral %s failed: %+v", PATH_COURSE_SEARCH_QUERY_API, err)
		return
	}
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	// for format
	resp.docType = req.docType
	return
}
