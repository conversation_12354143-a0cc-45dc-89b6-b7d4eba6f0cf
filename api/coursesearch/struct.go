package courseSearch

import "fwyytool/libs/json"

const (
	DocTypeCpu    DocType = 1
	DocTypeCourse DocType = 2
	DocTypeLesson DocType = 4

	OrderDesc OrderDr = "desc"
	OrderAsc  OrderDr = "asc"

	OpAnd Op = "and"
	OpOr  Op = "or"

	ExpEq Exps = "eq"
	ExpIn Exps = "in"
)

type DocType int64
type OrderDr string
type Op string
type Exps string

type QueryReq struct {
	arrFields []string
	arrCond   map[string]interface{}
	docType   DocType
	orderBy   map[string]OrderDr
	pn        int64
	rn        int64
}

// NewQueryReq 先只支持一层查询，要么and要么or，基本够用
func NewQueryReq(docType DocType, op Op) *QueryReq {
	return &QueryReq{
		arrFields: []string{},
		arrCond: map[string]interface{}{
			"op": op,
		},
		docType: docType,
		orderBy: map[string]OrderDr{},
		pn:      0,
		rn:      1000,
	}
}

func (qr *QueryReq) AddFields(fields ...string) *QueryReq {
	qr.arrFields = append(qr.arrFields, fields...)
	return qr
}

func (qr *QueryReq) AddCond(key string, value interface{}, exp Exps) *QueryReq {
	if qr.arrCond == nil {
		qr.arrCond = make(map[string]interface{})
	}

	aggs := make([]map[string]interface{}, 0)
	if conds, ok := qr.arrCond["aggs"]; ok {
		aggs = conds.([]map[string]interface{})
	}
	qr.arrCond["aggs"] = append(aggs, map[string]interface{}{
		"type": 0,
		"conds": map[string]interface{}{
			"key":   key,
			"value": value,
			"exps":  exp,
		},
	})
	return qr
}

func (qr *QueryReq) OrderBy(orderField string, order OrderDr) *QueryReq {
	qr.orderBy[orderField] = order
	return qr
}

func (qr *QueryReq) PagedBy(pn, rn int64) *QueryReq {
	qr.pn = pn
	qr.rn = rn
	return qr
}

func (qr *QueryReq) formatArrFields() string {
	if len(qr.arrFields) == 0 {
		// 未设置默认全部
		qr.arrFields = []string{"*"}
	}
	arrFields, _ := json.MarshalToString(qr.arrFields)
	return arrFields
}
func (qr *QueryReq) formatArrCond() string {
	if len(qr.arrCond) == 0 {
		return ""
	}
	arrCond, _ := json.MarshalToString(qr.arrCond)
	return arrCond
}
func (qr *QueryReq) formatOrderBy() string {
	if len(qr.orderBy) == 0 {
		return ""
	}
	orderBy, _ := json.MarshalToString(qr.orderBy)
	return orderBy
}

type QueryResp struct {
	List    interface{} `json:"list"`
	Total   int64       `json:"total"`
	Pn      int64       `json:"pn"`
	Rn      int64       `json:"rn"`
	docType DocType
}

type QueryCpu struct {
	// 待有缘人补充
}

func (rs *QueryResp) GetCpuList() (cpuList []QueryCpu) {
	if rs.docType != DocTypeCpu {
		return
	}
	marshal, _ := json.MarshalToString(rs.List)
	_ = json.UnmarshalFromString(marshal, &cpuList)
	return
}

type QueryCourse struct {
	CourseId      int64   `json:"courseId,omitempty"`
	CourseName    string  `json:"courseName,omitempty"`
	NewCourseType int64   `json:"newCourseType,omitempty"`
	StartTime     int64   `json:"startTime,omitempty"`
	StopTime      int64   `json:"stopTime,omitempty"`
	IsOnSale      int64   `json:"isOnSale,omitempty"`
	GradeId       int64   `json:"gradeId,omitempty"`
	SubjectId     int64   `json:"subjectId,omitempty"`
	Year          int64   `json:"year,omitempty"`
	LearnSeason   int64   `json:"learnSeason,omitempty"`
	SkuId         int64   `json:"skuId,omitempty"`
	IsInner       int64   `json:"isInner,omitempty"`
	TeacherUid    []int64 `json:"teacherUid,omitempty"`
}

func (rs *QueryResp) GetCourseList() (courseList []QueryCourse) {
	if rs.docType != DocTypeCourse {
		return
	}
	marshal, _ := json.MarshalToString(rs.List)
	_ = json.UnmarshalFromString(marshal, &courseList)
	return
}

type QueryLesson struct {
	// 待有缘人补充
}

func (rs *QueryResp) GetLessonList() (lessonList []QueryLesson) {
	if rs.docType != DocTypeLesson {
		return
	}
	marshal, _ := json.MarshalToString(rs.List)
	_ = json.UnmarshalFromString(marshal, &lessonList)
	return
}
