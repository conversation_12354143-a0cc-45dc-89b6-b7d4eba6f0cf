package userprofile

type RealPersonResult struct {
	List     []RealPerson `json:"list"`
	PageId   int          `json:"page_id"`
	PageSize int          `json:"page_size"`
	Total    int          `json:"total"`
}

type Real<PERSON>erson struct {
	UserId     int    `json:"userId"`
	Name       string `json:"name"`
	Mail       string `json:"mail"`
	Phone      string `json:"phone"`
	UserName   string `json:"userName"`
	UserPhone  string `json:"userPhone"`
	PhoneType  int64  `json:"phoneType"`
	CreateTime int64  `json:"create_time"`
	UpdateTime int64  `json:"update_time"`
}

type UserInfo struct {
	Avatar    string `json:"avatar"`
	Mail      string `json:"mail"`
	Name      string `json:"name"`
	Phone     string `json:"phone"`
	PhoneType int    `json:"phoneType"`
	UserId    int    `json:"userId"`
	UserName  string `json:"userName"`
	UserPhone string `json:"userPhone"`
	StaffUid  int    `json:"staffUid"`
}

type UserNameInfo struct {
	UserId    int    `json:"userId"` //用户资产id
	PersonUid int    `json:"personUid"`
	Name      string `json:"name"`
	Phone     int    `json:"phone"`
	UserName  string `json:"userName"`
	UserPhone int    `json:"userPhone"`
}

type UserNameInfoList struct {
	Record []UserNameInfo `json:"record"`
}

type GetStaffInfoByUidsReq struct {
	UserIds []int64 `json:"userIds,omitempty"`
}

type GetStaffInfoByUidsResp struct {
	List map[string]GetStaffInfoByUidsItem `json:"list,omitempty"`
}

type GetStaffInfoByUidsItem struct {
	UserName              string                    `json:"userName,omitempty"`
	UserPhone             string                    `json:"userPhone,omitempty"`
	UserId                int64                     `json:"userId,omitempty"`
	Mail                  string                    `json:"mail,omitempty"`
	City                  []int64                   `json:"city,omitempty"`
	Grade                 []int64                   `json:"grade,omitempty"`
	Subject               []int64                   `json:"subject,omitempty"`
	TrainStatus           int64                     `json:"trainStatus,omitempty"`
	UserAscription        string                    `json:"user_ascription,omitempty"`
	Status                int64                     `json:"status,omitempty"`
	QqNum                 string                    `json:"qqNum,omitempty"`
	Type                  int64                     `json:"type,omitempty"`
	Supplier              string                    `json:"supplier,omitempty"`
	ThumbnailCircleAvator string                    `json:"thumbnailCircleAvator,omitempty"`
	Group                 []GetStaffInfoByUidsGroup `json:"group,omitempty"`
}

type GetStaffInfoByUidsGroup struct {
	StaffUid    int64 `json:"staffUid,omitempty"`
	ProductLine int64 `json:"productLine,omitempty"`
	GroupId     int64 `json:"groupId,omitempty"`
	IsManager   int64 `json:"isManager,omitempty"`
}
