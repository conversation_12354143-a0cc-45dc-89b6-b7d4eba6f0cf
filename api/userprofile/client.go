package userprofile

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"fwyytool/api"
	"fwyytool/components"
	"fwyytool/conf"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"io"
	"strconv"
	"strings"
	"time"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.UserProfile,
	}
	return c
}

const (
	pathGetUsersSitesAPI  = "/userprofile/api/getuserssites"
	pathLoginCheckAPI     = "/userprofile/login/check"
	pathCheckRoleAPI      = "/userprofile/api/checkrole"
	apiGetStaffInfoByUids = "/userprofile/api/getstaffinfobyuids"
)

// 检查用户是否登陆
func (c *Client) LoginCheck(ctx *gin.Context) (userId int, err error) {
	var output struct {
		UserId   int    `json:"userId"`
		UserName string `json:"userName"`
	}
	err = c.ralRequest(ctx, pathLoginCheckAPI, map[string]interface{}{}, &output)
	if err != nil {
		zlog.Infof(ctx, "api getuserssites, err:%+v", err)
		return 0, errors.Wrap(components.ErrorUserNotLogin, err.Error())
	}
	zlog.Infof(ctx, "api getuserssites, rawId:%+v", output.UserId)
	return output.UserId, nil
}

func (c *Client) GetUserInfo(ctx *gin.Context, uid int) (userInfo *UserInfo, err error) {
	data := map[string]interface{}{
		"userId": uid,
	}
	var output struct {
		Record *UserInfo
	}
	err = c.ralRequest(ctx, pathGetUsersSitesAPI, data, &output)
	if err != nil {
		zlog.Infof(ctx, "api getuserssites, err:%+v", err)
		return
	}
	zlog.Infof(ctx, "api getuserssites, res:%+v", output.Record)
	return output.Record, nil
}

func (c *Client) ralRequest(ctx *gin.Context, urlPath string, data map[string]interface{}, output interface{}) (err error) {
	err = c.encrypt(conf.Custom.Mesh.AppId, data)
	if err != nil {
		return err
	}

	passport, _ := ctx.Cookie("ZYBUSS")
	ips, _ := ctx.Cookie("ZYBIPSCAS")
	skip, _ := ctx.Cookie("SKIP")
	dockerIps, _ := ctx.Cookie("DOCKERIPS")

	cookies := map[string]string{
		"ZYBIPSCAS": ips,
		"ZYBUSS":    passport,
		"SKIP":      skip,
		"DOCKERIPS": dockerIps,
	}
	opt := base.HttpRequestOptions{
		RequestBody: data,
		Cookies:     cookies,
	}

	res, err := conf.API.UserProfile.HttpPost(ctx, urlPath, opt)
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, output); err != nil {
		return
	}

	return nil
}

func (c *Client) encrypt(appID string, data map[string]interface{}) error {
	timestamp := time.Now().Unix()
	data["ts"] = timestamp

	appKey, err := c.genAppKey(appID, data)
	if err != nil {
		return err
	}

	data["appKey"] = appKey
	data["appId"] = appID

	return nil
}

func (c *Client) genAppKey(appID string, data map[string]interface{}) (string, error) {
	for k, v := range data {
		switch v.(type) {
		case string:
		case int64:
			data[k] = strconv.Itoa(int(v.(int64)))
		case int:
			data[k] = strconv.Itoa(v.(int))
		default:
			return "", fmt.Errorf("the type of value in param map is invalid. k:%v, v:%v, data:%v", k, v, data)
		}
	}

	bs, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	// 为了和PHP的默认json_encode行为等价, 需要替换一下
	jsonStr := strings.ReplaceAll(string(bs), `/`, `\/`)

	h := md5.New()
	if _, err := io.WriteString(h, appID); err != nil {
		return "", err
	}
	if _, err := io.WriteString(h, jsonStr); err != nil {
		return "", err
	}
	appKey := fmt.Sprintf("%x", h.Sum(nil))

	return appKey, nil
}

// 检查用户是否有接口权限
func (c *Client) CheckRole(ctx *gin.Context, userId int, route string) (hasRole bool, err error) {
	var output struct {
		HasRole bool `json:"hasRole"`
	}
	zlog.Infof(ctx, "CheckRole userId=%v,uri=%v", userId, ctx.Request.RequestURI)
	err = c.ralRequest(ctx, pathCheckRoleAPI, map[string]interface{}{
		"userId": userId,
		"route":  route,
	}, &output)
	if err != nil {
		return false, err
	}
	return output.HasRole, nil
}

func (c *Client) GetStaffInfoByUids(ctx *gin.Context, req GetStaffInfoByUidsReq) (resp GetStaffInfoByUidsResp, err error) {
	params := map[string]interface{}{}
	temp, _ := json.Marshal(req)
	if err = json.Unmarshal(temp, &params); err != nil {
		zlog.Warnf(ctx, "GetStaffInfoByUids.jsonUnMarshalErr:%v", err)
		return
	}

	err = c.ralRequest(ctx, pathGetUsersSitesAPI, params, &resp)
	if err != nil {
		return
	}
	return
}
