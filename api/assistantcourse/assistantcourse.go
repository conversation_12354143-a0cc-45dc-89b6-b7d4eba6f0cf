package assistantcourse

import (
	"encoding/json"
	"fwyytool/conf"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

const PathGetTask = "/assistantcourse/tool/tasklist"

type AssistantCourse struct {
	TypeName         string `json:"typeName"`
	TaskId           int64  `json:"taskId"`
	Id               int64  `json:"id"`
	CourseId         int64  `json:"courseId"`
	CourseName       string `json:"courseName"`
	CourseCreateTime string `json:"courseCreateTime"`
	CourseOnlineTime string `json:"courseOnlineTime"`
	CourseOnsaleTime string `json:"courseOnsaleTime"`
	BindStrategyTime string `json:"bindStrategyTime"`
	Current          string `json:"current"`
	ProcessTime      string `json:"processTime"`
	ErrInfo          string `json:"errInfo"`
	OpUid            int64  `json:"opUid"`
	OpName           string `json:"opName"`
	CpuCreateTime    string `json:"cpuCreateTime"`
	CpuOutlinesTime  string `json:"cpuOutlinesTime"`
	CpuPublishTime   string `json:"cpuPublishTime"`
	CpuTaskId        int64  `json:"cpuTaskId"`
	CpuTaskDetailId  int64  `json:"cpuTaskDetailId"`
}

type AssistantCpu struct {
	CpuCreateTime   string `json:"cpuCreateTime"`
	CpuOutlinesTime string `json:"cpuOutlinesTime"`
	CpuPublishTime  string `json:"cpuPublishTime"`
	CpuTaskId       int    `json:"cpuTaskId"`
	Id              int    `json:"id"`
	CpuTaskDetailId int    `json:"cpuTaskDetailId"`
	Current         string `json:"current"`
	CpuId           int    `json:"cpuId"`
	CpuName         string `json:"cpuName"`
	OpUid           int64  `json:"opUid"`
	OpName          string `json:"opName"`
	ErrInfo         string `json:"errInfo"`
}

type TaskListRsp struct {
	ErrNo  int      `json:"errNo"`
	ErrStr string   `json:"errStr"`
	Data   TaskList `json:"data"`
}

type TaskList struct {
	CourseList []AssistantCourse `json:"courseList"`
	CpuList    []AssistantCpu    `json:"cpuList"`
}

func GetTaskList(ctx *gin.Context, personUid, status, selectType int64) (TaskList, error) {
	req := map[string]interface{}{
		"opUid":      personUid,
		"status":     status,
		"selectType": selectType,
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}

	ret, err := conf.API.AssistantCourse.HttpPost(ctx, PathGetTask, opts)
	if err != nil {
		return TaskList{}, nil
	}
	var output TaskListRsp
	err = json.Unmarshal(ret.Response, &output)
	if output.ErrNo > 0 {
		return TaskList{}, base.Error{output.ErrNo, output.ErrStr}
	}
	if err != nil {
		return TaskList{}, err
	}
	return output.Data, err

}
