package officeServer

import (
	jsonn "encoding/json"
	"errors"
	"fwyytool/api"
	"fwyytool/conf"
	"fwyytool/libs/json"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.OfficeServer,
	}
	return c
}

const (
	htmlToPdfApi = "/office-server/task/create-pdf"     //异步生产word
	htmlToImgApi = "/office-server/task/create-capture" //同步生产word
)

const (
	officeCallBackPath = "/fwyytool/ui/office/callback?id="
)

// 异步转pdf
func (c *Client) AsyncHtmlToPdf(ctx *gin.Context, params HtmlToPdfParams) (exportUrl string, err error) {
	params.CallbackURL = conf.API.Fwyytool.Domain + officeCallBackPath + params.ID
	params.TriggerType = "offline"

	data, err := c.htmlToPdf(ctx, params)
	if err != nil {
		return
	}
	return cast.ToString(data["dwnURL"]), nil
}

// 同步转pdf
func (c *Client) SyncHtmlToPdf(ctx *gin.Context, params HtmlToPdfParams) (exportUrl string, err error) {
	params.TriggerType = "online"

	data, err := c.htmlToPdf(ctx, params)
	if err != nil {
		return
	}
	return cast.ToString(data["dwnURL"]), nil
}

// 同步获取metaData
func (c *Client) SyncHtmlToMetaData(ctx *gin.Context, params HtmlToPdfParams) (exportUrl string, err error) {
	params.TriggerType = "online"

	data, err := c.htmlToPdf(ctx, params)
	if err != nil {
		return
	}
	return cast.ToString(data["evalContent"]), nil
}

func (c *Client) htmlToPdf(ctx *gin.Context, params HtmlToPdfParams) (respData map[string]interface{}, err error) {
	reqParams := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &reqParams); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: reqParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, htmlToPdfApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	data := map[string]interface{}{}

	if _, err = decodeResponse(ctx, res, &data); err != nil {
		return
	}

	return data, nil
}

func (c *Client) htmlToImg(ctx *gin.Context, params HtmlToPdfParams) (exportUrl string, err error) {
	reqParams := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &reqParams); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: reqParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, htmlToImgApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	data := map[string]interface{}{}

	if _, err = decodeResponse(ctx, res, &data); err != nil {
		return
	}

	return cast.ToString(data["url"]), nil
}

// 解析返回值
func decodeResponse(ctx *gin.Context, res *base.ApiResult, output interface{}) (errno int, err error) {
	var r struct {
		Code int              `json:"code"`
		Msg  string           `json:"msg"`
		Data jsonn.RawMessage `json:"data"`
	}
	// 先解析数据整体
	if err = json.Unmarshal(res.Response, &r); err != nil {
		zlog.Errorf(ctx, "http response parse error[response: %v]", string(res.Response))
		return
	}

	errno = r.Code
	if errno == -1 {
		zlog.Errorf(ctx, "http response code: %d", errno)
		err = errors.New(r.Msg)
		return
	}

	if err = json.Unmarshal(r.Data, &output); err != nil {
		return
	}

	return errno, nil
}
