package officeServer

type (
	HtmlToPdfParams struct {
		ID                string  `json:"id"`
		Url               string  `json:"url"`
		TriggerType       string  `json:"triggerType"`       //触发方式：online/offline
		CallbackURL       string  `json:"callbackURL"`       //离线任务完成后的回调地址，说明见表格下方
		Scale             float64 `json:"scale"`             //网页渲染比例
		PaperWidth        float64 `json:"paperWidth"`        //页面宽度（单位：英寸
		PaperHeight       float64 `json:"paperHeight"`       //页面高度（单位：英寸
		Width             int     `json:"width"`             //页面宽度（单位：像素
		Height            int     `json:"height"`            //页面高度（单位：像素
		Eval              string  `json:"eval"`              //回传的内容(比如window.location)
		OnlyEval          bool    `json:"onlyEval"`          //只解析回传内容不转pdf
		RenderDoneWay     string  `json:"renderDoneWay"`     //页面渲染完成标志
		RenderDoneContent string  `json:"renderDoneContent"` // 当renderDoneWay为id时候，需要传对应id元素的名称，一般是#之后的内容，比如liveSty.当renderDoneWay为window时候，需要传递传递window.status等于的内容，比如finish
	}
)

type (
	HtmlToImgParams struct {
		ID               string  `json:"id"`
		Url              string  `json:"url"`
		ExpectMinuteTime int     `json:"expectMinuteTime"` // 期望完成时间，默认30s
		Width            int     `json:"width"`            //页面宽度（单位：像素
		Height           int     `json:"height"`           //页面高度（单位：像素
		TriggerType      string  `json:"triggerType"`      //触发方式：online/offline
		Merge            bool    `json:"merge"`            //是否合并为一张图片
		Span             int     `json:"span"`             //转前多少页面, pdf转图片专用，0表示全部转
		Resolution       float64 `json:"resolution"`       //分辨率
	}
)
