package api

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"fwyytool/components"
	"io"
	"net/http"
	"net/url"
	"reflect"
	"sort"
	"strings"

	jsoniter "github.com/json-iterator/go"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

var (
	// defaultHeaders for the http request.
	DefaultHeaders = map[string]string{"referer": "fwyytool"}
)

// 判断httpcode, 框架层面没有处理
func ApiHttpCode(ctx *gin.Context, res *base.ApiResult) (err error) {
	if res.HttpCode != http.StatusOK {
		zlog.Warnf(ctx, "api http code not 200", res)
		err = components.ErrorHttpNotOk.Sprintf("HttpCode:：", res.HttpCode)

		return err
	}

	return
}

// 解析返回值
func DecodeResponse(ctx *gin.Context, res *base.ApiResult, output interface{}) (errno int, err error) {
	var r struct {
		ErrNo  int             `json:"errNo"`
		ErrMsg string          `json:"errMsg"`
		Data   json.RawMessage `json:"data"`
	}
	// 先解析数据整体
	if err = json.Unmarshal(res.Response, &r); err != nil {
		zlog.Errorf(ctx, "http response parse error[response: %v]", string(res.Response))
		return
	}
	//检查错误，非0返回错误
	errno = r.ErrNo
	if r.ErrNo != 0 {
		zlog.Errorf(ctx, "http response code: %d", r.ErrNo)
		err = errors.New(r.ErrMsg)
		return
	}

	if string(r.Data) == "[]" {
		return
	}

	//解析Data
	if err = jsoniter.Unmarshal(r.Data, &output); err != nil {
		return
	}
	return errno, nil
}

// 解析返回值
func DecodeResponseString(ctx *gin.Context, res *base.ApiResult) (result string, errno int, err error) {
	var r struct {
		ErrNo  int             `json:"errNo"`
		ErrMsg string          `json:"errMsg"`
		Data   json.RawMessage `json:"data"`
	}
	// 先解析数据整体
	if err = json.Unmarshal(res.Response, &r); err != nil {
		zlog.Errorf(ctx, "http response parse error[response: %v]", string(res.Response))
		return
	}
	//检查错误，非0返回错误
	errno = r.ErrNo
	if r.ErrNo != 0 {
		zlog.Errorf(ctx, "http response code: %d", r.ErrNo)
		err = errors.New(r.ErrMsg)
		return
	}

	if string(r.Data) == "[]" {
		return
	}

	var jsonStr string
	err = json.Unmarshal(r.Data, &jsonStr) // 先将config解析为字符串
	if err != nil {
		return
	}

	return jsonStr, errno, nil
}

func GetRequestProxy(ctx *gin.Context, apiClient *base.ApiClient, path string, params map[string]interface{}) (*base.ApiResult, error) {
	return get(ctx, apiClient, path, params)
}

func PostRequestProxy(ctx *gin.Context, apiClient *base.ApiClient, path string, params map[string]interface{}) (*base.ApiResult, error) {
	return post(ctx, apiClient, path, params)
}

func get(ctx *gin.Context, apiClient *base.ApiClient, endpoint string, params map[string]interface{}) (*base.ApiResult, error) {
	opts := base.HttpRequestOptions{
		RequestBody: params,
	}
	return apiClient.HttpGet(ctx, endpoint, opts)
}

func post(ctx *gin.Context, apiClient *base.ApiClient, endpoint string, params map[string]interface{}) (*base.ApiResult, error) {
	opts := base.HttpRequestOptions{
		RequestBody: params,
		Encode:      base.EncodeJson,
	}
	return apiClient.HttpPost(ctx, endpoint, opts)
}

func BuildQueryString(data map[string]string) string {
	buf := bytes.NewBufferString("")
	for k, v := range data {
		if buf.Len() > 0 {
			buf.WriteByte('&')
		}
		buf.WriteString(k)
		buf.WriteByte('=')
		buf.WriteString(url.QueryEscape(v))
	}

	return buf.String()
}

func GetMoatOpts(appkey, appsecret string, opts map[string]interface{}) map[string]interface{} {
	signBuf := bytes.NewBuffer(nil)
	signBuf.WriteString(appkey)
	opts["appkey"] = appkey

	var arrParamMap map[string]string
	newOpts := make(map[string]interface{})
	optKeys := make([]string, 0)
	for k, v := range opts {
		if v == nil {
			continue
		}
		optKeys = append(optKeys, k)
		switch v.(type) {
		case string:
			newOpts[k] = v.(string)
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
			newOpts[k], _ = jsoniter.MarshalToString(v)
		default:
			if reflect.TypeOf(v).Kind().String() == reflect.Slice.String() {
				newOpts[k] = v
				if arrParamMap == nil {
					arrParamMap = make(map[string]string)
				}
				arrParamMap[k], _ = jsoniter.MarshalToString(v)
			} else {
				newOpts[k], _ = jsoniter.MarshalToString(v)
			}
		}
	}

	sort.Strings(optKeys)
	for _, k := range optKeys {
		signBuf.WriteString(k)
		if arrParamMap != nil {
			if v, ok := arrParamMap[k]; ok {
				signBuf.WriteString(v)
				continue
			}
		}
		signBuf.WriteString(newOpts[k].(string))
	}
	signBuf.WriteString(appsecret)
	s := signBuf.String()
	sign := strings.ToLower(Md5(s))
	newOpts["sign"] = sign
	return newOpts
}

func Md5(str string) string {
	w := md5.New()
	_, _ = io.WriteString(w, str)
	return fmt.Sprintf("%x", w.Sum(nil))
}
