package assistantdesk

import (
	"encoding/json"
	"fwyytool/api"
	"fwyytool/conf"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.AssistantDesk,
	}
	return c
}

const (
	GetCourseArkInfoAPI        = "/assistantdesk/api/fwyytool/getcoursearkinfo"
	GetCourseServicesAPI       = "/assistantdesk/api/fwyytool/getcourseservices"
	GetBindAssistantListAPI    = "/assistantdesk/api/user/getbindassistantlist"
	GetUserInfoAPI             = "/assistantdesk/api/user/userinfo"
	ChangeSelectedAssistantAPI = "/assistantdesk/api/user/changeselectedassistant"

	GetDowngradeConfigAPI             = "/assistantdesk/api/fwyytool/getdowngradeconfig"
	SetDowngradeFieldAPI              = "/assistantdesk/api/fwyytool/setdowngradefield"
	SetDowngradeFieldTimeoutConfigAPI = "/assistantdesk/api/fwyytool/setdowngradefieldtimeoutconfig"
	DelDowngradeFieldAPI              = "/assistantdesk/api/fwyytool/deldowngradefield"

	PathQueryOpLog = "/assistantdeskgo/oplog/query"
)

func (c *Client) DelDowngradeField(ctx *gin.Context, params SetArkFieldDownGradeConfig) (err error) {

	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDesk.HttpPost(ctx, DelDowngradeFieldAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	return nil
}

func (c *Client) SetDowngradeFieldTimeoutConfig(ctx *gin.Context, params SetArkFieldDownGradeConfig) (err error) {

	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDesk.HttpPost(ctx, SetDowngradeFieldTimeoutConfigAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	return nil
}

func (c *Client) SetDowngradeField(ctx *gin.Context, params SetArkFieldDownGradeConfig) (err error) {

	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDesk.HttpPost(ctx, SetDowngradeFieldAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	return nil
}

func (c *Client) GetArkFieldDownGradeConfig(ctx *gin.Context) (resp GetArkFieldDownGradeConfigResp, err error) {
	opts := base.HttpRequestOptions{Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDesk.HttpPost(ctx, GetDowngradeConfigAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}

func (c *Client) GetCourseServices(ctx *gin.Context, params GetCourseServicesParams) (serviceList []*CourseServiceList, err error) {
	resp := GetCourseServicesResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDesk.HttpPost(ctx, GetCourseServicesAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp.ServiceList, nil
}

func (c *Client) GetCourseArkInfo(ctx *gin.Context, params GetCourseArkInfoParams) (serviceList []*GetCourseArkInfoServiceList, err error) {
	resp := GetCourseArkInfoResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDesk.HttpPost(ctx, GetCourseArkInfoAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp.ServiceList, nil
}

func (c *Client) GetBindAssistantList(ctx *gin.Context, params GetBindAssistantListParams) (assistantList []*GetBindAssistantData, err error) {
	resp := GetBindAssistantListResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDesk.HttpPost(ctx, GetBindAssistantListAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp.List, nil
}

func (c *Client) GetUserInfo(ctx *gin.Context, assistantUid int64) (userInfo GetUserInfoResp, err error) {
	params := GetUserInfoParams{
		AssistantUid: assistantUid,
	}
	userInfo = GetUserInfoResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDesk.HttpPost(ctx, GetUserInfoAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &userInfo); err != nil {
		return
	}

	return userInfo, nil
}

func (c *Client) ChangeSelectedAssistant(ctx *gin.Context, personUid int64, assistantUid int64) (err error) {
	params := ChangeSelectedAssistantParams{
		PersonUid:    personUid,
		AssistantUid: assistantUid,
	}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDesk.HttpPost(ctx, ChangeSelectedAssistantAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	return nil
}

func (c *Client) QueryOpLog(ctx *gin.Context, params OperateLogQueryReq) (resp []OperateLogQueryRsp, err error) {

	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDeskGo.HttpPost(ctx, PathQueryOpLog, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return
}
