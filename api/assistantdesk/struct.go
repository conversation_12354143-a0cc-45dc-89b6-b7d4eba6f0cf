package assistantdesk

import "encoding/json"

type SetArkFieldDownGradeConfig struct {
	FieldKey string `json:"fieldKey"`
	Timeout  int64  `json:"timeout"`
}

type GetCourseServicesParams struct {
	CourseID int64 `json:"courseId"`
}

type GetCourseArkInfoParams struct {
	CourseID int64 `json:"courseId"`
}

// GetCourseServices
type GetCourseServicesResp struct {
	ServiceList []*CourseServiceList `json:"serviceList"`
}

type CourseServiceList struct {
	ID                int64                     `json:"id"` //服务模式id
	ServiceName       string                    `json:"serviceName"`
	TplId             int64                     `json:"tplId"`
	ServiceId         int64                     `json:"serviceId"`
	ParentServiceId   int64                     `json:"parentServiceId"`
	ParentServiceName string                    `json:"parentServiceName"`
	Features          CourseServiceListFeatures `json:"features"`
}

type CourseServiceListFeatures struct {
	Tools           []int64 `json:"tools,omitempty"`
	BroadcastButton []int64 `json:"BroadcastButton,omitempty"`
}

// GetCourseArkInfo
type GetCourseArkInfoResp struct {
	ServiceList []*GetCourseArkInfoServiceList `json:"serviceList"`
}

type GetCourseArkInfoServiceList struct {
	ServiceId    int64           `json:"serviceId"`
	FieldMapTree []*FieldMapTree `json:"fieldMapTree"`
}

type FieldMapTree struct {
	FieldTypeName string         `json:"fieldTypeName"`
	SecondGroup   []*SecondGroup `json:"secondGroup"`
	Hide          int64          `json:"hide"`
}

type SecondGroup struct {
	Name string             `json:"name"`
	List []*SecondGroupList `json:"List"`
}

type SecondGroupList struct {
	Key          string          `json:"key"`
	Name         string          `json:"name"`
	RuleName     string          `json:"ruleName"`
	RuleId       int64           `json:"ruleId"`
	Hover        string          `json:"hover"`
	Hide         int64           `json:"hide"`
	FilterMap    json.RawMessage `json:"filterMap"`
	Sort         int64           `json:"sort"`
	FeConfig     json.RawMessage `json:"feConfig"`
	NewFieldType int64           `json:"newFieldType"`
	SecondGroup  string          `json:"secondGroup"`
}

type GetBindAssistantListParams struct {
	PersonUid int64 `json:"personUid"`
}

type GetBindAssistantListResp struct {
	List []*GetBindAssistantData `json:"list"`
}

const (
	SelectedAssistantTrue = 1
)

type GetBindAssistantData struct {
	AssistantUid int64  `json:"assistantUid"`
	Name         string `json:"name"`
	Phone        string `json:"phone"`
	IsSelected   int    `json:"isSelected"`
}

type GetUserInfoParams struct {
	AssistantUid int64 `json:"assistantUid"`
}

type GetUserInfoResp struct {
	AssistantUid   string `json:"assistantUid"`
	AssistantPhone string `json:"assistantPhone"`
	PersonUid      int64  `json:"personUid"`
	PersonPhone    string `json:"personPhone"`
	Uname          string `json:"uname"`
}

type ChangeSelectedAssistantParams struct {
	PersonUid    int64 `json:"personUid"`
	AssistantUid int64 `json:"assistantUid"`
}

type GetArkFieldDownGradeConfigResp struct {
	TimeoutConfig   interface{} `json:"timeoutConfig"`
	DownGradeConfig interface{} `json:"downGradeConfig"`
}

type OperateLogQueryReq struct {
	RelationId   string `json:"relationId"`
	RelationType string `json:"relationType"`
	PersonUid    int64  `json:"personUid"`
	Page         int    `json:"page"`
	PageSize     int    `json:"pageSize"`
}

type OperateLogQueryRsp struct {
	Refer             string `json:"refer"`
	Module            string `json:"module"`
	Service           string `json:"service"`
	Content           string `json:"content"`
	Before            string `json:"before"`
	Remark            string `json:"remark"`
	RelationId        string `json:"relationId"`
	RelationType      string `json:"relationType"`
	AssistantUid      int64  `json:"assistantUid"`
	LogId             string `json:"logId"`
	RequestId         string `json:"requestId"`
	PersonUid         int64  `json:"operatorUid"`
	OperateTime       int64  `json:"operateTime"`
	OperateTimeFormat string `json:"OperateTimeFormat"`
}
