package goodssearch

import (
	"encoding/json"
	"fwyytool/api"
	"fwyytool/conf"
	"fwyytool/consts"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

const (
	apiGetSkuListByConditionV2 = "/goodssearch/interface/getskulistbycondition" // https://ssv.zuoyebang.cc/static/open-sell/index.html#/docs/list?releaseId=1620719187&node=4_67_70_&docType=1
)

func GetSkuListByCondition(ctx *gin.Context, req GetSkuListByConditionReq) (resp *GetSkuListByConditionResp, err error) {
	params := map[string]interface{}{}
	temp, _ := json.Marshal(req)
	if err = json.Unmarshal(temp, &params); err != nil {
		zlog.Warnf(ctx, "GetSkuListByCondition.jsonUnMarshalErr:%v", err)
		return
	}
	params["arrConds"], _ = jsoniter.MarshalToString(req.ArrConds)
	if len(req.SourceField) > 0 {
		params["sourceField"], _ = jsoniter.MarshalToString(req.SourceField)
	}
	if len(req.ArrOrder) > 0 {
		params["arrOrder"], _ = jsoniter.MarshalToString(req.ArrOrder)
	}
	params = api.GetMoatOpts(consts.MoatAppKey, consts.MoatAppSecret, params)

	opts := base.HttpRequestOptions{RequestBody: params, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.Moat.HttpPost(ctx, apiGetSkuListByConditionV2, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetSkuListByCondition.ral failed:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return
}
