package goodssearch

import (
	"fwyytool/consts"
	"time"
)

type GetSkuListByConditionReq struct {
	ArrConds    map[string]interface{} `json:"arrConds"`              // 检索条件 ，条件字段的筛选参考：https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=174839757；非必填
	SourceField []string               `json:"sourceField,omitempty"` // 出参字段,参考：https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=174839757；非必填，默认只查skuId
	ArrOrder    map[string]string      `json:"arrOrder,omitempty"`    // 排序{"skuId":"desc","createTime":"desc"}；非必填
	Pn          int64                  `json:"pn"`                    // 偏移量，pn>=0
	Rn          int64                  `json:"rn"`                    // 数量，rn<=200
	IsAll       int64                  `json:"isAll"`                 // 1=内部测试+外部发布；0=外部发布
}

func NewGetSkuListByConditionReq() *GetSkuListByConditionReq {
	return &GetSkuListByConditionReq{
		ArrConds: map[string]interface{}{
			"op": consts.OpAnd,
			"aggs": []map[string]interface{}{
				{
					"type": 0,
					"conds": map[string]interface{}{
						"key":   "isDelete",
						"value": 0,
						"exps":  consts.ExpsEq,
					},
				},
			},
		},
		Pn:    0,
		Rn:    200,
		IsAll: 1,
	}
}

func (gslbc *GetSkuListByConditionReq) AndConds(key string, value interface{}, exps consts.Exps) *GetSkuListByConditionReq {
	if gslbc.ArrConds == nil {
		return gslbc
	}

	aggs := gslbc.ArrConds["aggs"].([]map[string]interface{})
	gslbc.ArrConds["aggs"] = append(aggs, map[string]interface{}{
		"type": 0,
		"conds": map[string]interface{}{
			"key":   key,
			"value": value,
			"exps":  exps,
		},
	})
	return gslbc
}

func (gslbc *GetSkuListByConditionReq) AddSourceFields(sourceFields []string) *GetSkuListByConditionReq {
	if gslbc.SourceField == nil {
		gslbc.SourceField = make([]string, 0, len(sourceFields))
	}

	gslbc.SourceField = append(gslbc.SourceField, sourceFields...)
	return gslbc
}

func (gslbc *GetSkuListByConditionReq) WithOnSale() *GetSkuListByConditionReq {
	if gslbc.ArrConds == nil {
		return gslbc
	}

	aggs := gslbc.ArrConds["aggs"].([]map[string]interface{})
	now := time.Now().Unix()
	saleConds := []map[string]interface{}{
		{
			"type": 0,
			"conds": map[string]interface{}{
				"key":   "status",
				"value": 1,
				"exps":  consts.ExpsEq,
			},
		},
		{
			"type": 0,
			"conds": map[string]interface{}{
				"key":   "startTime",
				"value": now,
				"exps":  consts.ExpsElt,
			},
		},
		{
			"type": 0,
			"conds": map[string]interface{}{
				"key":   "stopTime",
				"value": now,
				"exps":  consts.ExpsEgt,
			},
		},
	}

	gslbc.ArrConds["aggs"] = append(aggs, saleConds...)
	return gslbc
}

type GetSkuListByConditionResp struct {
	Total   int64 `json:"total"`
	HasMore int64 `json:"hasMore"`
	List    []GetSkuListByConditionItem
}

type GetSkuListByConditionItem struct {
	SkuId     int64 `json:"skuId"`
	Status    int64 `json:"status"`    // 上架状态；1=上架，0=下架
	StartTime int64 `json:"startTime"` // 上架时间
	StopTime  int64 `json:"stopTime"`  // 下架时间
	IsInner   int64 `json:"isInner"`   // 内外部商品；1=内部测试，0=外部发布
	// 待有缘人补充
}
