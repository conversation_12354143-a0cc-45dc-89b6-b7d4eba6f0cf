package assistantdeskgo

type AddhttpTestTraceParam struct {
	TraceID     string `json:"traceID" form:"traceID"`
	TargetURL   string `json:"targetURL" form:"targetURL"`
	Operator    string `json:"operator" form:"operator"`
	OperatorUID int64  `json:"operatorUID" form:"operatorUID"`
}

type UpdateHttpTestTraceParam struct {
	TraceID       string `json:"traceID" form:"traceID"`
	TraceReport   string `json:"traceReport" form:"traceReport"`
	Total         int64  `json:"total" form:"total"`
	TotalDuration int64  `json:"totalDuration" form:"totalDuration"`
}

type GetUnfinishedReportParam struct {
}

type GetReportListParam struct {
	OperatorUID int64 `json:"operatorUID" form:"operatorUID"`
	Pn          int64 `json:"pn" form:"pn"`
	Rn          int64 `json:"rn" form:"rn"`
}

type TraceReport struct {
	ID             int64  `json:"id"`
	TraceID        string `json:"traceID"`
	TargetURL      string `json:"targetURL"`
	TraceReportRaw string `json:"traceReport"`
	Total          int    `json:"total"`
	TotalDuration  int    `json:"totalDuration"`
	Status         int    `json:"status"`
	Operator       string `json:"operator"`
	OperatorUID    int64  `json:"operatorUID"`
	CreateTime     int64  `json:"createTime"`
	UpdateTime     int64  `json:"updateTime"`
}

type GetReportListResp struct {
	Total int64          `json:"total" form:"total"`
	List  []*TraceReport `json:"list" form:"list"`
}
