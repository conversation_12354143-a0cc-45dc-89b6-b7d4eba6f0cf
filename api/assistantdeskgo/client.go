package assistantdeskgo

import (
	"encoding/json"
	"fwyytool/api"
	"fwyytool/conf"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.AssistantDesk,
	}
	return c
}

const (
	AddHttpTestTraceAPI    = "/assistantdeskgo/api/tool/addhttptesttrace"
	UpdateHttpTestTraceAPI = "/assistantdeskgo/api/tool/updatehttptesttrace"
	GetUnfinishedReportAPI = "/assistantdeskgo/api/tool/getunfinishedtreport"
	GetReportListAPI       = "/assistantdeskgo/api/tool/getreportlist"
	GetReportAPI           = "/assistantdeskgo/api/tool/getreport"
)

func (c *Client) AddHttpTestTrace(ctx *gin.Context, params AddhttpTestTraceParam) (err error) {
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDeskGo.HttpPost(ctx, AddHttpTestTraceAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	return nil
}

func (c *Client) UpdateHttpTestTrace(ctx *gin.Context, params UpdateHttpTestTraceParam) (err error) {
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDeskGo.HttpPost(ctx, UpdateHttpTestTraceAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	return nil
}

func (c *Client) GetUnfinishedReport(ctx *gin.Context) (resp []*TraceReport, err error) {
	req := map[string]interface{}{}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDeskGo.HttpGet(ctx, GetUnfinishedReportAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return
}

func (c *Client) GetReportList(ctx *gin.Context, params GetReportListParam) (resp *GetReportListResp, err error) {
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.AssistantDeskGo.HttpGet(ctx, GetReportListAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return
}
