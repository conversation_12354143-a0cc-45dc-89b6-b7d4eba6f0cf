package tower

type (
	GetCourseInfoParams struct {
		CourseID int64 `json:"courseId" form:"courseId"`
	}

	GetAssistantClassParams struct {
		CourseId int64 `json:"courseId" form:"courseId"`
	}

	GetClassCodeParams struct {
		AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
		CourseId     int64 `json:"courseId" form:"courseId"`
	}
)

type GetCourseInfoResp struct {
	CourseId       int64 `json:"courseId"`
	CoursePriceTag int64 `json:"coursePriceTag"`
	Year           int64 `json:"year"`
	Grade          int64 `json:"grade"`
	Department     int64 `json:"department"`
	NewCourseType  int64 `json:"newCourseType"`
	Season         int64 `json:"season"`
	SaleMode       int64 `json:"saleMode"`
}
type GetPriceTagListResp struct {
	List []GetPriceTagList `json:"list"`
}

type GetPriceTagList struct {
	ID   int64  `json:"id"` //服务模式id
	Name string `json:"name"`
}

type GetAssistantClassResp struct {
	Data map[string]int64 `json:"data"` // 小班id:班主任资产uid
}

type GetClassCodeResp struct {
	CourseId     int64              `json:"courseId"`
	AssistantUid int64              `json:"assistantUid"`
	List         map[string]int64   `json:"list"`
	ClassList    []GetClassCodeItem `json:"classList"`
}

type GetClassCodeItem struct {
	ClassId       int64 `json:"classId"`
	Code          int64 `json:"code"`
	StudentMaxCnt int64 `json:"studentMaxCnt"`
}

type CourseBind struct {
	CourseBindData map[string][]CourseBindData `json:"courseBindData"`
}

type CourseBindData struct {
	CourseID   int   `json:"courseId"`
	DeviceUID  int64 `json:"deviceUid"`
	Status     int   `json:"status"`
	MapID      int   `json:"mapId"`
	CreateTime int   `json:"createTime"`
}

type GetBatchExpireTimeByCourseListResp []GetBatchExpireTimeByCourseList

type GetBatchExpireTimeByCourseList struct {
	CourseId        int64 `json:"courseId"`
	ExpireTime      int   `json:"expireTime"`
	ExpireTimeStart int   `json:"expireTimeStart"`
}
