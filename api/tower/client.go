package tower

import (
	"encoding/json"
	"errors"
	"fwyytool/api"
	"fwyytool/conf"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.Tower,
	}
	return c
}

const (
	GetCourseInfoAPI                  = "/tower/api/getcourseinfo"
	GetPriceTagListAPI                = "/tower/api/getpricetaglist"
	GetAssistantClassAPI              = "/tower/api/getassistantclass"
	GetClassCodeAPI                   = "/tower/api/getclasscode"
	GetCourseBindByCourseIdsAPI       = "/tower/api/getcoursebindbycourseids"
	GetBatchExpireTimeByCourseListAPI = "/tower/api/getbatchexpiretimebycourselist"
)

const (
	ApiMaxNum = 20
)

func makeRequest(ctx *gin.Context, params interface{}, encode string) (req base.HttpRequestOptions, err error) {
	p := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &p); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}
	req = base.HttpRequestOptions{RequestBody: p, Encode: encode}
	utils.DecorateHttpOptions(ctx, &req)
	return
}
func makeRequestNoArgs(ctx *gin.Context, encode string) (req base.HttpRequestOptions) {
	req = base.HttpRequestOptions{Encode: encode}
	utils.DecorateHttpOptions(ctx, &req)
	return
}

// Notes:根据课程id获取课程性质
// 接口提供人：刘报童
// yapi：http://yapi.zuoyebang.cc/project/5802/interface/api/212114
func (c *Client) GetCourseInfo(ctx *gin.Context, params GetCourseInfoParams) (resp GetCourseInfoResp, err error) {
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.Tower.HttpPost(ctx, GetCourseInfoAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}

func (c *Client) GetPriceTagList(ctx *gin.Context) (resp map[int64]GetPriceTagList, err error) {
	resp = map[int64]GetPriceTagList{}
	opts := base.HttpRequestOptions{Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)
	apiResp := GetPriceTagListResp{}
	res, err := conf.API.Tower.HttpPost(ctx, GetPriceTagListAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &apiResp); err != nil {
		return
	}

	for _, info := range apiResp.List {
		resp[info.ID] = info
	}
	return resp, nil
}

func (c *Client) GetAssistantClass(ctx *gin.Context, params GetAssistantClassParams) (resp GetAssistantClassResp, err error) {
	var request base.HttpRequestOptions
	request, err = makeRequest(ctx, params, base.EncodeForm)
	if err != nil {
		return
	}

	var res *base.ApiResult
	res, err = conf.API.Tower.HttpPost(ctx, GetAssistantClassAPI, request)
	if err != nil {
		return
	}
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return
}

func (c *Client) GetClassCode(ctx *gin.Context, params GetClassCodeParams) (resp GetClassCodeResp, err error) {
	var request base.HttpRequestOptions
	request, err = makeRequest(ctx, params, base.EncodeForm)
	if err != nil {
		return
	}

	var res *base.ApiResult
	res, err = conf.API.Tower.HttpPost(ctx, GetClassCodeAPI, request)
	if err != nil {
		return
	}
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return
}

func (c *Client) BatchGetClassCode(ctx *gin.Context, params []GetClassCodeParams) (respAMap map[int64]GetClassCodeResp, err error) {
	respAMap = make(map[int64]GetClassCodeResp)
	var resp GetClassCodeResp
	for _, param := range params {
		resp, err = c.GetClassCode(ctx, param)
		if err != nil {
			continue
		}

		respAMap[resp.AssistantUid] = resp
	}
	return
}

func (c *Client) BatchCourseBindByCourseId(ctx *gin.Context, courseIds []int) (bindData map[int][]CourseBindData, err error) {
	if len(courseIds) == 0 {
		return nil, errors.New("param error")
	}

	bindData = map[int][]CourseBindData{}
	chunks := fwyyutils.ChunkArrayInt(courseIds, ApiMaxNum)
	for _, chunk := range chunks {
		resp, apiErr := c.GetCourseBindByCourseId(ctx, chunk)
		if apiErr != nil {
			return
		}
		for key := range resp {
			courseId, _ := strconv.Atoi(key)
			bindData[courseId] = resp[key]
		}
	}
	return bindData, nil
}

// 根据课程id查询排班信息
// https://yapi.zuoyebang.cc/project/5802/interface/api/201970
func (c *Client) GetCourseBindByCourseId(ctx *gin.Context, courseIds []int) (bindData map[string][]CourseBindData, err error) {
	if len(courseIds) == 0 {
		return nil, errors.New("param error")
	}

	resp := CourseBind{}

	req := map[string]interface{}{
		"courseIds": fwyyutils.JoinArrayIntToString(courseIds, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetCourseBindByCourseIdsAPI, opts)
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp.CourseBindData, nil
}

// yapi: https://yapi.zuoyebang.cc/project/5802/interface/api/257319
func (c *Client) GetBatchExpireTimeByCourseList(ctx *gin.Context, courseIds []int64) (periodsMap map[int64]GetBatchExpireTimeByCourseList, err error) {
	if len(courseIds) == 0 {
		return nil, errors.New("param error")
	}
	resp := make([]GetBatchExpireTimeByCourseList, 0)

	req := map[string]interface{}{
		"courseIds": fwyyutils.JoinArrayInt64ToString(courseIds, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetBatchExpireTimeByCourseListAPI, opts)
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	periodsMap = map[int64]GetBatchExpireTimeByCourseList{}
	for idx := range resp {
		periodsMap[resp[idx].CourseId] = resp[idx]
	}

	return periodsMap, nil
}
