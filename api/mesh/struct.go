package mesh

import "strconv"

type BaseReq struct {
	AppId string `json:"appId,omitempty"`
	Ts    int64  `json:"ts,omitempty"`
	Sign  string `json:"sign,omitempty"`
}

type GetDeviceListByDeviceUidsReq struct {
	BaseReq
	DeviceUids []int64 `json:"deviceUids,omitempty"`
}

type GetDeviceListByDeviceUidsResp struct {
	DeviceId        int64  `json:"deviceId,omitempty"`
	DeviceUid       int64  `json:"deviceUid,omitempty"`
	NickName        string `json:"nickName,omitempty"`
	Phone           string `json:"phone,omitempty"`
	WxNum           string `json:"wxNum,omitempty"`
	KpAscription    int64  `json:"kpAscription,omitempty"`
	AssignClassType int64  `json:"assignClassType,omitempty"`
	WecomUserId     string `json:"wecomUserId,omitempty"`
	WecomCorpId     string `json:"wecomCorpId,omitempty"`
}

type GetDeviceInfoListReq struct {
	BaseReq
	DeviceUids []int64 `json:"deviceUids,omitempty"`
}

type GetDeviceInfoListResp struct {
	List map[string]GetDeviceInfoListItem `json:"list,omitempty"` // key是字符串的资产uid
}

func (gdr *GetDeviceInfoListResp) GetDeviceInfo(deviceId int64) (item GetDeviceInfoListItem, exist bool) {
	if gdr.List == nil || len(gdr.List) == 0 {
		return
	}
	item, exist = gdr.List[strconv.Itoa(int(deviceId))]
	return
}

type GetDeviceInfoListItem struct {
	DeviceId         int64   `json:"deviceId,omitempty"`
	Phone            string  `json:"phone,omitempty"`
	NickName         string  `json:"nickName,omitempty"`
	ShortName        string  `json:"shortName,omitempty"`
	KpAscriptionList []int64 `json:"kpAscriptionList,omitempty"`
	StaffUid         int64   `json:"staffUid,omitempty"`
	StaffName        string  `json:"staffName,omitempty"`
	StaffStatus      int64   `json:"staffStatus,omitempty"`
	WecomUserId      string  `json:"wecomUserId,omitempty"`
	WecomCorpId      string  `json:"wecomCorpId,omitempty"`
	WecomQRCode      string  `json:"wecomQRCode,omitempty"`
	WecomQRCodeUrl   string  `json:"wecomQRCodeUrl,omitempty"`
	WxQRCode         string  `json:"wxQRCode,omitempty"`
	WxQRCodeUrl      string  `json:"wxQRCodeUrl,omitempty"`
}
