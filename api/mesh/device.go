package mesh

import (
	"fwyytool/api"
	"fwyytool/conf"
	"fwyytool/libs/json"
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

const (
	apiGetDeviceListByDeviceUids = "/mesh/api/device/getdevicelistbydeviceuids"
	apiGetDeviceInfoList         = "/mesh/api/device/getdeviceinfolist"
)

const (
	defaultAppId = "CC71C24B8F6BEDDA"
)

func GetDeviceListByDeviceUids(ctx *gin.Context, req GetDeviceListByDeviceUidsReq) (respList []GetDeviceListByDeviceUidsResp, err error) {
	params := map[string]interface{}{}
	temp, _ := json.Marshal(req)
	if err = json.Unmarshal(temp, &params); err != nil {
		zlog.Warnf(ctx, "GetDeviceListByDeviceUids.jsonUnMarshalErr:%v", err)
		return
	}

	err = encrypt(defaultAppId, params)
	if err != nil {
		zlog.Warnf(ctx, "GetDeviceListByDeviceUids.encrypt params:%+v, failed:%v", params, err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: params, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.Mesh.HttpGet(ctx, apiGetDeviceListByDeviceUids, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetDeviceListByDeviceUids.ral failed:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	if _, err = api.DecodeResponse(ctx, res, &respList); err != nil {
		return
	}
	return
}

func GetDeviceInfoList(ctx *gin.Context, req GetDeviceInfoListReq) (resp GetDeviceInfoListResp, err error) {
	params := map[string]interface{}{}
	temp, _ := json.Marshal(req)
	if err = json.Unmarshal(temp, &params); err != nil {
		zlog.Warnf(ctx, "GetDeviceInfoList.jsonUnMarshalErr:%v", err)
		return
	}

	err = encrypt(defaultAppId, params)
	if err != nil {
		zlog.Warnf(ctx, "GetDeviceInfoList.encrypt params:%+v, failed:%v", params, err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: params, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := conf.API.Mesh.HttpGet(ctx, apiGetDeviceInfoList, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetDeviceInfoList.ral failed:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return
}

func encrypt(appId string, data map[string]interface{}) (err error) {
	timestamp := time.Now().Unix()
	data["ts"] = timestamp

	appKey, err := genAppKey(appId, data)
	if err != nil {
		return
	}

	data["appKey"] = appKey
	data["appId"] = appId
	return
}

func genAppKey(appId string, data map[string]interface{}) (appKey string, err error) {
	dataStr, err := json.MarshalToString(data)
	if err != nil {
		return
	}
	appKey = api.Md5(appId + dataStr)
	return
}
