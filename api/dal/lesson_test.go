package dal

import (
	"encoding/json"
	"reflect"
	"testing"

	"genke/api"
	"genke/components"
)

func TestDecodeLessonInfo(t *testing.T) {
	components.InitTestLog()
	params := []struct {
		body []byte
		exp  LessonInfo
	}{
		{
			body: []byte(`{"lessonId":537217,"lessonName":"Unit 2 The Magician","startTime":**********,"stopTime":**********,"finishTime":**********,"status":1,"outlineId":166873,"courseId":[605817],"lessonType":1,"hasHomework":0,"hasPlayback":1,"previewNoteUri":"","classNoteUri":"fudao_3f64db3b9e82293c14e059da478a6665.pdf","reopenLessonId":0,"fileList":"","serviceInfo":[{"containerId":537217,"containerType":4,"serviceProvider":0,"serviceId":2397,"serviceName":"章节课前出境","serviceStatus":0,"extData":{"assistantInfo":{"teachTimeLength":900,"assistantInfoLiveStage":2149}}},{"containerId":537217,"containerType":4,"serviceProvider":12,"serviceId":2473,"serviceName":"小英直播间","serviceStatus":0,"extData":[]}],"stageTest":""}`),
			exp: LessonInfo{
				LessonId:       537217,
				LessonName:     "Unit 2 The Magician",
				StartTime:      **********,
				StopTime:       **********,
				FinishTime:     **********,
				Status:         1,
				OutlineId:      166873,
				CourseId:       []int{605817},
				LessonType:     1,
				HasHomework:    0,
				HasPlayback:    1,
				PreviewNoteUri: "",
				ClassNoteUri:   "fudao_3f64db3b9e82293c14e059da478a6665.pdf",
				ReopenLessonId: 0,
				FileList:       "",
				ServiceInfo: []ServiceInfo{
					{
						ContainerId:     537217,
						ContainerType:   4,
						ServiceProvider: 0,
						ServiceId:       2397,
						ServiceName:     "章节课前出境",
						ServiceStatus:   0,
						ExtData: map[string]interface{}{
							"assistantInfo": map[string]interface{}{
								"assistantInfoLiveStage": float64(2149),
								"teachTimeLength":        float64(900),
							},
						},
					},
					{
						ContainerId:     537217,
						ContainerType:   4,
						ServiceProvider: 12,
						ServiceId:       2473,
						ServiceName:     "小英直播间",
						ServiceStatus:   0,
						ExtData:         []interface{}{},
					},
				},
				StageTest: "",
			},
		},
	}

	for _, p := range params {
		var data interface{}
		if err := json.Unmarshal(p.body, &data); err != nil {
			t.Fatalf("json decode failed, %s", err)
		}

		var output LessonInfo
		if err := api.DecodeInterface(nil, data, &output); err != nil {
			t.Fatalf("api decode failed, %s", err)
		}

		if !reflect.DeepEqual(output, p.exp) {
			t.Fatalf("not equal, got:\n%+v\nwant:\n%+v", output, p.exp)
		}
	}
}
