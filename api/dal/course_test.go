package dal

import (
	"encoding/json"
	"testing"

	"github.com/magiconair/properties/assert"

	"genke/api"
	"genke/components"
)

func TestDecodeCourseInfo(t *testing.T) {
	components.InitTestLog()
	params := []struct {
		body []byte
		exp  CourseInfo
	}{
		{
			body: []byte(`{"courseId":593893,"courseName":"【2021寒】四年级语文提升班（综合读写）","grades":[14,1],"subjects":[1],"courseType":2,"season":4,"numberPeriods":"1","learnSeason":41,"year":2021,"status":1,"isInner":0,"cpuId":82174,"courseTags":[],"brandId":1,"content":"","startTime":**********,"newCourseType":2,"firstLessonTime":**********,"lastLessonStopTime":**********,"onlineFormatTime":"1月26日-2月2日 每天 18:30-20:20 共8次课","onlineFormatTimeAll":"1月26日-2月2日 每天 18:30-20:20 共8次课","lessonCnt":8,"lessonCompleteCnt":8,"coreLessonCnt":7,"coreLessonCompleteCnt":7,"hasMaterial":1,"mainGradeId":14,"mainSubjectId":1,"vipClass":0,"finishTime":**********,"lectureSendTime":**********,"formatShow":1,"deleteTime":0,"deleteResason":"","serviceInfo":[{"containerId":593893,"containerType":2,"serviceProvider":2,"serviceId":19,"serviceName":"教辅服务","serviceStatus":0,"extData":{"lectureSendTime":**********}},{"containerId":593893,"containerType":2,"serviceProvider":1,"serviceId":32,"serviceName":"报后测","serviceStatus":0,"extData":[]}]}`),
			exp: CourseInfo{
				CourseId:              593893,
				CourseName:            "【2021寒】四年级语文提升班（综合读写）",
				Grades:                []int{14, 1},
				Subjects:              []int{1},
				CourseType:            2,
				Season:                4,
				NumberPeriods:         "1",
				LearnSeason:           41,
				Year:                  2021,
				Status:                1,
				IsInner:               0,
				CpuId:                 82174,
				CourseTags:            []interface{}{},
				BrandId:               1,
				Content:               "",
				StartTime:             **********,
				NewCourseType:         2,
				FirstLessonTime:       **********,
				LastLessonStopTime:    **********,
				OnlineFormatTime:      "1月26日-2月2日 每天 18:30-20:20 共8次课",
				OnlineFormatTimeAll:   "1月26日-2月2日 每天 18:30-20:20 共8次课",
				LessonCnt:             8,
				LessonCompleteCnt:     8,
				CoreLessonCnt:         7,
				CoreLessonCompleteCnt: 7,
				HasMaterial:           1,
				MainGradeId:           14,
				MainSubjectId:         1,
				VipClass:              0,
				FinishTime:            **********,
				LectureSendTime:       **********,
				FormatShow:            1,
				DeleteTime:            0,
				DeleteResason:         "",
				ServiceInfo: []ServiceInfo{
					{
						ContainerId:     593893,
						ContainerType:   2,
						ServiceProvider: 2,
						ServiceId:       19,
						ServiceName:     "教辅服务",
						ServiceStatus:   0,
						ExtData:         map[string]interface{}{"lectureSendTime": float64(**********)},
					},
					{
						ContainerId:     593893,
						ContainerType:   2,
						ServiceProvider: 1,
						ServiceId:       32,
						ServiceName:     "报后测",
						ServiceStatus:   0,
						ExtData:         []interface{}{},
					},
				},
			},
		},
	}

	for _, p := range params {
		var data interface{}
		if err := json.Unmarshal(p.body, &data); err != nil {
			t.Fatalf("json decode failed, %s", err)
		}

		var output CourseInfo
		if err := api.DecodeInterface(nil, data, &output); err != nil {
			t.Fatalf("api decode failed, %s", err)
		}

		assert.Equal(t, output, p.exp)
	}
}
