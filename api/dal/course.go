package dal

import (
	"errors"
	"fwyytool/components"
	"sync"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"

	"fwyytool/api/zbcore"
	"fwyytool/conf"
)

const (
	CourseModule         = "dal"
	CourseEntity         = "course"
	CourseConcurrencyMax = 100 // 取课程的最大并必度
)

type CourseInfo struct {
	BrandId               int           `json:"brandId" mapstructure:"brandId"`
	Content               string        `json:"content" mapstructure:"content"` // JSON
	CoreLessonCnt         int           `json:"coreLessonCnt" mapstructure:"coreLessonCnt"`
	CoreLessonCompleteCnt int           `json:"coreLessonCompleteCnt" mapstructure:"coreLessonCompleteCnt"`
	CourseId              int           `json:"courseId" mapstructure:"courseId"`
	CourseName            string        `json:"courseName" mapstructure:"courseName"`
	CourseTags            []interface{} `json:"courseTags" mapstructure:"courseTags"`
	CourseType            int           `json:"courseType" mapstructure:"courseType"`
	CpuId                 int           `json:"cpuId" mapstructure:"cpuId"`
	DeleteResason         string        `json:"deleteResason" mapstructure:"deleteResason"`
	DeleteTime            int           `json:"deleteTime" mapstructure:"deleteTime"`
	FinishTime            int           `json:"finishTime" mapstructure:"finishTime"`
	FirstLessonTime       int           `json:"firstLessonTime" mapstructure:"firstLessonTime"`
	FormatShow            int           `json:"formatShow" mapstructure:"formatShow"`
	Grades                []int         `json:"grades" mapstructure:"grades"`
	HasMaterial           int           `json:"hasMaterial" mapstructure:"hasMaterial"`
	IsInner               int           `json:"isInner" mapstructure:"isInner"`
	LastLessonStopTime    int           `json:"lastLessonStopTime" mapstructure:"lastLessonStopTime"`
	LearnSeason           int           `json:"learnSeason" mapstructure:"learnSeason"`
	LectureSendTime       int           `json:"lectureSendTime" mapstructure:"lectureSendTime"`
	LessonCnt             int           `json:"lessonCnt" mapstructure:"lessonCnt"`
	LessonCompleteCnt     int           `json:"lessonCompleteCnt" mapstructure:"lessonCompleteCnt"`
	MainGradeId           int           `json:"mainGradeId" mapstructure:"mainGradeId"`
	MainSubjectId         int           `json:"mainSubjectId" mapstructure:"mainSubjectId"`
	NewCourseType         int           `json:"newCourseType" mapstructure:"newCourseType"`
	NumberPeriods         string        `json:"numberPeriods" mapstructure:"numberPeriods"`
	OnlineFormatTime      string        `json:"onlineFormatTime" mapstructure:"onlineFormatTime"`
	OnlineFormatTimeAll   string        `json:"onlineFormatTimeAll" mapstructure:"onlineFormatTimeAll"`
	Season                int           `json:"season" mapstructure:"season"`
	StartTime             int           `json:"startTime" mapstructure:"startTime"`
	Status                int           `json:"status" mapstructure:"status"`
	Subjects              []int         `json:"subjects" mapstructure:"subjects"`
	VipClass              int           `json:"vipClass" mapstructure:"vipClass"`
	Year                  int           `json:"year" mapstructure:"year"`
	ServiceInfo           []ServiceInfo `json:"serviceInfo" mapstructure:"serviceInfo"`
}

type CourseLessonInfo struct {
	CourseInfo `mapstructure:",squash"`
	LessonList map[int]LessonInfo `mapstructure:"lessonList"`
}

func getCourseAllFields() []string {
	return []string{
		"courseId",
		"courseName",
		"grades",     //array [2,4   Zb_Const_GradeSubject 课程年纪,学科枚举值
		"subjects",   //array [3,4]
		"courseType", //int 课程类型   Zb_Const_Course 枚举类
		"season",     //1、2、3、4 春暑秋寒
		"numberPeriods",
		"learnSeason",           //int 学季Id    Zb_Const_LearnSeason  学季枚举信息
		"year",                  //学年
		"firstLessonTime",       //第一章节开课时间
		"lastLessonStopTime",    //最后一节课结束时间
		"status",                //课程状态       Zb_Const_Course  枚举类
		"isInner",               //是否内部课
		"onlineFormatTime",      //课程上课时间(格式化好的与app端同步)
		"onlineFormatTimeAll",   //上课时间格式化，展示频率最高的时间
		"lessonCnt",             //有效章节总数
		"lessonCompleteCnt",     //有效已上章节数量
		"coreLessonCnt",         //有效主题课章节数量
		"coreLessonCompleteCnt", //有效主题课已上章节数量
		"hasMaterial",           //是否有教材
		"mainGradeId",           //int 主年级字段
		"mainSubjectId",         //int 主学科字段
		"vipClass",              // VIP课程
		"finishTime",            // 结束时间
		"lectureSendTime",       // 教材发送时间
		"formatShow",            // content展示方式是html还是格式化信息
		"deleteTime",            // 删除时间
		"deleteReason",          // 删除原因
		"cpuId",                 // cpuID
		"courseTags",            //课程标签
		"brandId",               //课程类型
		"content",               //内容
		"startTime",             //开始时间
		"newCourseType",         //newCourseType
		"serviceInfo",
	}
}

// GetCourseInfoByCourseId 获取课程信息
func GetCourseInfoByCourseId(ctx *gin.Context, courseId int) (courseInfo CourseInfo, err error) {
	if courseId <= 0 {
		err = errors.New("param error")
		return
	}

	var courseMap map[int]CourseInfo
	err = getKVByCourseId(ctx, []int{courseId}, getCourseAllFields(), nil, nil, &courseMap)
	if err != nil {
		return
	}
	courseInfo = courseMap[courseId]
	return
}

// 获取课程基本信息
func GetCourseBaseByCourseId(ctx *gin.Context, courseId int, fields []string) (CourseInfo, error) {
	if courseId <= 0 {
		return CourseInfo{}, errors.New("param error")
	}

	var courseMap map[int]CourseInfo
	err := getKVByCourseId(ctx, []int{courseId}, fields, nil, nil, &courseMap)
	if err != nil {
		return CourseInfo{}, err
	}

	return courseMap[courseId], nil
}

func GetCourseBaseByCourseIds(ctx *gin.Context, courseIds []int, fields []string) (map[int]CourseInfo, error) {
	if len(courseIds) == 0 {
		return nil, errors.New("param error")
	}

	if len(courseIds) > ApiMaxNum {
		return getCourseBaseByCourseIdsInBatch(ctx, courseIds, fields)
	} else {
		var courseMap map[int]CourseInfo
		err := getKVByCourseId(ctx, courseIds, fields, nil, nil, &courseMap)
		if err != nil {
			return nil, err
		}

		return courseMap, nil
	}
}

// 课程详情接口
// courseIds      待查询课程ID json list [123, 234, 345]
// courseFields   接口返回的Course字段
// lessonFields   接口返回的Lesson字段
// materialFields 接口返回的Material字段
func getKVByCourseId(ctx *gin.Context, courseIds []int, courseFields []string, lessonFields []string,
	materialFields []string, output interface{}) error {

	if len(courseFields) == 0 {
		courseFields = getCourseAllFields()
	}

	if len(lessonFields) == 0 {
		lessonFields = []string{}
	}

	if len(materialFields) == 0 {
		materialFields = []string{}
	}

	arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, CourseModule, CourseEntity, "getKV", false, conf.GetAppName())
	params := map[string]interface{}{
		"courseIds":      courseIds,
		"courseFields":   courseFields,
		"lessonFields":   lessonFields,
		"materialFields": materialFields,
		"allFields":      1,
	}

	_, err := zbcore.PostDal(ctx, params, arrHeader, output)
	if err != nil {
		zlog.Warnf(ctx, "getKVByCourseId failed, err:%s", err)
		return err
	}

	return nil
}

// 课程批量调用
func getCourseBaseByCourseIdsInBatch(ctx *gin.Context, courseIds []int, courseFields []string) (map[int]CourseInfo, error) {
	chunks := components.Util.ChunkArrayInt(courseIds, ApiMaxNum)

	wg := &sync.WaitGroup{}
	ch := make(chan map[int]CourseInfo)
	maxCon := make(chan int, CourseConcurrencyMax)

	for _, chunk := range chunks {
		maxCon <- 1
		wg.Add(1)
		go func(uids []int) {
			defer wg.Done()
			var ret map[int]CourseInfo
			err := getKVByCourseId(ctx, uids, courseFields, nil, nil, &ret)
			if err != nil {
				return
			}

			ch <- ret
			<-maxCon
		}(chunk)
	}

	result := make(map[int]CourseInfo)
	colWg := sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer colWg.Done()
		for ret := range ch {
			for courseId, courseInfo := range ret {
				result[courseId] = courseInfo
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result, nil
}

// 根据课程Id获取课程信息和章节信息
func GetCourseLessonInfoByCourseId(ctx *gin.Context, courseId int, courseFields []string, lessonFields []string) (CourseLessonInfo, error) {
	ret, err := GetCourseLessonInfoByCourseIds(ctx, []int{courseId}, courseFields, lessonFields)
	if err != nil {
		return CourseLessonInfo{}, err
	}

	if info, ok := ret[courseId]; ok {
		return info, nil
	}

	return CourseLessonInfo{}, nil
}

func GetCourseLessonInfoByCourseIds(ctx *gin.Context, courseIds []int, courseFields, lessonFields []string) (map[int]CourseLessonInfo, error) {
	if len(courseIds) == 0 {
		return nil, errors.New("courseIds不能为空")
	}

	if len(courseIds) > ApiMaxNum {
		// 批量并发获取
		return courseLessonBatch(ctx, courseIds, courseFields, lessonFields), nil
	} else {
		return courseLessonSingle(ctx, courseIds, courseFields, lessonFields)
	}
}

func courseLessonSingle(ctx *gin.Context, courseIds []int, courseFields, lessonFields []string) (map[int]CourseLessonInfo, error) {
	if len(courseFields) == 0 {
		courseFields = getCourseAllFields()
	}

	if len(lessonFields) == 0 {
		lessonFields = getLessonAllFields()
	}

	var courseLessonMap map[int]CourseLessonInfo
	err := getKVByCourseId(ctx, courseIds, courseFields, lessonFields, nil, &courseLessonMap)
	if err != nil {
		return nil, err
	}

	return courseLessonMap, nil
}

func courseLessonBatch(ctx *gin.Context, courseIds []int, courseFields, lessonFields []string) map[int]CourseLessonInfo {
	chunks := components.Util.ChunkArrayInt(courseIds, ApiMaxNum)
	wg := &sync.WaitGroup{}
	ch := make(chan map[int]CourseLessonInfo)
	for _, chunk := range chunks {
		wg.Add(1)
		go func(cids []int) {
			defer wg.Done()
			ret, err := courseLessonSingle(ctx, cids, courseFields, lessonFields)
			if err != nil {
				zlog.Warnf(ctx, "courseLessonSingle failed, courseIds:%+v, err:%s", cids, err)
				return
			}

			ch <- ret
		}(chunk)
	}

	result := make(map[int]CourseLessonInfo)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer colWg.Done()
		for singleRet := range ch {
			for k, v := range singleRet {
				result[k] = v
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result
}
