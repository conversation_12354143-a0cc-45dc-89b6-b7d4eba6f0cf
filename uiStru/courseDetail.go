package uiStru

type CourseDetail struct {
	CourseID         int64
	CourseName       string
	Year             int64
	SeasonName       string
	CourseTypeName   string
	Content          string
	OnlineFormatTime string
	CpuID            int64
	IsInner          string
	SubjectName      string
	GradeName        string
	OwnPackage       string
	LessonList       []CourseLessonInfo
}
type CourseLessonInfo struct {
	LessonID       int
	LessonName     string
	LessonDate     string
	OutlineID      int
	PlayTypeName   string
	LessonTypeName string
	StatusName     string
}
