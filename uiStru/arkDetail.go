package uiStru

import (
	"fwyytool/api/assistantdesk"
)

type ArkDetail struct {
	CourseID           int64
	CourseName         string
	CoursePriceTag     int64
	CoursePriceTagName string
	Year               int64
	SeasonName         string
	CourseTypeName     string
	Department         int64
	DepartName         string
	ServiceList        []GetCourseServiceList
}

type GetCourseServiceList struct {
	ID                int64 //服务模式id
	ServiceName       string
	TplId             int64
	ServiceId         int64
	ParentServiceId   int64
	ParentServiceName string
	FiledMapTreeList  []*assistantdesk.FieldMapTree
}
