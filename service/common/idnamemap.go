package common

import (
	"fwyytool/api/coursebase"
	"fwyytool/api/mesh"
	"fwyytool/api/newgoodsplatform"
	"fwyytool/api/userprofile"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetCategoryIdNameMap 获取 商品id名称 map
func GetCategoryIdNameMap(ctx *gin.Context, categoryIds []int64) (categoryIdNameMap map[int64]string) {
	categoryIdNameMap = make(map[int64]string)
	if len(categoryIds) == 0 {
		return
	}

	categoryReq := newgoodsplatform.GetCategoryFieldKVByCategoryIdListReq{
		CategoryIdList: categoryIds,
	}
	categoryRespList, err := newgoodsplatform.GetCategoryFieldKVByCategoryIdList(ctx, categoryReq)
	if err != nil {
		zlog.Warnf(ctx, "GetCategoryIdNameMap get failed, categoryReq: %+v, err: %+v", categoryReq, err)
		return
	}

	for _, categoryResp := range categoryRespList {
		categoryIdNameMap[categoryResp.CategoryId] = categoryResp.CategoryName
	}
	return
}

// GetKeyMap 获取基础keys id名称 map
func GetKeyMap(ctx *gin.Context, key coursebase.Key) (keyMap map[int64]string) {
	keyMap = make(map[int64]string)
	keysReq := coursebase.KeysReq{
		Keys: []coursebase.Key{
			key,
		},
	}
	keysMap, err := coursebase.KeysMap(ctx, keysReq)
	if err != nil {
		zlog.Warnf(ctx, "GetKeyMap get failed, keysReq: %+v, err: %+v", keysReq, err)
		return
	}
	if values, ok := keysMap[key]; ok {
		keyMap = values
	}
	return
}

// GetPersonUidNameMap 获取真人 id名称 map
func GetPersonUidNameMap(ctx *gin.Context, personUids []int64) (personUidNameMap map[int64]string) {
	personUidNameMap = make(map[int64]string)
	if len(personUids) == 0 {
		return
	}

	personReq := userprofile.GetStaffInfoByUidsReq{
		UserIds: personUids,
	}
	personResp, err := userprofile.NewClient().GetStaffInfoByUids(ctx, personReq)
	if err != nil {
		zlog.Warnf(ctx, "GetPersonUidNameMap get failed, personReq: %+v, err: %+v", personReq, err)
		return
	}
	if len(personResp.List) == 0 {
		return
	}

	for _, item := range personResp.List {
		personUidNameMap[item.UserId] = item.UserName
	}
	return
}

// GetDeviceUidNameMap 获取资产 id名称 map
func GetDeviceUidNameMap(ctx *gin.Context, deviceUids []int64) (deviceUidNameMap map[int64]string) {
	deviceUidNameMap = make(map[int64]string)
	if len(deviceUids) == 0 {
		return
	}

	deviceReq := mesh.GetDeviceListByDeviceUidsReq{
		DeviceUids: deviceUids,
	}
	deviceList, err := mesh.GetDeviceListByDeviceUids(ctx, deviceReq)
	if err != nil {
		zlog.Warnf(ctx, "GetDeviceUidNameMap get failed, deviceReq: %+v, err: %+v", deviceReq, err)
		return
	}

	for _, deviceResp := range deviceList {
		deviceUidNameMap[deviceResp.DeviceUid] = deviceResp.NickName
	}
	return
}
