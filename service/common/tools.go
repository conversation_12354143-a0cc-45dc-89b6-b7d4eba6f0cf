package common

import (
	"encoding/hex"
	"fwyytool/uiStru"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"github.com/gin-gonic/gin"
	"strconv"
)

var Tools tools

type tools struct {
}

func (s tools) IdCode(ctx *gin.Context, encodeInput string, decodeInput string) (data uiStru.ToolsIdCode, err error) {
	if encodeInput != "" {
		encodeInputInt, _ := strconv.Atoi(encodeInput)
		data.EncodeQidValue, _ = utils.EncodeQid(encodeInputInt, 0)
		data.EncodeQidQbValue, _ = utils.EncodeQid(encodeInputInt, 1)
		data.EncodeAQidValue, _ = utils.EncodeAQid(encodeInputInt)
		data.EncodeUidValue, _ = utils.EncodeUid(encodeInputInt)
		data.EncodeCidValue, _ = utils.EncodeCid(encodeInputInt)
		data.EncodeLidValue, _ = utils.EncodeLid(encodeInputInt)
		data.EncodeOuidValue, _ = utils.EncodeOuid(encodeInputInt)
		data.EncodePhoneValue = EncodePhone(encodeInput)
	}
	if decodeInput != "" {
		ignore := func() {
			if r := recover(); r != nil {
				return
			}
		}
		data.DecodeQidValue = func(decodeInput string) string {
			defer ignore()
			_v, _e := utils.DecodeQid(decodeInput, 0)
			if _e == nil {
				return strconv.Itoa(_v)
			}
			return ""
		}(decodeInput)
		data.DecodeQidQbValue = func(decodeInput string) string {
			defer ignore()
			_v, _e := utils.DecodeQid(decodeInput, 1)
			if _e == nil {
				return strconv.Itoa(_v)
			}
			return ""
		}(decodeInput)
		data.DecodeAQidValue = func(decodeInput string) string {
			defer ignore()
			_v, _e := utils.DecodeAQid(decodeInput)
			if _e == nil {
				return strconv.Itoa(_v)
			}
			return ""
		}(decodeInput)
		data.DecodeUidValue = func(decodeInput string) string {
			defer ignore()
			_v, _e := utils.DecodeUid(decodeInput)
			if _e == nil {
				return strconv.Itoa(_v)
			}
			return ""
		}(decodeInput)
		data.DecodeCidValue = func(decodeInput string) string {
			defer ignore()
			_v, _e := utils.DecodeCid(decodeInput)
			if _e == nil {
				return strconv.Itoa(_v)
			}
			return ""
		}(decodeInput)
		data.DecodeLidValue = func(decodeInput string) string {
			defer ignore()
			_v, _e := utils.DecodeLid(decodeInput)
			if _e == nil {
				return strconv.Itoa(_v)
			}
			return ""
		}(decodeInput)
		data.DecodeOuidValue = func(decodeInput string) string {
			defer ignore()
			_v, _e := utils.DecodeOuid(decodeInput)
			if _e == nil {
				return strconv.Itoa(_v)
			}
			return ""
		}(decodeInput)
		data.DecodePhoneValue = DecodePhone(decodeInput)
	}
	return
}

func EncodePhone(phone string) (encrypt string) {
	data := []byte(phone)
	key := []byte("5Yc&^uEz")
	encrypted := utils.AESEncrypt(data, key)
	encrypt = hex.EncodeToString(encrypted)
	return
}

func DecodePhone(encrypt string) (phone string) {
	defer func() {
		if r := recover(); r != nil {
			return
		}
	}()

	key := []byte("5Yc&^uEz")
	encrypted, _ := hex.DecodeString(encrypt)
	data := utils.AESDecrypt(encrypted, key)
	phone = string(data)
	return
}
