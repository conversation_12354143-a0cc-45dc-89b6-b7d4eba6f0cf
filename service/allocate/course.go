package allocate

import (
	"fwyytool/api/tower"
	"fwyytool/controllers/http/allocate/dto"
	"github.com/gin-gonic/gin"
)

func GetCourseInfo(ctx *gin.Context, req dto.GetCourseInfoReq) (resp dto.GetCourseInfoResp, err error) {
	// 排班课程信息
	courseInfoParams := tower.GetCourseInfoParams{
		CourseID: req.CourseId,
	}
	var courseInfo tower.GetCourseInfoResp
	courseInfo, err = tower.NewClient().GetCourseInfo(ctx, courseInfoParams)
	if err != nil {
		return
	}

	// 排班课程资产
	assistantByCourseIdParams := tower.GetAssistantClassParams{
		CourseId: req.CourseId,
	}
	var assistantClass tower.GetAssistantClassResp
	assistantClass, err = tower.NewClient().GetAssistantClass(ctx, assistantByCourseIdParams)
	if err != nil {
		return
	}

	if len(assistantClass.Data) == 0 {
		return makeRet(ctx, courseInfo, assistantClass.Data)
	}

	// 排班课程小班
	batchReqs := make([]tower.GetClassCodeParams, 0, len(assistantClass.Data))
	for _, assistantUid := range assistantClass.Data {
		batchReqs = append(batchReqs, tower.GetClassCodeParams{
			AssistantUid: assistantUid,
			CourseId:     req.CourseId,
		})
	}

	var acClassMap map[int64]tower.GetClassCodeResp
	acClassMap, err = tower.NewClient().BatchGetClassCode(ctx, batchReqs)
	if err != nil {
		return
	}

	return makeRetWithClass(ctx, courseInfo, assistantClass.Data, acClassMap)
}

func makeRet(ctx *gin.Context, courseInfo tower.GetCourseInfoResp, assistantList map[string]int64) (resp dto.GetCourseInfoResp, err error) {
	return makeRetWithClass(ctx, courseInfo, assistantList, map[int64]tower.GetClassCodeResp{})
}

func makeRetWithClass(ctx *gin.Context, courseInfo tower.GetCourseInfoResp, assistantList map[string]int64, acClassMap map[int64]tower.GetClassCodeResp) (resp dto.GetCourseInfoResp, err error) {
	resp.CourseId = courseInfo.CourseId
	resp.NewCourseType = courseInfo.NewCourseType
	resp.PriceTagId = courseInfo.CoursePriceTag
	resp.SaleMode = courseInfo.SaleMode
	resp.Year = courseInfo.Year
	resp.Season = courseInfo.Season
	resp.Department = courseInfo.Department
	resp.GradeId = courseInfo.Grade

	acList := make([]dto.GetCourseInfoAcItem, 0, len(assistantList))
	for _, assistantUid := range assistantList {
		item := dto.GetCourseInfoAcItem{
			AssistantUid: assistantUid,
		}

		classResp, ok := acClassMap[assistantUid]
		if !ok {
			acList = append(acList, item)
			continue
		}

		classList := make([]dto.GetCourseInfoClassItem, 0, len(classResp.ClassList))
		for _, classItem := range classResp.ClassList {
			classList = append(classList, dto.GetCourseInfoClassItem{
				ClassId:       classItem.ClassId,
				Code:          classItem.Code,
				StudentMaxCnt: classItem.StudentMaxCnt,
			})
		}
		item.ClassList = classList
		acList = append(acList, item)
	}
	resp.AcList = acList
	return
}
