package desk

import (
	"fwyytool/api/arkgo"
	uiStru "fwyytool/uiStru"
	"github.com/gin-gonic/gin"
)

var ArkTestCaseService arkTestCaseService

type arkTestCaseService struct {
}

func (s arkTestCaseService) GetArkTestCaseByRuleId(ctx *gin.Context, ruleId int64, isInner int) (data uiStru.ArkTestCaseDetail, err error) {
	arkTestCaseDetail, err := arkgo.NewClient().GetArkDetailByRuleId(ctx, arkgo.GetArkDetailByRuleIdParams{RuleId: ruleId, IsInner: isInner})
	if err != nil {
		return
	}
	data = uiStru.ArkTestCaseDetail(arkTestCaseDetail)
	return
}

func (s arkTestCaseService) GetArkTestCaseByCname(ctx *gin.Context, cname string, isInner int) (data uiStru.ArkTestCaseDetail, err error) {
	arkTestCaseDetail, err := arkgo.NewClient().GetArkDetailByCname(ctx, arkgo.GetArkDetailByCnameParams{Cname: cname, IsInner: isInner})
	if err != nil {
		return
	}
	data = uiStru.ArkTestCaseDetail(arkTestCaseDetail)
	return
}

func (s arkTestCaseService) GetArkTestCaseByToolId(ctx *gin.Context, toolId int64, componentKey string, isInner int) (data uiStru.ArkTestCaseToolDetail, err error) {
	arkTestCaseDetail, err := arkgo.NewClient().GetArkFeatureToolDetailByToolId(ctx, arkgo.GetArkFeatureToolDetailByToolIdParams{ToolId: toolId, ComponentKey: componentKey, IsInner: isInner})
	if err != nil {
		return
	}
	data = uiStru.ArkTestCaseToolDetail(arkTestCaseDetail)
	return
}
