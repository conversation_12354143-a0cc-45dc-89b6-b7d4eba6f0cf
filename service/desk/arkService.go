package desk

import (
	"errors"
	"fwyytool/api/assistantdesk"
	"fwyytool/api/dal"
	"fwyytool/api/tower"
	"fwyytool/components"
	"fwyytool/controllers/http/desk/input"
	"fwyytool/stru"
	"fwyytool/uiStru"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

var ArkService arkService

type arkService struct {
}

func (s arkService) Detail(ctx *gin.Context, param input.DetailParam) (data uiStru.ArkDetail, err error) {
	courseInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, int(param.CourseID), []string{}, []string{})
	if err != nil {
		return
	}
	//fmt.Println(courseInfo)
	coursePriceTagInfo, err := tower.NewClient().GetCourseInfo(ctx, tower.GetCourseInfoParams{
		CourseID: param.CourseID,
	})
	if err != nil {
		return
	}

	data.CourseID = int64(courseInfo.CourseId)
	data.CourseName = courseInfo.CourseName
	data.CoursePriceTag = coursePriceTagInfo.CoursePriceTag
	data.SeasonName = components.GetLearnSeanFromat(int(coursePriceTagInfo.Season))
	data.Year = coursePriceTagInfo.Year
	data.CourseTypeName = components.GetCourseTypeName(int(coursePriceTagInfo.NewCourseType))
	data.Department = coursePriceTagInfo.Department
	depart := components.GradeDepartMap[int(coursePriceTagInfo.Department)]
	data.DepartName = "未知"
	if _, ok := components.DepartNameMap[depart]; ok {
		data.DepartName = components.DepartNameMap[depart]
	}

	priceTagMap, err := tower.NewClient().GetPriceTagList(ctx)
	if err != nil {
		return
	}

	if _, ok := priceTagMap[coursePriceTagInfo.CoursePriceTag]; ok {
		data.CoursePriceTagName = priceTagMap[coursePriceTagInfo.CoursePriceTag].Name
	}

	serviceList, err := assistantdesk.NewClient().GetCourseServices(ctx, assistantdesk.GetCourseServicesParams{CourseID: param.CourseID})
	if err != nil {
		return
	}

	zlog.Infof(ctx, "serviceList:%+v", serviceList)

	arkInfoServiceList, arkErr := assistantdesk.NewClient().GetCourseArkInfo(ctx, assistantdesk.GetCourseArkInfoParams{CourseID: param.CourseID})
	if arkErr != nil {
		return
	}

	zlog.Infof(ctx, "arkInfoServiceList:%+v", arkInfoServiceList)

	arkInfoServiceMap := map[int64][]*assistantdesk.FieldMapTree{}
	for _, serviceInfo := range arkInfoServiceList {
		arkInfoServiceMap[serviceInfo.ServiceId] = serviceInfo.FieldMapTree
	}

	for _, serviceInfo := range serviceList {
		if serviceInfo.ServiceId <= 0 {
			continue
		}

		zlog.Infof(ctx, "serviceInfo:%+v", serviceInfo)

		filedMapTreeList := make([]*assistantdesk.FieldMapTree, 0)
		if _, ok := arkInfoServiceMap[serviceInfo.ServiceId]; ok {
			filedMapTreeList = arkInfoServiceMap[serviceInfo.ServiceId]
		}

		data.ServiceList = append(data.ServiceList, uiStru.GetCourseServiceList{
			ID:                serviceInfo.ID,
			ServiceName:       serviceInfo.ServiceName,
			TplId:             serviceInfo.TplId,
			ServiceId:         serviceInfo.ServiceId,
			ParentServiceId:   serviceInfo.ParentServiceId,
			ParentServiceName: serviceInfo.ParentServiceName,
			FiledMapTreeList:  filedMapTreeList,
		})
	}
	return
}

func (s arkService) JumpTaskList(ctx *gin.Context, param input.JumpTaskListParam) (data uiStru.ArkJumpTaskList, err error) {
	deskCli := assistantdesk.NewClient()
	data = uiStru.ArkJumpTaskList{}
	courseInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, int(param.CourseId), []string{}, []string{})
	if err != nil {
		return
	}

	periodMap, err := tower.NewClient().GetBatchExpireTimeByCourseList(ctx, []int64{param.CourseId})
	if err != nil {
		return
	}

	start := periodMap[param.CourseId].ExpireTimeStart
	end := periodMap[param.CourseId].ExpireTime
	timestamp := int(time.Now().Unix())

	serviceType := stru.SERVICE_TYPE_AFTER
	if timestamp < start {
		serviceType = stru.SERVICE_TYPE_BEFORE
	}
	if timestamp >= start && timestamp <= end {
		serviceType = stru.SERVICE_TYPE_IN
	}

	//通过课程id获取关联的资产uid
	courseBindTeacherList, apiErr := tower.NewClient().BatchCourseBindByCourseId(ctx, []int{courseInfo.CourseId})
	if apiErr != nil {
		return
	}
	courseBindTeacher, ok := courseBindTeacherList[courseInfo.CourseId]
	if !ok {
		return data, errors.New("未找到课程排班信息")
	}

	xuid := param.Xuid
	if param.Xuid == 0 { //没有传xuid从课程绑定的资产中随便选一个作为xuid
		for idx := range courseBindTeacher {
			if courseBindTeacher[idx].DeviceUID > 0 {
				xuid = courseBindTeacher[idx].DeviceUID
			}
		}
	}

	courseBindTeacherMap := map[int64]tower.CourseBindData{}
	for idx := range courseBindTeacher {
		courseBindTeacherMap[courseBindTeacher[idx].DeviceUID] = courseBindTeacher[idx]
	}

	//xuid(真人uid or 资产uid) 转成 真人uid
	personUid := xuid
	userInfo, apiErr := deskCli.GetUserInfo(ctx, xuid)
	if apiErr != nil {
		return
	}
	if userInfo.PersonUid > 0 {
		personUid = userInfo.PersonUid
	}

	assistantList, apiErr := deskCli.GetBindAssistantList(ctx, assistantdesk.GetBindAssistantListParams{PersonUid: personUid})
	if apiErr != nil {
		return
	}

	if len(assistantList) <= 0 {
		return data, errors.New("课程、xuid不匹配")
	}

	serviceList, apiErr := assistantdesk.NewClient().GetCourseServices(ctx, assistantdesk.GetCourseServicesParams{CourseID: param.CourseId})
	if apiErr != nil {
		return
	}
	for idx := range serviceList {
		data.ServiceId = serviceList[idx].ServiceId
		break
	}

	for idx := range assistantList {
		if _, ok = courseBindTeacherMap[assistantList[idx].AssistantUid]; ok {
			data.AssistantUid = assistantList[idx].AssistantUid
			break
		}
	}

	data.CourseId = int64(courseInfo.CourseId)
	data.Year = int64(courseInfo.Year)
	data.Xuid = personUid
	data.CourseServiceType = serviceType
	data.CourseServiceTypeName = stru.ServiceTypeMap[serviceType]
	return
}
