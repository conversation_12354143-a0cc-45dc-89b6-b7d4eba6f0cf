package desk

import (
	"fmt"
	"fwyytool/api/dal"
	"fwyytool/components"
	"fwyytool/controllers/http/desk/input"
	uiStru "fwyytool/uiStru"
	"github.com/gin-gonic/gin"
	"time"
)

var CourseService courseService

type courseService struct {
}

func (s courseService) Detail(ctx *gin.Context, param input.DetailParam) (data uiStru.CourseDetail, err error) {
	courseInfo, err := dal.GetCourseLessonInfoByCourseId(ctx, int(param.CourseID), []string{}, []string{})
	if err != nil {
		return
	}

	data.CourseID = int64(courseInfo.CourseId)
	data.CourseName = courseInfo.CourseName
	data.CourseTypeName = components.GetCourseTypeName(courseInfo.CourseType)
	data.SeasonName = components.GetLearnSeanFromat(courseInfo.Season)
	data.Year = int64(courseInfo.Year)
	data.Content = courseInfo.Content
	data.OnlineFormatTime = courseInfo.OnlineFormatTime
	data.CpuID = int64(courseInfo.CpuId)
	data.IsInner = "否 "
	if courseInfo.IsInner == 1 {
		data.IsInner = "是"
	}
	data.SubjectName = components.SubjectNameMap[courseInfo.MainSubjectId]
	data.GradeName = components.DepartNameMap[components.GradeDepartMap[courseInfo.MainGradeId]]

	for _, lessonInfo := range courseInfo.LessonList {
		startDate := time.Unix(int64(lessonInfo.StartTime), 0).Format("01月02日 15:04")
		stopDate := time.Unix(int64(lessonInfo.StopTime), 0).Format("15:04")
		lessonDate := fmt.Sprintf("%s-%s", startDate, stopDate)

		playTypeName := ""
		if _, ok := dal.PlayTypeMap[int64(lessonInfo.PlayType)]; ok {
			playTypeName = dal.PlayTypeMap[int64(lessonInfo.PlayType)]
		}

		lessonTypeName := ""
		if _, ok := dal.LessonTypeMap[int64(lessonInfo.LessonType)]; ok {
			lessonTypeName = dal.LessonTypeMap[int64(lessonInfo.LessonType)]
		}

		statusName := ""
		if _, ok := dal.LessonStatusMap[int64(lessonInfo.Status)]; ok {
			statusName = dal.LessonStatusMap[int64(lessonInfo.Status)]
		}

		data.LessonList = append(data.LessonList, uiStru.CourseLessonInfo{
			LessonID:       lessonInfo.LessonId,
			LessonName:     lessonInfo.LessonName,
			LessonDate:     lessonDate,
			PlayTypeName:   playTypeName,
			LessonTypeName: lessonTypeName,
			StatusName:     statusName,
			OutlineID:      lessonInfo.OutlineId,
		})
	}
	return
}
