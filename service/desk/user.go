package desk

import (
	"errors"
	"fwyytool/api/assistantdesk"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

var UserService userService

type userService struct {
}

// ConvertXuidToPersonUid 将xuid转换为真人uid
// 输入参数：xuid（可以是真人uid或资产uid）
// 输出结果：转换后的真人uid（personUid）
// 业务逻辑：
// 1. 接收xuid参数
// 2. 调用GetUserInfo获取用户信息
// 3. 如果userInfo.PersonUid > 0，则使用PersonUid作为真人uid
// 4. 否则使用原始xuid作为真人uid
// 5. 验证该真人uid是否有绑定的资产列表
// 6. 如果没有绑定资产，返回"未绑定资产"的错误
func (s userService) ConvertXuidToPersonUid(ctx *gin.Context, xuid int64) (personUid int64, err error) {
	if xuid <= 0 {
		return 0, errors.New("xuid参数无效")
	}

	deskCli := assistantdesk.NewClient()

	// xuid(真人uid or 资产uid) 转成 真人uid
	personUid = xuid
	userInfo, apiErr := deskCli.GetUserInfo(ctx, xuid)
	if apiErr != nil {
		zlog.Warnf(ctx, "获取用户信息失败, xuid:%d, err:%v", xuid, apiErr)
		return 0, apiErr
	}

	// 如果PersonUid > 0，则使用PersonUid作为真人uid
	if userInfo.PersonUid > 0 {
		personUid = userInfo.PersonUid
	}

	// 验证该真人uid是否有绑定的资产
	assistantList, apiErr := deskCli.GetBindAssistantList(ctx, assistantdesk.GetBindAssistantListParams{PersonUid: personUid})
	if apiErr != nil {
		zlog.Warnf(ctx, "获取绑定资产列表失败, personUid:%d, err:%v", personUid, apiErr)
		return 0, apiErr
	}

	// 如果没有绑定资产，返回错误
	if len(assistantList) <= 0 {
		zlog.Warnf(ctx, "未绑定资产, xuid:%d, personUid:%d", xuid, personUid)
		return 0, errors.New("资产")
	}

	zlog.Infof(ctx, "xuid转换成功, xuid:%d -> personUid:%d, assistantCount:%d", xuid, personUid, len(assistantList))
	return personUid, nil
}
