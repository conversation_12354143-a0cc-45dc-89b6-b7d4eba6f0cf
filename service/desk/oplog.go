package desk

import (
	"fwyytool/api/assistantdesk"
	deskinut "fwyytool/controllers/http/desk/input"
	"github.com/gin-gonic/gin"
	"time"
)

func ListOpLog() {

}

var OplogService opLogService

type opLogService struct {
}

func (s opLogService) ListOpLog(ctx *gin.Context, params *deskinut.OperateLogQueryReq) (data []assistantdesk.OperateLogQueryRsp, err error) {
	list, err := assistantdesk.NewClient().QueryOpLog(ctx, assistantdesk.OperateLogQueryReq{
		RelationId:   params.RelationId,
		RelationType: params.RelationType,
		PersonUid:    params.PersonUid,
		Page:         params.Page,
		PageSize:     params.PageSize,
	})

	for index := range list {
		if list[index].OperateTime > 0 {
			timeFormat := time.Unix(list[index].OperateTime, 0)
			list[index].OperateTimeFormat = timeFormat.Format("2006年01月02日 15:04:05")
		}
	}

	return list, err

}
