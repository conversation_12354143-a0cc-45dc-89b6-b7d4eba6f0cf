package desk

import (
	"fwyytool/api/arkgo"
	"fwyytool/api/assistantdesk"
	"fwyytool/uiStru"
	"github.com/gin-gonic/gin"
)

var ArkFieldDownGradeService arkFieldDownGradeService

type arkFieldDownGradeService struct {
}

func (s arkFieldDownGradeService) GetArkFieldDownGradeConfig(ctx *gin.Context) (data uiStru.ArkFieldDownGradeConfig, err error) {
	detail, err := assistantdesk.NewClient().GetArkFieldDownGradeConfig(ctx)
	if err != nil {
		return
	}
	data = uiStru.ArkFieldDownGradeConfig(detail)
	return
}

func (s arkFieldDownGradeService) GetNewArkFieldDownGradeConfig(ctx *gin.Context) (data uiStru.ArkFusingDetail, err error) {
	detail, err := arkgo.NewClient().GetArkFusing(ctx)
	if err != nil {
		return
	}
	data = uiStru.ArkFusingDetail(detail)
	return
}
