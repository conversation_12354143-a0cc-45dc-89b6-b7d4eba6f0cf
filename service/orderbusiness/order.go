package orderbusiness

import (
	"fwyytool/api/coursebase"
	"fwyytool/api/dal"
	"fwyytool/api/moat"
	"fwyytool/consts"
	"fwyytool/controllers/http/orderbusiness/dto"
	"fwyytool/service/common"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"strings"
	"time"
)

// getOrderBusinessStatusMap 懒得整接口了，先写死 https://ued.zuoyebang.cc/documents/docs/dds/orderBusinessStatus.html
func getOrderBusinessStatusMap() map[int64]string {
	return map[int64]string{
		0:  "待支付",
		1:  "已支付",
		2:  "履约中",
		3:  "待发货",
		4:  "待收货",
		5:  "已完成",
		6:  "已取消",
		7:  "暂停",
		8:  "部分支付",
		10: "已关单",
	}
}

func GetUserOrderList(ctx *gin.Context, req dto.GetUserOrderListReq) (respList []dto.GetUserOrderListResp, err error) {
	respList = make([]dto.GetUserOrderListResp, 0)
	if req.UserId == 0 {
		return
	}

	var shopIds []int64
	if len(strings.TrimSpace(req.ShopIds)) > 0 {
		shopIds, err = fwyyutils.ConvertArrayStringToArrayInt64(strings.Split(strings.TrimSpace(req.ShopIds), ","))
		if err != nil {
			return
		}
	}
	orderList, err := moat.OneOpenList(ctx, moat.OneOpenListReq{
		UserId:   req.UserId,
		ShopIds:  shopIds,
		Page:     req.Pn,
		PageSize: 20,
	})
	if err != nil || len(orderList) == 0 {
		return
	}

	// shop
	shopIdNameMap := common.GetKeyMap(ctx, coursebase.KeySource)

	// grade
	gradeIdNameMap := common.GetKeyMap(ctx, coursebase.KeyGrade)

	// subject
	subjectIdNameMap := common.GetKeyMap(ctx, coursebase.KeySubject)

	// courseInfo
	courseIdMap := make(map[int]struct{})
	for _, resp := range orderList {
		if len(resp.SkuRowList) == 0 {
			continue
		}
		for _, rowInfo := range resp.SkuRowList {
			if rowInfo.SkuServiceType == consts.SkuServiceTypeCourse && rowInfo.ProductId > 0 {
				courseIdMap[int(rowInfo.ProductId)] = struct{}{}
			}
		}
	}
	var courseIdInfoMap map[int]dal.CourseInfo
	if len(courseIdMap) > 0 {
		courseIds := make([]int, 0, len(courseIdMap))
		for courseId, _ := range courseIdMap {
			courseIds = append(courseIds, courseId)
		}
		courseIdInfoMap, _ = dal.GetCourseBaseByCourseIds(ctx, courseIds, []string{})
	}

	// format
	for _, resp := range orderList {
		var addressInfo moat.AddressInfo
		if resp.AddressInfo != nil {
			marshal, _ := jsoniter.Marshal(addressInfo)
			_ = jsoniter.Unmarshal(marshal, &addressInfo)
		}

		var logInfo moat.LogInfo
		if resp.LogInfo != nil {
			marshal, _ := jsoniter.Marshal(logInfo)
			_ = jsoniter.Unmarshal(marshal, &logInfo)
		}

		var payTimeDesc string
		if resp.PayTime > 0 {
			payTimeDesc = time.Unix(resp.PayTime, 0).Format("2006-01-02 15:04:05")
		}

		item := dto.GetUserOrderListResp{
			UserId:                  resp.UserId,
			OrderId:                 resp.OrderId,
			OriginalOrderId:         resp.OriginalOrderId,
			BusinessId:              resp.BusinessId,
			OrderFlags:              fwyyutils.JoinArrayInt64ToString(resp.OrderFlag, ","),
			OrderBusinessStatus:     resp.OrderBusinessStatus,
			OrderBusinessStatusName: getOrderBusinessStatusMap()[resp.OrderBusinessStatus],
			ShopId:                  resp.ShopId,
			ShopName:                shopIdNameMap[resp.ShopId],
			SaleChannel:             resp.SaleChannel,
			Quantity:                resp.Quantity,
			GoodsAmount:             resp.GoodsAmount,
			PaidAmount:              resp.PaidAmount,
			Address:                 addressInfo.Address,
			LastFrom:                logInfo.Lastfrom,
			OrderTimeDesc:           time.Unix(resp.OrderTime, 0).Format("2006-01-02 15:04:05"),
			PayTimeDesc:             payTimeDesc,
			SkuRowList:              make([]dto.GetUserOrderSkuRowInfo, 0, len(resp.SkuRowList)),
		}

		if len(resp.SkuRowList) == 0 {
			respList = append(respList, item)
			continue
		}

		for _, skuRowInfo := range resp.SkuRowList {
			subItem := dto.GetUserOrderSkuRowInfo{
				SkuId:          skuRowInfo.SkuId,
				SkuRowId:       skuRowInfo.SkuRowId,
				Quantity:       skuRowInfo.Quantity,
				SkuName:        skuRowInfo.SkuName,
				MainSkuId:      skuRowInfo.MainSkuId,
				ProductId:      skuRowInfo.ProductId,
				IsGift:         skuRowInfo.IsGift,
				ShopId:         skuRowInfo.ShopId,
				GoodsAmount:    skuRowInfo.GoodsAmount,
				PaidAmount:     skuRowInfo.PaidAmount,
				SkuServiceType: skuRowInfo.SkuServiceType,
			}
			if len(courseIdInfoMap) > 0 {
				if courseInfo, ok := courseIdInfoMap[int(skuRowInfo.ProductId)]; ok {
					subItem.CourseId = cast.ToString(skuRowInfo.ProductId)
					subItem.CourseName = courseInfo.CourseName
					subItem.CourseGradeId = cast.ToString(courseInfo.MainGradeId)
					subItem.CourseGradeName = gradeIdNameMap[int64(courseInfo.MainGradeId)]
					subItem.CourseSubjectId = cast.ToString(courseInfo.MainSubjectId)
					subItem.CourseSubjectName = subjectIdNameMap[int64(courseInfo.MainSubjectId)]
				}
			}
			item.SkuRowList = append(item.SkuRowList, subItem)
		}
		respList = append(respList, item)
	}
	return
}
