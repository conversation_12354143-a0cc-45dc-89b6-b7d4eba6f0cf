package newgoodsplatform

import (
	"fmt"
	"fwyytool/api/coursebase"
	"fwyytool/api/goodssearch"
	"fwyytool/api/newgoodsplatform"
	"fwyytool/consts"
	"fwyytool/controllers/http/newgoodsplatform/dto"
	"fwyytool/service/common"
	"github.com/gin-gonic/gin"
)

func GetSkuInfoByCourseId(ctx *gin.Context, req dto.GetSkuInfoByCourseIdReq) (resp dto.GetSkuInfoByCourseIdResp, err error) {
	courseId := req.CourseId

	// 查询商品信息
	skuListByConditionReq := goodssearch.NewGetSkuListByConditionReq().
		AddSourceFields([]string{"skuId", "status", "startTime", "stopTime", "isInner"}).
		AndConds("productList", fmt.Sprintf("%s_%d_%d", consts.ProductPre, consts.ProductCourse, courseId), consts.ExpsEq)
	skuListByCondition, err := goodssearch.GetSkuListByCondition(ctx, *skuListByConditionReq)
	if err != nil {
		return
	}
	if len(skuListByCondition.List) == 0 {
		return
	}
	skuStatusMap := make(map[int64]goodssearch.GetSkuListByConditionItem)
	skuIds := make([]int64, 0, len(skuListByCondition.List))
	for _, item := range skuListByCondition.List {
		skuIds = append(skuIds, item.SkuId)
		skuStatusMap[item.SkuId] = item
	}

	// 获取商品信息
	skuBaseInfoReq := newgoodsplatform.SkuBaseInfoReq{
		SkuIdList: skuIds,
	}
	var skuBaseInfo *newgoodsplatform.SkuBaseInfoResp
	skuBaseInfo, err = newgoodsplatform.SkuBaseInfo(ctx, skuBaseInfoReq)
	if err != nil {
		return
	}

	resp = formatGetSkuInfoByCourseIdResp(ctx, skuBaseInfo, skuStatusMap)
	return
}

func formatGetSkuInfoByCourseIdResp(ctx *gin.Context, skuBaseInfo *newgoodsplatform.SkuBaseInfoResp, skuStatusMap map[int64]goodssearch.GetSkuListByConditionItem) (resp dto.GetSkuInfoByCourseIdResp) {
	categoryIds := make([]int64, 0, len(skuBaseInfo.SkuInfoList))
	for _, item := range skuBaseInfo.SkuInfoList {
		categoryIds = append(categoryIds, item.Category)
	}

	// category options
	categoryIdNameMap := common.GetCategoryIdNameMap(ctx, categoryIds)

	// source options
	sourceIdNameMap := common.GetKeyMap(ctx, coursebase.KeySource)

	resp.List = make([]dto.GetSkuInfoByCourseIdItem, 0, len(skuBaseInfo.SkuInfoList))
	for _, item := range skuBaseInfo.SkuInfoList {
		// build attr
		attrTags, _ := structToString(ctx, item.AttributeTags)

		// build spec
		specTags, _ := structToString(ctx, item.SpecTags)

		// build label
		labelTags, _ := structToString(ctx, item.LabelTags)

		var onSaleStatus int64 = 0
		var onSaleStartTime int64 = 0
		var onSaleStopTime int64 = 0
		var isInner int64 = 0
		if skuStatus, ok := skuStatusMap[item.SkuId]; ok {
			onSaleStatus = skuStatus.Status
			onSaleStartTime = skuStatus.StartTime
			onSaleStopTime = skuStatus.StopTime
			isInner = skuStatus.IsInner
		}

		resp.List = append(resp.List, dto.GetSkuInfoByCourseIdItem{
			SkuId:           item.SkuId,
			SkuName:         item.SkuName,
			Source:          item.Source,
			SourceName:      sourceIdNameMap[item.Source],
			Category:        item.Category,
			CategoryName:    categoryIdNameMap[item.Category],
			Price:           item.Price,
			SkuOriginPrice:  item.SkuOriginPrice,
			CombinationType: item.CombinationType,
			SpuId:           item.SpuId,
			SkuMode:         item.SkuMode,
			ShopId:          item.ShopId,
			AttributeTags:   attrTags,
			SpecTags:        specTags,
			LabelTags:       labelTags,
			OnSaleStatus:    onSaleStatus,
			OnSaleStartTime: onSaleStartTime,
			OnSaleStopTime:  onSaleStopTime,
			IsInner:         isInner,
		})
	}
	return
}
