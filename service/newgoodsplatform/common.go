package newgoodsplatform

import (
	"fmt"
	"fwyytool/libs/json"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
)

func structToString(ctx *gin.Context, origin interface{}) (data string, err error) {
	data, err = json.MarshalToString(origin)
	if err != nil {
		zlog.Warnf(ctx, "structToMap Marshal failed, origin: %+v, err: %+v", origin, err)
		data = fmt.Sprintf("%+v", data)
		return
	}
	return
}

// getLearnSeasonName 学季+期次；例如31=秋1期
func getLearnSeasonName(learnSeason int64) string {
	strLS := strconv.Itoa(int(learnSeason))
	if len(strLS) == 0 {
		return ""
	}

	season := strLS[0:1]
	semester := strLS[1:]
	if len(semester) == 0 {
		semester = "0期"
	} else {
		semester += "期"
	}

	seasonNum, _ := strconv.Atoi(season)
	switch seasonNum {
	case 1:
		season = "春"
	case 2:
		season = "暑"
	case 3:
		season = "秋"
	case 4:
		season = "寒"
	}
	return season + semester
}
