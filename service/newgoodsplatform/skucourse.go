package newgoodsplatform

import (
	"fmt"
	"fwyytool/api/coursebase"
	courseSearch "fwyytool/api/coursesearch"
	"fwyytool/api/mesh"
	"fwyytool/api/tower"
	"fwyytool/controllers/http/newgoodsplatform/dto"
	"fwyytool/service/common"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strconv"
	"time"
)

func GetCourseInfoBySkuId(ctx *gin.Context, req dto.GetCourseInfoBySkuIdReq) (resp dto.GetCourseInfoBySkuIdResp, err error) {
	queryReq := courseSearch.NewQueryReq(courseSearch.DocTypeCourse, courseSearch.OpAnd).
		AddCond("skuId", req.SkuId, courseSearch.ExpEq).
		OrderBy("createTime", courseSearch.OrderDesc)
	queryResp, err := courseSearch.Query(ctx, *queryReq)
	if err != nil {
		zlog.Warnf(ctx, "GetCourseInfoBySkuId.Query failed, queryReq: %+v, err: %+v", queryReq, err)
		return
	}
	courseList := queryResp.GetCourseList()
	zlog.Infof(ctx, "GetCourseInfoBySkuId.Query req: %+v, resp: %+v", queryReq, queryResp)

	resp = formatGetCourseInfoBySkuIdResp(ctx, courseList)
	return
}

func formatGetCourseInfoBySkuIdResp(ctx *gin.Context, courseList []courseSearch.QueryCourse) (resp dto.GetCourseInfoBySkuIdResp) {
	if len(courseList) == 0 {
		return
	}
	courseIds := make([]int, 0, len(courseList))
	teacherUids := make([]int64, 0)
	for _, course := range courseList {
		courseIds = append(courseIds, int(course.CourseId))
		teacherUids = append(teacherUids, course.TeacherUid...)
	}

	// teacher
	teacherUidNameMap := common.GetPersonUidNameMap(ctx, teacherUids)

	// assistant
	courseAssistantListMap := getCourseAssistantIdNameMap(ctx, courseIds)

	// newCourseType
	newCourseTypeIdNameMap := common.GetKeyMap(ctx, coursebase.KeyNewCourseType)

	// grade
	gradeIdNameMap := common.GetKeyMap(ctx, coursebase.KeyGrade)

	// subject
	subjectIdNameMap := common.GetKeyMap(ctx, coursebase.KeySubject)

	resp.List = make([]dto.GetCourseInfoBySkuIdItem, 0, len(courseList))
	for _, courseInfo := range courseList {
		isOnSaleDesc := "未上架"
		if courseInfo.IsOnSale == 1 {
			isOnSaleDesc = "已上架"
		}
		isInnerDesc := "内部测试课"
		if courseInfo.IsInner == 0 {
			isInnerDesc = "正式课"
		}

		teacherDesc := make([]string, 0)
		if len(courseInfo.TeacherUid) > 0 {
			for _, tu := range courseInfo.TeacherUid {
				teacherDesc = append(teacherDesc, fmt.Sprintf("%s[%d]", teacherUidNameMap[tu], tu))
			}
		}

		resp.List = append(resp.List, dto.GetCourseInfoBySkuIdItem{
			CourseId:            courseInfo.CourseId,
			CourseName:          courseInfo.CourseName,
			NewCourseTypeDesc:   fmt.Sprintf("%s[%d]", newCourseTypeIdNameMap[courseInfo.NewCourseType], courseInfo.NewCourseType),
			MainGradeDesc:       fmt.Sprintf("%s[%d]", gradeIdNameMap[courseInfo.GradeId], courseInfo.GradeId),
			MainSubjectDesc:     fmt.Sprintf("%s[%d]", subjectIdNameMap[courseInfo.SubjectId], courseInfo.SubjectId),
			YearDesc:            strconv.Itoa(int(courseInfo.Year)),
			LearnSeasonDesc:     getLearnSeasonName(courseInfo.LearnSeason),
			StartTimeDesc:       time.Unix(courseInfo.StartTime, 0).Format("2006-01-02 15:04:05"),
			StopTimeDesc:        time.Unix(courseInfo.StopTime, 0).Format("2006-01-02 15:04:05"),
			SkuId:               courseInfo.SkuId,
			IsOnSaleDesc:        isOnSaleDesc,
			IsInnerDesc:         isInnerDesc,
			TeachersDesc:        teacherDesc,
			AssistantPersonDesc: courseAssistantListMap[courseInfo.CourseId],
		})
	}
	return
}

func getCourseAssistantIdNameMap(ctx *gin.Context, courseIds []int) (courseIdAssistantListMap map[int64][]string) {
	if len(courseIds) == 0 {
		return
	}
	courseIdAssistantListMap = make(map[int64][]string)
	bindCourseList, err := tower.NewClient().BatchCourseBindByCourseId(ctx, courseIds)
	if err != nil {
		zlog.Warnf(ctx, "GetCourseInfoBySkuId.BatchCourseBindByCourseId failed, courseIds: %+v, err: %+v", courseIds, err)
		return
	}

	assistantUidMap := make(map[int64]struct{})
	for _, bindData := range bindCourseList {
		for _, bind := range bindData {
			assistantUidMap[bind.DeviceUID] = struct{}{}
		}
	}
	assistantUids := make([]int64, 0, len(assistantUidMap))
	for k, _ := range assistantUidMap {
		assistantUids = append(assistantUids, k)
	}
	req := mesh.GetDeviceInfoListReq{
		DeviceUids: assistantUids,
	}
	resp, err := mesh.GetDeviceInfoList(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetCourseInfoBySkuId.GetDeviceInfoList failed, req: %+v, err: %+v", req, err)
		return
	}

	for _, bindData := range bindCourseList {
		for _, bind := range bindData {
			formattedList, ok := courseIdAssistantListMap[int64(bind.CourseID)]
			if !ok {
				formattedList = make([]string, 0, len(bindData))
			}
			item, _ := resp.GetDeviceInfo(bind.DeviceUID)
			formattedList = append(formattedList, fmt.Sprintf("资产：%s[%d] 真人：%s[%d]", item.NickName, bind.DeviceUID, item.StaffName, item.StaffUid))
			courseIdAssistantListMap[int64(bind.CourseID)] = formattedList
		}
	}
	return
}
