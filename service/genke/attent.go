package genke

import (
	"fwyytool/api/dal"
	apiGenke "fwyytool/api/genke"
	"fwyytool/uiStru"
	"github.com/gin-gonic/gin"
)

var Attend attend

type attend struct {
}

func (s attend) Detail(ctx *gin.Context, lessonID, studentUid int64) (data uiStru.AttendDetail, err error) {
	attendDetail, err := apiGenke.NewClient().GetTimelineByLessonStu(ctx, apiGenke.GetTimelineByLessonStuParams{LessonId: lessonID, StudentUid: studentUid})
	if err != nil {
		return
	}

	lessonInfo, err := dal.GetLessonBaseByLessonId(ctx, int(lessonID), []string{})
	if err != nil {
		return
	}

	courseID := int64(0)
	if len(lessonInfo.CourseId) > 0 {
		courseID = int64(lessonInfo.CourseId[0])
	}

	attentStatusString := "未知"
	for _, stuid := range attendDetail.Stats.AttendStudentUids {
		if int64(stuid) == studentUid {
			attentStatusString = "到课"
		}
	}
	for _, stuid := range attendDetail.Stats.NoAttendStudentUids {
		if int64(stuid) == studentUid {
			attentStatusString = "未到课"
		}
	}

	data.LessonName = lessonInfo.LessonName
	data.CourseID = courseID
	data.AttentStatusString = attentStatusString
	data.LiveRoomId = attendDetail.LiveRoomId
	data.TeacherName = attendDetail.Teacher.TeacherName
	data.TeacherUid = attendDetail.Teacher.TeacherUid
	data.AttendTimeData = attendDetail.AttendTimeData[studentUid]
	data.AttendOutInTimeData = attendDetail.TeacherAttendTimeData[studentUid]
	return
}
