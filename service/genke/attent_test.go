package genke

import (
	"fmt"
	"fwyytool/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"
)

var initialized bool

func init() {
	if initialized {
		return
	}
	initialized = true
	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()

	//validator
	helpers.InitValidator()
	//esclient
	helpers.InitCos()
	helpers.InitMysql()
	helpers.InitRedis()
}

// getSourcePath returns the directory containing the source code that is calling this function.
func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func TestDetail(t *testing.T) {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	info, _ := Attend.Detail(ctx, 486245, 2135252654)
	fmt.Println(info)
}
