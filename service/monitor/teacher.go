package monitor

import (
	"fmt"
	courseSearch "fwyytool/api/coursesearch"
	"fwyytool/api/tower"
	"fwyytool/components"
	"fwyytool/conf"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils/dingTalk"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
	"time"
)

var TeacherService teacherService

type teacherService struct {
}

type TimetableData struct {
	LessonCnt  int
	TeacherCnt int
}

func (s teacherService) Today(ctx *gin.Context) (err error) {
	lessonList, err := courseSearch.GetTodayAllLessonList(ctx, []string{"lessonCourseId", "courseName", "lessonId", "startTime", "stopTime", "teacherUid"})
	if err != nil {
		return
	}

	courseIds := make([]int, 0)
	for _, lessonInfo := range lessonList {
		courseIds = append(courseIds, lessonInfo.LessonCourseId)
	}
	//通过课程id获取关联的资产uid
	courseBindTeacherList, apiErr := tower.NewClient().BatchCourseBindByCourseId(ctx, courseIds)
	if apiErr != nil {
		return
	}

	timetableData := [24]TimetableData{}

	todayTime := components.Util.GetTodayTimestamp()
	for _, lessonInfo := range lessonList {
		courseTeacher := courseBindTeacherList[lessonInfo.LessonCourseId]
		//fmt.Println(lessonInfo.LessonCourseId, courseTeacher)
		startHour := (int64(lessonInfo.StartTime) - todayTime) / 3600
		stopHour := (int64(lessonInfo.StopTime) - todayTime) / 3600

		timetableData[startHour].TeacherCnt += len(courseTeacher)
		timetableData[startHour].LessonCnt++

		if startHour != stopHour {
			timetableData[stopHour].TeacherCnt += len(courseTeacher)
			timetableData[stopHour].LessonCnt++
		}
	}

	var dingNotice strings.Builder

	dingNotice.WriteString("当日排班老师:\n\n")
	todayDate := time.Now().Format("2006-01-02")
	for i := 9; i < 22; i++ {
		curr, data := i, timetableData[i]
		dingNotice.WriteString(fmt.Sprintf("%s %d-%d: 章节数：%d，老师人数：%d \n\n", todayDate, curr, curr+1, data.LessonCnt, data.TeacherCnt))
	}

	//统计结果,发送dingding通知
	msg := dingTalk.DingMsg{
		MsgType:   "markdown",
		Token:     conf.Custom.DingToken["fwyywh"],
		Title:     "通知 \n\n",
		Text:      dingNotice.String(),
		IsAtAll:   false,
		AtMobiles: []string{},
	}

	_ = msg.Notice(ctx)
	zlog.Infof(ctx, "msg: %+v", msg)
	//fmt.Println(msg)
	//fmt.Println(timetableData)
	return
}
