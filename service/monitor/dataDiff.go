package monitor

import (
	"fmt"
	"fwyytool/api/arkgo"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils/dingTalk"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"sort"
	"strings"
)

var DataDiffService dataDiffService

type dataDiffService struct {
}

func (s dataDiffService) YesterdayReport(ctx *gin.Context) (err error) {
	res, err := arkgo.NewClient().GetDiffReportRes(ctx)
	if err != nil {
		return
	}

	zlog.Infof(ctx, "YesterdayReport res: %+v", res)

	allData := make([]arkgo.GetDataDiffReportOutPut, 0)

	for _, val := range res {
		allData = append(allData, val)
	}

	sort.Slice(allData, func(i, j int) bool {
		return allData[i].HasDiffNum > allData[j].HasDiffNum
	})

	var dingNotice strings.Builder

	dingNotice.WriteString("接口:\n\n")
	for _, item := range allData {
		dingNotice.WriteString(fmt.Sprintf("接口：%s\n", item.Handler))
		dingNotice.WriteString(fmt.Sprintf("有 diff 任务数:%d，无 diff 任务数:%d，未处理任务数：%d\n", item.HasDiffNum, item.NoDiffNum, item.UnFinishedTask))
		dingNotice.WriteString("----------------------------------\n\n")
	}

	//统计结果,发送dingding通知
	msg := dingTalk.DingMsg{
		MsgType:   "markdown",
		Token:     "SEC4be253dab02d8372380a2c7a5b1692fb85901c2d1b86e6acbec40fb6212ff52b",
		Title:     "昨日 diff 数据 \n\n",
		Text:      dingNotice.String(),
		IsAtAll:   false,
		AtMobiles: []string{},
	}

	err = msg.Notice(ctx)
	if err != nil {
		zlog.Warnf(ctx, "YesterdayReport err: %+v", err)
		return
	}
	zlog.Infof(ctx, "YesterdayReport msg: %+v", msg)
	//fmt.Println(msg)
	//fmt.Println(timetableData)
	return
}
