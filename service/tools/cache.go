package tools

import (
	"fmt"
	"fwyytool/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func ClearQWCache(ctx *gin.Context, minId, maxId int64) error {
	go func() {
		dataCtx := gin.CreateNewContext(gin.New())
		ClearQWUrlCache(dataCtx, minId, maxId)
	}()
	return nil
}

func ClearQWUrlCache(ctx *gin.Context, minId, maxId int64) error {
	zlog.Infof(ctx, "[ClearQWUrlCache] start to clear cache, minId: %d, maxId: %d", minId, maxId)

	keyPrefix := "qiwei-course:short-url-%d"
	for i := minId; i <= maxId; i++ {
		key := fmt.Sprintf(keyPrefix, i)
		exists, err := helpers.RedisClient.Exists(ctx, key)
		if err != nil {
			zlog.Warnf(ctx, "[ClearQWUrlCache] check key exists failed, key: %s, err: %v", key, err)
			continue
		}

		if !exists {
			continue
		}

		_, err = helpers.RedisClient.Del(ctx, key)
		if err != nil {
			zlog.Warnf(ctx, "[ClearQWUrlCache] del key failed, key: %s, err: %v", key, err)
		}
	}
	zlog.Infof(ctx, "[ClearQWUrlCache] clear cache success, minId: %d, maxId: %d", minId, maxId)
	return nil
}
