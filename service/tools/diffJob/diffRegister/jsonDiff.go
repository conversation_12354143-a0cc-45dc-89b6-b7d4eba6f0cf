package diffRegister

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"reflect"
	"strings"
)

// DiffType 差异类型
type DiffType string

const (
	Create DiffType = "create"
	Update DiffType = "update"
	Delete DiffType = "delete"
)

// Diff 单条差异
type Diff struct {
	Path string   `json:"path"` // RFC 6901 JSON Pointer
	Type DiffType `json:"type"`
	Old  any      `json:"old,omitempty"`
	New  any      `json:"new,omitempty"`
}

// DiffConfig diff 行为配置
type DiffConfig struct {
	IgnorePaths []string                    // 忽略的 JSONPath，例如 "/users/*/id"
	KeyMappers  map[string]func(any) string // JSONPath -> 生成 map key 的函数
	StringKeys  map[string]bool             // 新增：按字段名走字符串比较
}

// Compare 对外入口
func Compare(a, b []byte, cfg DiffConfig) ([]string, error) {
	var oldV, newV any
	if err := json.Unmarshal(a, &oldV); err != nil {
		return nil, err
	}
	if err := json.Unmarshal(b, &newV); err != nil {
		return nil, err
	}
	var diffs []Diff
	compareAny("", oldV, newV, cfg, &diffs)
	return diffsToStrings(diffs), nil
}

func isScalar(v any) bool {
	switch v.(type) {
	case map[string]any, []any:
		return false
	}
	return true
}

func lastKey(p string) string {
	i := strings.LastIndex(p, "/")
	if i < 0 {
		return p
	}
	return p[i+1:]
}

func compareAny(path string, old, new any, cfg DiffConfig, diffs *[]Diff) {
	if shouldIgnore(path, cfg) {
		return
	}

	// ---------- 叶子标量分支 ----------
	if isScalar(old) && isScalar(new) {
		key := lastKey(path)     // 取出字段名
		if cfg.StringKeys[key] { // 仅在叶子且 key 命中
			if cast.ToString(old) != cast.ToString(new) {
				*diffs = append(*diffs, Diff{Path: path, Type: Update, Old: old, New: new})
			}
			return
		}
	}

	// nil 与 nil 相等
	if reflect.DeepEqual(old, new) {
		return
	}

	switch ov := old.(type) {
	case map[string]any:
		nv, ok := new.(map[string]any)
		if !ok {
			*diffs = append(*diffs, Diff{Path: path, Type: Update, Old: old, New: new})
			return
		}
		compareObject(path, ov, nv, cfg, diffs)

	case []any:
		nv, ok := new.([]any)
		if !ok {
			*diffs = append(*diffs, Diff{Path: path, Type: Update, Old: old, New: new})
			return
		}
		compareArray(path, ov, nv, cfg, diffs)

	default:
		// 标量
		*diffs = append(*diffs, Diff{Path: path, Type: Update, Old: old, New: new})
	}
}

func compareObject(path string, old, new map[string]any, cfg DiffConfig, diffs *[]Diff) {
	// 先处理删除 / 更新
	for k, v1 := range old {
		p := joinPath(path, k)
		if shouldIgnore(p, cfg) {
			continue
		}

		if v2, ok := new[k]; ok {
			compareAny(p, v1, v2, cfg, diffs)
		} else {
			*diffs = append(*diffs, Diff{Path: p, Type: Delete, Old: v1})
		}
	}
	// 处理新增
	for k, v2 := range new {
		p := joinPath(path, k)
		if shouldIgnore(p, cfg) {
			continue
		}
		if _, ok := old[k]; !ok {
			*diffs = append(*diffs, Diff{Path: p, Type: Create, New: v2})
		}
	}
}

func compareArray(path string, old, new []any, cfg DiffConfig, diffs *[]Diff) {
	if km, ok := cfg.KeyMappers[path]; ok || shouldUnordered(path, cfg) {
		// 转成 map 再比较
		oldMap := arrayToMap(old, km)
		newMap := arrayToMap(new, km)

		compareObject(path, oldMap, newMap, cfg, diffs)
		return
	}

	// 有序数组，按索引比较
	maxNum := len(old)
	if len(new) > maxNum {
		maxNum = len(new)
	}
	for i := 0; i < maxNum; i++ {
		p := fmt.Sprintf("%s/%d", path, i)
		if i >= len(old) {
			*diffs = append(*diffs, Diff{Path: p, Type: Create, New: new[i]})
		} else if i >= len(new) {
			*diffs = append(*diffs, Diff{Path: p, Type: Delete, Old: old[i]})
		} else {
			compareAny(p, old[i], new[i], cfg, diffs)
		}
	}
}

// 将数组转为 map，key 由用户提供或默认使用 json.Marshal 字符串
func arrayToMap(arr []any, keyFn func(any) string) map[string]any {
	m := make(map[string]any)
	for _, v := range arr {
		key := keyFn(v)
		m[key] = v
	}
	return m
}

func shouldIgnore(path string, cfg DiffConfig) bool {
	for _, p := range cfg.IgnorePaths {
		if match(path, p) {
			return true
		}
	}
	return false
}

// 只要某路径配置了 KeyMapper 就认为需要无序比较
func shouldUnordered(path string, cfg DiffConfig) bool {
	_, ok := cfg.KeyMappers[path]
	return ok
}

// JSON Pointer 拼接
func joinPath(base, key string) string {
	esc := strings.ReplaceAll(key, "~", "~0")
	esc = strings.ReplaceAll(esc, "/", "~1")
	if base == "" {
		return "/" + esc
	}
	return base + "/" + esc
}

// 简易通配符匹配：仅支持 * 且为单层
func match(path, pattern string) bool {
	parts := strings.Split(pattern, "/")
	pPath := strings.Split(path, "/")
	if len(parts) != len(pPath) {
		return false
	}
	for i := range parts {
		if parts[i] == "*" {
			continue
		}
		if parts[i] != pPath[i] {
			return false
		}
	}
	return true
}

func diffsToStrings(ds []Diff) []string {
	out := make([]string, 0)
	for _, d := range ds {
		oldStr := sprint(d.Old)
		newStr := sprint(d.New)
		out = append(out, fmt.Sprintf("%s, %s, %s --> %s",
			d.Path, d.Type, oldStr, newStr))
	}
	return out
}

// sprint 把 nil 显示成 <nil>，其余直接 %v
func sprint(v any) string {
	if v == nil {
		return "<nil>"
	}
	return fmt.Sprintf("%v", v)
}
