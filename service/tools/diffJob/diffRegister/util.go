package diffRegister

import (
	"bytes"
	"fmt"
	"fwyytool/components"
	"fwyytool/helpers"
	"fwyytool/stru"
	"html"
	"html/template"
	"io"
	"os"
	"strconv"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

type ChangeLog struct {
	Type string      `json:"type"`
	Path []string    `json:"path"`
	From interface{} `json:"from"`
	To   interface{} `json:"to"`
}

// DiffItem 差异项结构
type DiffItem struct {
	Path string      `json:"path"` // JSON路径
	Type string      `json:"type"` // 差异类型：create/update/delete
	From interface{} `json:"from"` // 原值
	To   interface{} `json:"to"`   // 新值
	Raw  string      `json:"raw"`  // 原始字符串
}

// DiffStats 差异统计
type DiffStats struct {
	Total  int `json:"total"`  // 总差异数量
	Create int `json:"create"` // 新增数量
	Update int `json:"update"` // 修改数量
	Delete int `json:"delete"` // 删除数量
}

// JSONDiffData jsondiffpatch 模板数据
type JSONDiffData struct {
	Stats   DiffStats `json:"stats"`
	HasDiff bool      `json:"hasDiff"`
	OldData string    `json:"oldData"`
	NewData string    `json:"newData"`
}

var ColourMap = map[string]string{
	"create": "#90EE90",
	"update": "#FED8B1",
	"delete": "#FFB6C1",
}

// HighlightRule 定义高亮规则（新增Description字段）
type HighlightRule struct {
	Path        []string // JSON路径
	Color       string   // 高亮颜色
	Description string   // 新增：规则描述
}

// GenerateJSONHTMLFromString 生成带描述的HTML
func GenerateJSONHTMLFromString(ctx *gin.Context, jsonStr string, rules []HighlightRule, id int64) (path string, err error) {
	var data interface{}
	if err = json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return
	}

	currentTime := time.Now().Format("2006-01-02_15:04:05")
	fileName := fmt.Sprintf("diff_%d_%s", id, currentTime)
	filePath := "./" + fileName + ".html"
	file, err := os.Create(filePath)
	if err != nil {
		fmt.Println("Error creating file:", err)
		return
	}
	defer func() {
		file.Close()
		os.Remove(filePath)
	}()

	// 写入HTML模板（新增描述展示区）
	fmt.Fprint(file, `<!DOCTYPE html>
<html>
<head>
  <title>JSON Highlight with Descriptions</title>
        <meta charset="UTF-8">
  <style>
    pre { 
      font-family: Menlo, Consolas, monospace;
      background: #f8f8f8;
      padding: 20px;
      border-radius: 5px;
    }
    .legend {
      margin-bottom: 20px;
      padding: 15px;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .legend-item {
      display: flex;
      align-items: center;
      margin: 5px 0;
    }
    .color-box {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      border: 1px solid #ccc;
    }
  </style>
</head>
<body>
`)

	// 生成说明图例
	generateLegend(file, rules)

	// 生成JSON内容
	fmt.Fprint(file, "<pre>\n")
	var buf bytes.Buffer
	generateJSON(data, []string{}, rules, &buf, 0)
	file.Write(buf.Bytes())
	fmt.Fprint(file, "</pre>\n</body>\n</html>")

	// upload cos
	path, _, err = helpers.UploadFile2Cos(ctx, filePath, fileName, "html", "fwyyDiffRes")
	if err != nil {
		return
	}

	return
}

// 生成说明图例
func generateLegend(w io.Writer, rules []HighlightRule) {
	fmt.Fprint(w, `<div class="legend"><h3>Diff 结果说明</h3>`)
	for _, rule := range rules {
		fmt.Fprintf(w, `
    <div class="legend-item">
      <div class="color-box" style="background-color: %s"></div>
      <div>%s</div>
    </div>
`, rule.Color, rule.Description)
	}
	fmt.Fprint(w, "</div>")
}

// 新的generateJSON支持颜色绑定
func generateJSON(data interface{}, currentPath []string, rules []HighlightRule, w io.Writer, indent int) {
	// 查找匹配的规则
	matchedRule := findMatchedRule(currentPath, rules)
	indentStr := strings.Repeat("  ", indent)

	// 添加高亮样式
	if matchedRule != nil {
		fmt.Fprintf(w, `<span style="background-color: %s;">`, matchedRule.Color)
	}

	switch v := data.(type) {
	case map[string]interface{}:
		handleObject(v, currentPath, rules, w, indentStr, indent)
	case []interface{}:
		handleArray(v, currentPath, rules, w, indentStr, indent)
	default:
		jsonBytes, _ := json.Marshal(v)
		fmt.Fprint(w, string(jsonBytes))
	}

	if matchedRule != nil {
		fmt.Fprint(w, `</span>`)
	}
}

// 查找第一个匹配的规则
func findMatchedRule(current []string, rules []HighlightRule) *HighlightRule {
	for _, rule := range rules {
		if comparePaths(current, rule.Path) {
			return &rule
		}
	}
	return nil
}

// 路径比较逻辑（支持通配符）
func comparePaths(current, target []string) bool {
	if len(target) == 0 {
		return false
	}

	// 全通配符匹配
	if len(target) == 1 && target[0] == "*" {
		return true
	}

	if len(current) != len(target) {
		return false
	}

	for i := range current {
		if target[i] != "*" && current[i] != target[i] {
			return false
		}
	}
	return true
}

// 处理JSON对象
func handleObject(obj map[string]interface{}, currentPath []string, rules []HighlightRule, w io.Writer, indentStr string, indent int) {
	fmt.Fprint(w, "{\n")
	i := 0
	for key, val := range obj {
		newPath := append(currentPath, key)
		fmt.Fprintf(w, "%s  %q: ", indentStr, key)
		generateJSON(val, newPath, rules, w, indent+1)
		if i < len(obj)-1 {
			fmt.Fprint(w, ",")
		}
		fmt.Fprint(w, "\n")
		i++
	}
	fmt.Fprintf(w, "%s}", indentStr)
}

// 处理JSON数组
func handleArray(arr []interface{}, currentPath []string, rules []HighlightRule, w io.Writer, indentStr string, indent int) {
	fmt.Fprint(w, "[\n")
	for i, elem := range arr {
		newPath := append(currentPath, fmt.Sprintf("[%d]", i))
		fmt.Fprintf(w, "%s  ", indentStr)
		generateJSON(elem, newPath, rules, w, indent+1)
		if i < len(arr)-1 {
			fmt.Fprint(w, ",")
		}
		fmt.Fprint(w, "\n")
	}
	fmt.Fprintf(w, "%s]", indentStr)
}

func GenerateArrayHTMLFromString(ctx *gin.Context, arr []string, id int64) (path string, err error) {
	currentTime := time.Now().Format("2006-01-02_15:04:05")
	fileName := fmt.Sprintf("diff_%d_%s", id, currentTime)
	filePath := "./" + fileName + ".html"
	file, err := os.Create(filePath)
	if err != nil {
		fmt.Println("Error creating file:", err)
		return
	}
	defer func() {
		os.Remove(filePath) // 确保最后删除临时文件
		file.Close()
	}()

	// 解析差异项
	diffItems := ParseDiffItems(arr)
	stats := CalculateStats(diffItems)

	// 准备模板数据
	templateData := struct {
		Stats     DiffStats
		DiffItems []DiffItem
	}{
		Stats:     stats,
		DiffItems: diffItems,
	}

	// 创建模板并注册函数
	tmpl := template.New("diffresult.html").Funcs(template.FuncMap{
		"getTypeLabel": getTypeLabel,
		"formatValue":  formatValueHtmlSafe,
		"getValueType": getValueType,
	})

	// 解析模板文件
	tmpl, err = tmpl.ParseFiles("templates/desk/datadiff/diffresult.html")
	if err != nil {
		zlog.Errorf(ctx, "Parse template failed: %v", err)
		return "", err
	}

	// 执行模板 - 需要指定define的模板名称
	if err := tmpl.ExecuteTemplate(file, "desk/datadiff/diffresult.html", templateData); err != nil {
		zlog.Errorf(ctx, "Execute template failed: %v", err)
		return "", err
	}

	// 上传到COS
	path, _, err = helpers.UploadFile2Cos(ctx, filePath, fileName, "html", "fwyyDiffRes")
	if err != nil {
		return
	}

	return
}

// GenerateJSONDiffHTML 使用 jsondiffpatch 生成差异对比 HTML
func GenerateJSONDiffHTML(ctx *gin.Context, oldData, newData string, id int64) (path string, err error) {
	currentTime := time.Now().Format("2006-01-02_15:04:05")
	fileName := fmt.Sprintf("diff_%d_%s", id, currentTime)
	filePath := "./" + fileName + ".html"
	file, err := os.Create(filePath)
	if err != nil {
		zlog.Errorf(ctx, "Create file failed: %v", err)
		return "", err
	}
	defer func() {
		os.Remove(filePath) // 确保最后删除临时文件
		file.Close()
	}()

	// 计算简单的差异统计
	stats := calculateJSONDiffStats(oldData, newData)
	hasDiff := stats.Total > 0

	templateData := struct {
		JSONDiffData
	}{
		JSONDiffData: JSONDiffData{
			Stats:   stats,
			HasDiff: hasDiff,
			OldData: oldData,
			NewData: newData,
		},
	}

	// 创建模板
	tmpl := template.New("diffresult.html")

	// 解析模板文件
	tmpl, err = tmpl.ParseFiles("templates/desk/datadiff/diffresult.html")
	if err != nil {
		zlog.Errorf(ctx, "Parse template failed: %v", err)
		return "", err
	}

	// 执行模板
	if err := tmpl.ExecuteTemplate(file, "desk/datadiff/diffresult.html", templateData); err != nil {
		zlog.Errorf(ctx, "Execute template failed: %v", err)
		return "", err
	}

	// 上传到COS
	path, _, err = helpers.UploadFile2Cos(ctx, filePath, fileName, "html", "fwyyDiffRes")
	if err != nil {
		return
	}

	return
}

// calculateJSONDiffStats 计算JSON差异的简单统计
func calculateJSONDiffStats(oldData, newData string) DiffStats {
	stats := DiffStats{}

	// 简单的统计逻辑：比较字符串长度和内容
	if oldData != newData {
		stats.Total = 1

		// 简单判断差异类型
		if oldData == "" || oldData == "{}" || oldData == "[]" {
			stats.Create = 1
		} else if newData == "" || newData == "{}" || newData == "[]" {
			stats.Delete = 1
		} else {
			stats.Update = 1
		}
	}

	return stats
}

// ParseDiffItems 解析差异字符串数组
func ParseDiffItems(arr []string) []DiffItem {
	var items []DiffItem

	for _, str := range arr {
		item := parseSingleDiff(str)
		items = append(items, item)
	}

	return items
}

// parseSingleDiff 解析单个差异字符串
func parseSingleDiff(str string) DiffItem {
	// 格式: "路径, 类型, 旧值 --> 新值"
	parts := strings.Split(str, ", ")
	if len(parts) < 3 {
		return DiffItem{
			Type: "unknown",
			Raw:  str,
		}
	}

	path := parts[0]
	diffType := parts[1]

	// 分离旧值和新值
	valueParts := strings.Split(strings.Join(parts[2:], ", "), " --> ")
	var oldValue, newValue interface{}

	if len(valueParts) >= 1 {
		oldValue = parseValue(valueParts[0])
	}
	if len(valueParts) >= 2 {
		newValue = parseValue(valueParts[1])
	}

	return DiffItem{
		Path: path,
		Type: diffType,
		From: oldValue,
		To:   newValue,
		Raw:  str,
	}
}

// parseValue 解析值字符串
func parseValue(s string) interface{} {
	if s == "<nil>" {
		return nil
	}

	// 尝试解析为JSON
	var jsonValue interface{}
	if err := json.Unmarshal([]byte(s), &jsonValue); err == nil {
		return jsonValue
	}

	return s
}

// CalculateStats 计算差异统计
func CalculateStats(items []DiffItem) DiffStats {
	stats := DiffStats{}

	for _, item := range items {
		stats.Total++

		switch item.Type {
		case "create":
			stats.Create++
		case "update":
			stats.Update++
		case "delete":
			stats.Delete++
		}
	}

	return stats
}

// formatValue 格式化值显示
func formatValue(v interface{}) string {
	if v == nil {
		return "<nil>"
	}

	switch val := v.(type) {
	case string:
		return val
	default:
		if jsonData, err := json.MarshalIndent(val, "", "  "); err == nil {
			return string(jsonData)
		}
		return fmt.Sprintf("%v", v)
	}
}

// FormatValueHtmlSafe 格式化值显示并确保HTML安全（导出版本）
func FormatValueHtmlSafe(v interface{}) template.HTML {
	return formatValueHtmlSafe(v)
}

// formatValueHtmlSafe 格式化值显示并确保HTML安全
func formatValueHtmlSafe(v interface{}) template.HTML {
	if v == nil {
		return template.HTML("&lt;nil&gt;")
	}

	switch val := v.(type) {
	case string:
		if val == "" {
			return template.HTML("") // 空字符串，由CSS处理显示
		}
		return template.HTML(html.EscapeString(val))
	default:
		if jsonData, err := json.MarshalIndent(val, "", "  "); err == nil {
			return template.HTML(html.EscapeString(string(jsonData)))
		}
		return template.HTML(html.EscapeString(fmt.Sprintf("%v", v)))
	}
}

// getValueType 获取值的类型，用于模板中区分不同类型
func getValueType(v interface{}) string {
	if v == nil {
		return "nil"
	}

	switch val := v.(type) {
	case string:
		if val == "" {
			return "empty-string"
		}
		return "string"
	default:
		return "other"
	}
}

// GetValueType 获取值类型（导出版本）
func GetValueType(v interface{}) string {
	return getValueType(v)
}

// GetTypeLabel 获取类型标签（导出版本）
func GetTypeLabel(diffType string) string {
	return getTypeLabel(diffType)
}

// getTypeLabel 获取类型标签
func getTypeLabel(diffType string) string {
	switch diffType {
	case "create":
		return "新增"
	case "update":
		return "修改"
	case "delete":
		return "删除"
	default:
		return "未知"
	}
}

func addDiffTag(ctx *gin.Context, param string) string {
	var p map[string]interface{}
	err := json.Unmarshal([]byte(param), &p)
	if err != nil {
		return param
	}
	p["forDiff"] = 1
	newParam, err := json.MarshalToString(p)
	if err != nil {
		return param
	}
	return newParam
}

func addXUIDCookie(ctx *gin.Context, cookie string, xuid int64) string {
	cookie = cookie + ";isDebug=1"
	if xuid == 0 {
		return cookie
	}

	cookie = strings.TrimSpace(cookie)
	if cookie == "" {
		return "XUID=" + strconv.FormatInt(xuid, 10)
	}

	var (
		target = "XUID="
		newVal = target + strconv.FormatInt(xuid, 10)
		sb     strings.Builder
	)

	parts := strings.Split(cookie, ";")
	updated := false

	for i, p := range parts {
		kv := strings.SplitN(strings.TrimSpace(p), "=", 2)
		if len(kv) == 0 {
			continue
		}
		key := strings.TrimSpace(kv[0])
		if strings.EqualFold(key, "XUID") {
			// 替换
			sb.WriteString(newVal)
			updated = true
		} else {
			// 原样写回
			sb.WriteString(strings.TrimSpace(p))
		}

		// 加分号（最后一段除外）
		if i < len(parts)-1 {
			sb.WriteString("; ")
		}
	}

	// 如果原先没有 XUID，则追加
	if !updated {
		if sb.Len() > 0 {
			sb.WriteString("; ")
		}
		sb.WriteString(newVal)
	}

	return sb.String()
}

type PersonUidStruct struct {
	PersonUid int64 `json:"personUid"`
}

func getPersonUid(param string) int64 {
	p := PersonUidStruct{}
	err := json.Unmarshal([]byte(param), &p)
	if err != nil {
		return 0
	}
	return p.PersonUid
}


func extractDataField(ctx *gin.Context, jsonStr string) (string, error) {
	var response struct {
		Data  interface{} `json:"data"`
		ErrNo int         `json:"errNo"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &response); err != nil {
		return "", err
	}

	if response.ErrNo != 0 {
		return "", fmt.Errorf("errNo is not 0")
	}

	toString, err := json.MarshalToString(response.Data)
	if err != nil {
		return "", err
	}

	if toString == "[]" {
		return "", nil
	}

	jsoniter, err := components.Util.SortJSONKeysWithJsoniter(toString)
	if err != nil {
		zlog.Warnf(ctx, "sort json keys failed, err:%v, json:%s", err, toString)
		return toString, nil
	}

	return jsoniter, nil
}