package diffRegister

import (
	toolsInput "fwyytool/controllers/http/tools/input"
	"fwyytool/service/tools"
	"fwyytool/stru"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type baseDataDiffAPI struct{}

func (d *baseDataDiffAPI) GetOldData(ctx *gin.Context, record *stru.ArkStudentListDataDiff, diffConfig map[string]stru.APIDiffConfig) (jsonData string, err error) {
	config, ok := diffConfig[record.HandlerName]
	if !ok {
		return
	}
	// 反射调用
	resp, err := tools.HttpTestService.CallForDIff(ctx, toolsInput.HttpTestCallParam{
		Method:      config.RalMethod,
		Url:         config.OldPath,
		Body:        addDiffTag(ctx, record.Params),
		ContentType: config.RalConverter,
		Cookie:      addXUIDCookie(ctx, config.<PERSON>ie, getPersonUid(record.Params)),
		Header:      "charset:UTF-8",
		Value:       "",
	})
	if err != nil {
		zlog.Warnf(ctx, "GetOldData error: %v", err)
		return
	}

	return extractDataField(ctx, resp.Body)
}

func (d *baseDataDiffAPI) GetNewData(ctx *gin.Context, record *stru.ArkStudentListDataDiff, diffConfig map[string]stru.APIDiffConfig) (jsonData string, err error) {
	config, ok := diffConfig[record.HandlerName]
	if !ok {
		return
	}
	// 反射调用
	resp, err := tools.HttpTestService.CallForDIff(ctx, toolsInput.HttpTestCallParam{
		Method:      config.RalMethod,
		Url:         config.NewPath,
		Body:        addDiffTag(ctx, record.Params),
		ContentType: config.RalConverter,
		Cookie:      addXUIDCookie(ctx, config.Cookie, getPersonUid(record.Params)),
		Header:      "charset:UTF-8",
		Value:       "",
	})
	if err != nil {
		zlog.Warnf(ctx, "GetNewData error: %v", err)
		return
	}
	return extractDataField(ctx, resp.Body)
}

func (d *baseDataDiffAPI) GetDiffResult(ctx *gin.Context, a, b string, record stru.ArkStudentListDataDiff, diffConfig map[string]stru.APIDiffConfig) (diffNum int64, filePath string, err error) {
	_, ok := diffConfig[record.HandlerName]
	if !ok {
		return
	}

	// 使用新的 jsondiffpatch 方式生成 HTML
	path, err := GenerateJSONDiffHTML(ctx, a, b, record.ID)
	if err != nil {
		return 0, "", err
	}

	// 计算差异数量（简单比较）
	diffCount := int64(0)
	if a != b {
		diffCount = 1
	}

	return diffCount, path, nil
}
