package diffRegister

import (
	"fwyytool/stru"
	"github.com/gin-gonic/gin"
	"strings"
)

type DataDiffAPI interface {
	GetOldData(ctx *gin.Context, record *stru.ArkStudentListDataDiff, diffConfig map[string]stru.APIDiffConfig) (jsonData string, err error)
	GetNewData(ctx *gin.Context, record *stru.ArkStudentListDataDiff, diffConfig map[string]stru.APIDiffConfig) (jsonData string, err error)
	GetDiffResult(ctx *gin.Context, a, b string, record stru.ArkStudentListDataDiff, diffConfig map[string]stru.APIDiffConfig) (diffNum int64, filePath string, err error)
}

// GetDataDiffAPI 支持的接口注册
func GetDataDiffAPI(handler string) DataDiffAPI {
	h := strings.Split(handler, "_")
	switch h[0] {
	default:
		return &baseDataDiffAPI{}
	}
}
