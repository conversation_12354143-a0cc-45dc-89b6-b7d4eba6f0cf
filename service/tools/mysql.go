package tools

import (
	"fwyytool/consts"
	"fwyytool/controllers/http/tools/output"
	"fwyytool/models/mysqlSlow"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
	"time"
)

var MysqlService mysqlService

type mysqlService struct {
}

func (s mysqlService) SlowStat(ctx *gin.Context, param SlowStatParam) (data output.SlowStatOutput, err error) {
	if len(param.Port) == 0 {
		return
	}

	data.TrendDataList, err = s.getSlowTrendList(ctx, param.PortArr, param.BeginDate, param.EndDate)
	if err != nil {
		zlog.Errorf(ctx, "getSlowTrendList err: %+v", err)
		return
	}

	cnt, err := mysqlSlow.MysqlSlowDao.GetSlowCnt(ctx, param.PortArr, param.BeginDate, param.EndDate)
	if err != nil {
		zlog.Errorf(ctx, "GetSlowCnt err: %+v", err)
		return
	}

	//指纹列表统计
	total, err := mysqlSlow.MysqlSlowDao.GetSlowCntTotal(ctx, param.PortArr, param.BeginDate, param.EndDate)
	if err != nil {
		zlog.Errorf(ctx, "GetSlowCntTotal err: %+v", err)
		return
	}
	slowStatList, err := mysqlSlow.MysqlSlowDao.GetSlowCntList(ctx, param.PortArr, param.BeginDate, param.EndDate, (param.Page-1)*param.PageSize, param.PageSize)
	if err != nil {
		zlog.Errorf(ctx, "GetSlowCntList err: %+v", err)
		return
	}
	data.Cnt = cnt
	data.Total = total
	for _, slowStat := range slowStatList {
		data.List = append(data.List, output.SlowStatList{Cnt: slowStat.Cnt, CheckSum: slowStat.CheckSum, Fingerprint: slowStat.Fingerprint})
	}
	return
}

func (s mysqlService) SlowList(ctx *gin.Context, param SlowListParam) (data output.SlowListOutput, err error) {
	if len(param.Port) == 0 || len(param.CheckSum) == 0 {
		return
	}
	total, err := mysqlSlow.MysqlSlowDao.GetTotal(ctx, param.PortArr, param.CheckSum, param.BeginDate, param.EndDate)
	if err != nil {
		return
	}

	slowList, err := mysqlSlow.MysqlSlowDao.GetList(ctx, param.PortArr, param.CheckSum, param.BeginDate, param.EndDate, (param.Page-1)*param.PageSize, param.PageSize)
	if err != nil {
		return
	}
	for _, detail := range slowList {
		data.List = append(data.List, output.SlowList{
			CheckSum:    detail.CheckSum,
			Fingerprint: detail.Fingerprint,
			MysqlIp:     detail.MysqlIp,
			MysqlPort:   detail.MysqlPort,
			ClientUser:  detail.ClientUser,
			ClientIp:    detail.ClientIp,
			Sql:         detail.Sql,
			QueryTime:   detail.QueryTime,
			Ts:          detail.Ts,
		})
	}
	data.Total = total
	return
}

func (s mysqlService) getSlowTrendList(ctx *gin.Context, portArr []string, beginDate string, endDate string) (trendList []output.TrendStat, err error) {
	clusterTrandMap, err := mysqlSlow.MysqlSlowDao.GetSlowHourTrendMap(ctx, portArr, beginDate, endDate)
	if err != nil {
		return
	}

	trendList = make([]output.TrendStat, 0)
	beginTime, _ := fwyyutils.AnyToTime(beginDate + " 00:00:00")
	endTime, _ := fwyyutils.AnyToTime(endDate + " 00:00:00")
	////填充没有数据的日期，小时级
	for currTime := beginTime; currTime < endTime; currTime += 3600 {
		currDate := time.Unix(currTime, 0).Format("2006-01-02 15:04:05")
		trendDare := output.TrendStat{
			Date:      currDate,
			TrendList: make([]output.ClusterTrand, 0),
		}

		currTrendList := make([]output.ClusterTrand, 0)
		total := uint64(0)
		for _, port := range portArr {
			clusterTrand := output.ClusterTrand{
				Port: port,
				Name: s.GetClusterName(ctx, []string{port}),
				Cnt:  0,
			}
			if _, ok := clusterTrandMap[currDate][port]; ok {
				clusterTrand.Cnt = clusterTrandMap[currDate][port]
				total += clusterTrand.Cnt
			}
			currTrendList = append(currTrendList, clusterTrand)
		}

		trendDare.TrendList = append(trendDare.TrendList, output.ClusterTrand{
			Port: strings.Join(portArr, ","),
			Name: s.GetClusterName(ctx, portArr),
			Cnt:  total,
		})
		trendDare.TrendList = append(trendDare.TrendList, currTrendList...)

		trendList = append(trendList, trendDare)
	}
	return
}

func (s mysqlService) GetClusterName(ctx *gin.Context, portArr []string) string {
	portString := strings.Join(portArr, ",")
	clusterNameMap := map[string]string{}
	for _, clusterGroup := range consts.ClusterList {
		ports := make([]string, 0)
		for _, detail := range clusterGroup.List {
			ports = append(ports, detail.Port)
		}
		clusterNameMap[strings.Join(ports, ",")] = clusterGroup.Name

		for _, detail := range clusterGroup.List {
			clusterNameMap[detail.Port] = detail.Name
		}
	}
	name, ok := clusterNameMap[portString]
	if !ok {
		name = portString
	}
	return name
}

func (s mysqlService) SlowCollect(ctx *gin.Context) (collectData []output.SlowCollectData, err error) {
	var weekOffset int64 = 60 * 60 * 24 * 7
	nextWeekendDate := fwyyutils.GetNextFirstDateOfWeek(time.Now()).Unix()
	collectData = make([]output.SlowCollectData, 0)

	for _, clusterGroup := range consts.ClusterList {
		ports := make([]string, 0)
		for _, detail := range clusterGroup.List {
			ports = append(ports, detail.Port)
		}

		collectDetail := output.SlowCollectData{Name: clusterGroup.Name}
		for i := 0; i < 4; i++ {
			benginDate := time.Unix(nextWeekendDate-weekOffset*(int64(i)+1), 0).Format("2006-01-02")
			endDate := time.Unix(nextWeekendDate-weekOffset*(int64(i)), 0).Format("2006-01-02")
			total, dbErr := mysqlSlow.MysqlSlowDao.GetSlowCollectCnt(ctx, ports, benginDate, endDate)
			if dbErr != nil {
				zlog.Errorf(ctx, "GetSlowCntTotal err: %+v", err)
				return
			}
			collectDetail.CntList = append(collectDetail.CntList, output.SlowCollectDataCnt{
				Cnt:       total,
				BeginDate: benginDate,
				EndDate:   endDate,
				Port:      strings.Join(ports, ","),
			})
		}
		collectData = append(collectData, collectDetail)
	}

	return
}

// GetFirstDateOfWeek 获取本周周一的日期
func GetFirstDateOfWeek(t time.Time) time.Time {
	offset := int(time.Monday - t.Weekday())
	if offset > 0 {
		offset = -6
	}
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local).
		AddDate(0, 0, offset)
}
