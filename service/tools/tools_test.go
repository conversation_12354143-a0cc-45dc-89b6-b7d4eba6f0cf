package tools

import (
	"fwyytool/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"
)

var initialized bool

func init() {
	if initialized {
		return
	}
	initialized = true
	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()

	//validator
	helpers.InitValidator()
	//esclient
	helpers.InitClickHouese()
}

// getSourcePath returns the directory containing the source code that is calling this function.
func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

type TimeList struct {
	StartTime int64 `json:"startTime"`
	StopTime  int64 `json:"stopTime"`
}

func TestSlowCollect(t *testing.T) {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	MysqlService.SlowCollect(ctx)
}

func TestClearQWUrlCache(t *testing.T) {
	helpers.InitRedis()
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	helpers.RedisClient.Set(ctx, "qiwei-course:short-url-10", "value", 0)
	err := ClearQWUrlCache(ctx, 0, 100)
	t.Log(err)
}

func TestHttpTestReport(t *testing.T) {
	helpers.InitRedis()
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	HttpTestService.AnalysisTrace(ctx)
}
