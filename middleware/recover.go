package middleware

import (
	"fwyytool/libs/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

// Recover middleware handler for panic
func Recover(ctx *gin.Context) {
	defer utils.CatchPanic(ctx, func() {
		select {
		case <-ctx.Writer.CloseNotify():
			break
		default:
			base.RenderJsonAbort(ctx, base.Error{
				ErrNo:  5000,
				ErrMsg: "system internal error",
			})
		}
	})
	ctx.Next()
}
