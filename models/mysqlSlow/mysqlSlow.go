package mysqlSlow

import (
	"fmt"
	"fwyytool/helpers"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"strings"
)

var MysqlSlowDao mysqlSlowDao

type mysqlSlowDao struct {
}

type MysqlSlow struct {
	CheckSum    string  `json:"checkSum"`
	Fingerprint string  `json:"fingerPrint"`
	MysqlIp     string  `json:"mysqlIp"`
	MysqlPort   string  `json:"mysqlPort"`
	ClientUser  string  `json:"clientUser"`
	ClientIp    string  `json:"clientIp"`
	Sql         string  `json:"sql"`
	Cnt         uint64  `json:"cnt"`
	QueryTime   float64 `json:"queryTime"`
	Ts          string  `json:"ts"`
}

func (s mysqlSlowDao) GetTotal(ctx *gin.Context, portArr []string, checkSum string, beginDate string, endDate string) (res int64, err error) {
	if len(portArr) == 0 {
		return
	}

	portList := make([]string, len(portArr))
	for idx := range portArr {
		portList[idx] = fmt.Sprintf("'%s'", portArr[idx])
	}
	wherePort := fmt.Sprintf("mysql_port in (%s) and ", strings.Join(portList, ","))

	checkSumSql := ""
	if len(checkSum) > 0 {
		checkSumSql = fmt.Sprintf("checksum = '%s' and ", checkSum)
	}

	query := fmt.Sprintf("select count(*) as cnt from mysql_slow where checksum !='' and query_time > 0.1 and %s %s ts between '%s' and '%s'", wherePort, checkSumSql, beginDate, endDate)
	zlog.Infof(ctx, "clickhouse query:%s", query)
	row := helpers.ClickHouseClient.QueryRow(ctx, query)
	var (
		cnt uint64
	)

	if err = row.Scan(
		&cnt,
	); err != nil {
		return 0, err
	}

	return int64(cnt), nil
}

func (s mysqlSlowDao) GetList(ctx *gin.Context, portArr []string, checkSum string, beginDate string, endDate string, offset, limit int) (res []MysqlSlow, err error) {
	res = make([]MysqlSlow, 0)
	if len(portArr) == 0 {
		return
	}

	portList := make([]string, len(portArr))
	for idx := range portArr {
		portList[idx] = fmt.Sprintf("'%s'", portArr[idx])
	}
	wherePort := fmt.Sprintf("mysql_port in (%s) and ", strings.Join(portList, ","))

	limitStr := fmt.Sprintf("order by query_time desc limit %d, %d", offset, limit)

	checkSumSql := ""
	if len(checkSum) > 0 {
		checkSumSql = fmt.Sprintf("checksum = '%s' and ", checkSum)
	}
	query := fmt.Sprintf("select checksum, fingerprint, mysql_ip, mysql_port, sql, query_time, toString(ts) as dateTime from mysql_slow where checksum !='' and query_time > 0.1 and %s %s ts between '%s' and '%s' %s", wherePort, checkSumSql, beginDate, endDate, limitStr)
	zlog.Infof(ctx, "clickhouse query:%s", query)
	rows, queryErr := helpers.ClickHouseClient.Query(ctx, query)
	if queryErr != nil {
		return nil, queryErr
	}
	for rows.Next() {
		var (
			checksum, fingerprint, mysqlIp, mysqlPort, sql, dateTime string
			queryTime                                                float32
		)
		if err = rows.Scan(
			&checksum,
			&fingerprint,
			&mysqlIp,
			&mysqlPort,
			&sql,
			&queryTime,
			&dateTime,
		); err != nil {
			return nil, err
		}
		res = append(res, MysqlSlow{Fingerprint: fingerprint, CheckSum: checksum, MysqlIp: mysqlIp, MysqlPort: mysqlPort, Sql: sql, QueryTime: float64(queryTime), Ts: dateTime})
	}
	return
}

func (s mysqlSlowDao) GetSlowCnt(ctx *gin.Context, portArr []string, beginDate string, endDate string) (res int64, err error) {
	if len(portArr) == 0 {
		return
	}

	portList := make([]string, len(portArr))
	for idx := range portArr {
		portList[idx] = fmt.Sprintf("'%s'", portArr[idx])
	}
	wherePort := fmt.Sprintf("mysql_port in (%s) and ", strings.Join(portList, ","))

	query := fmt.Sprintf("select count(*) as cnt from mysql_slow where checksum !='' and query_time > 0.1 and %s ts between '%s' and '%s';", wherePort, beginDate, endDate)
	zlog.Infof(ctx, "clickhouse query:%s", query)
	row := helpers.ClickHouseClient.QueryRow(ctx, query)
	var (
		cnt uint64
	)

	if err = row.Scan(
		&cnt,
	); err != nil {
		return 0, err
	}

	return int64(cnt), nil
}

func (s mysqlSlowDao) GetSlowHourTrendMap(ctx *gin.Context, portArr []string, beginDate string, endDate string) (hourTrendMap map[string]map[string]uint64, err error) {
	hourTrendMap = map[string]map[string]uint64{}
	if len(portArr) == 0 {
		return
	}

	portList := make([]string, len(portArr))
	for idx := range portArr {
		portList[idx] = fmt.Sprintf("'%s'", portArr[idx])
	}
	wherePort := fmt.Sprintf("port in (%s) and ", strings.Join(portList, ","))

	query := fmt.Sprintf("SELECT toString(toStartOfHour(ts)) as date, mysql_port as port, count() as cnt from mysql_slow where checksum !='' and query_time > 0.1 and %s ts between '%s' and '%s' GROUP BY date, port ORDER BY date, port", wherePort, beginDate, endDate)
	zlog.Infof(ctx, "clickhouse query:%s", query)
	rows, queryErr := helpers.ClickHouseClient.Query(ctx, query)
	if queryErr != nil {
		return nil, queryErr
	}
	for rows.Next() {
		var (
			date, port string
			cnt        uint64
		)
		if err = rows.Scan(
			&date,
			&port,
			&cnt,
		); err != nil {
			return nil, err
		}
		if _, ok := hourTrendMap[date]; !ok {
			hourTrendMap[date] = map[string]uint64{}
		}
		hourTrendMap[date][port] = cnt
	}
	return hourTrendMap, nil
}

func (s mysqlSlowDao) GetSlowCntTotal(ctx *gin.Context, portArr []string, beginDate string, endDate string) (res int64, err error) {
	if len(portArr) == 0 {
		return
	}

	portList := make([]string, len(portArr))
	for idx := range portArr {
		portList[idx] = fmt.Sprintf("'%s'", portArr[idx])
	}
	wherePort := fmt.Sprintf("mysql_port in (%s) and ", strings.Join(portList, ","))

	query := fmt.Sprintf("select count(1) as total from (select count(1) as cnt, checksum, fingerprint from mysql_slow where checksum !='' and query_time > 0.1 and %s ts between '%s' and '%s' group by checksum, fingerprint);", wherePort, beginDate, endDate)
	zlog.Infof(ctx, "clickhouse query:%s", query)
	row := helpers.ClickHouseClient.QueryRow(ctx, query)
	var (
		cnt uint64
	)

	if err = row.Scan(
		&cnt,
	); err != nil {
		return 0, err
	}

	return int64(cnt), nil
}

func (s mysqlSlowDao) GetSlowCntList(ctx *gin.Context, portArr []string, beginDate string, endDate string, offset, limit int) (res []MysqlSlow, err error) {
	res = make([]MysqlSlow, 0)

	if len(portArr) == 0 {
		return
	}

	portList := make([]string, len(portArr))
	for idx := range portArr {
		portList[idx] = fmt.Sprintf("'%s'", portArr[idx])
	}
	wherePort := fmt.Sprintf("mysql_port in (%s) and ", strings.Join(portList, ","))

	limitStr := fmt.Sprintf("order by cnt desc limit %d, %d", offset, limit)
	query := fmt.Sprintf("select count(*) as cnt, checksum, fingerprint from mysql_slow where checksum !='' and query_time > 0.1 and %s ts between '%s' and '%s' group by checksum, fingerprint %s;", wherePort, beginDate, endDate, limitStr)
	zlog.Infof(ctx, "clickhouse query:%s", query)
	rows, queryErr := helpers.ClickHouseClient.Query(ctx, query)
	if queryErr != nil {
		return nil, queryErr
	}

	for rows.Next() {
		var (
			cnt                   uint64
			checksum, fingerprint string
		)
		if err = rows.Scan(
			&cnt,
			&checksum,
			&fingerprint,
		); err != nil {
			return nil, err
		}
		res = append(res, MysqlSlow{Cnt: cnt, CheckSum: checksum, Fingerprint: fingerprint})
	}
	return
}

func (s mysqlSlowDao) GetSlowCollectCnt(ctx *gin.Context, portArr []string, beginDate string, endDate string) (res int64, err error) {
	if len(portArr) == 0 {
		return
	}

	portList := make([]string, len(portArr))
	for idx := range portArr {
		portList[idx] = fmt.Sprintf("'%s'", portArr[idx])
	}
	wherePort := fmt.Sprintf("mysql_port in (%s) and ", strings.Join(portList, ","))

	query := fmt.Sprintf("select count(mysql_port) as cnt from mysql_slow where query_time > 0.1 and %s ts between '%s' and '%s';", wherePort, beginDate, endDate)
	zlog.Infof(ctx, "clickhouse query:%s", query)
	row := helpers.ClickHouseClient.QueryRow(ctx, query)
	var (
		cnt uint64
	)

	if err = row.Scan(
		&cnt,
	); err != nil {
		return 0, err
	}

	return int64(cnt), nil
}
