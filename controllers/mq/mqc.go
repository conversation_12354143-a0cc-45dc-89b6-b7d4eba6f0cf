package mq

import (
	"fwyytool/libs/mqc"
	"git.zuoyebang.cc/pkg/golib/v2/pool/gpool"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"time"
)

func RegisterMQConsumer(mqc *mqc.MQC) {
	r := mqresource{}
	mqc.RegisterConsumer("xxxx", r.RMQConsumer)
}

type mqresource struct {
}

const (
	gPoolSize = 1
)

var (
	GPool *gpool.Pool
)

func init() {
	var err error
	GPool, err = gpool.NewPool(gPoolSize)
	if err != nil {
		panic(err)
	}
}

func (m mqresource) RMQConsumer(ctx *gin.Context, msg rmq.Message) error {
	defer func() {
		if err := recover(); err != nil {
			zlog.Errorf(ctx, "fwyytoolConsumer error", zap.Any("error", err))
		}
	}()

	err := GPool.Submit(func() {
		err := m.execute(ctx, msg)
		if err != nil {
			zlog.Errorf(ctx, "err: %s", err)
			return
		}
	})
	if err != nil {
		zlog.Errorf(ctx, "err: %s", err)
		return err
	}
	return nil
}

func (m mqresource) execute(ctx *gin.Context, msg rmq.Message) error {
	var err error
	startTime := time.Now()
	switch msg.GetTag() {
	case "xxxx":
		//err = Examfwyytool.TagIDExamfwyytool(ctx, msg)
	default:
		zlog.Infof(ctx, "unknown tagID: %v", msg.GetTag())
		return nil
	}
	zlog.Info(ctx, "got message id=", msg.GetID(), " shard=", msg.GetShard(), " tag=", msg.GetTag(), " content=", msg.GetContent(), " convert cost time=", time.Now().Sub(startTime))
	return err
}
