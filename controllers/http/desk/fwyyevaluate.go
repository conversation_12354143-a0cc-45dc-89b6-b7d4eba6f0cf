package desk

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

var FwyyEvaluateController fwyyEvaluateController

type fwyyEvaluateController struct {
}

func (s fwyyEvaluateController) TextConfig(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "desk/fwyyevaluate/textconfig.html", nil)
	return
}

func (s fwyyEvaluateController) CourseMapping(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "desk/fwyyevaluate/coursemapping.html", nil)
	return
}
