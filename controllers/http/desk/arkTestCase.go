package desk

import (
	"fwyytool/libs/json"
	"fwyytool/service/desk"
	"github.com/gin-gonic/gin"
	"net/http"
)

var ArkTestCaseController arkTestCaseController

type arkTestCaseController struct {
}

func (s arkTestCaseController) GetArkTestCaseByRuleId(ctx *gin.Context) {
	errMsg := ""
	var params struct {
		RuleId  int64 `json:"ruleId" form:"ruleId" binding:"required"`
		IsInner int   `json:"isInner" form:"isInner"`
	}
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	data, err := desk.ArkTestCaseService.GetArkTestCaseByRuleId(ctx, params.RuleId, params.IsInner)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, "desk/arktestcase/rule.html", output)
	return
}

func (s arkTestCaseController) GetArkTestCaseByCname(ctx *gin.Context) {
	errMsg := ""
	var params struct {
		Cname   string `json:"cname" form:"cname" binding:"required"`
		IsInner int    `json:"isInner" form:"isInner"`
	}
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	data, err := desk.ArkTestCaseService.GetArkTestCaseByCname(ctx, params.Cname, params.IsInner)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, "desk/arktestcase/cname.html", output)
	return
}

func (s arkTestCaseController) GetArkTestCaseByToolId(ctx *gin.Context) {
	errMsg := ""
	var params struct {
		ToolId       int64  `json:"toolId" form:"toolId" binding:"required"`
		ComponentKey string `json:"componentKey" form:"componentKey" binding:"required"`
		IsInner      int    `json:"isInner" form:"isInner"`
	}
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	data, err := desk.ArkTestCaseService.GetArkTestCaseByToolId(ctx, params.ToolId, params.ComponentKey, params.IsInner)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, "desk/arktestcase/tool.html", output)
	return
}
