package desk

import (
	deskinput "fwyytool/controllers/http/desk/input"
	"fwyytool/libs/json"
	"fwyytool/service/desk"
	"github.com/gin-gonic/gin"
	"net/http"
)

var OplogController oplogController

type oplogController struct {
}

func (s oplogController) List(ctx *gin.Context) {
	errMsg := ""
	params := &deskinput.OperateLogQueryReq{}
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	data, err := desk.OplogService.ListOpLog(ctx, params)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, "desk/oplog/list.html", output)
	return
}
