package desk

import (
	"fwyytool/components"
	deskInput "fwyytool/controllers/http/desk/input"
	"fwyytool/service/desk"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

var UserController userController

type userController struct {
}

// ConvertXuidToPersonUidApi 将xuid转换为真人uid的API接口
// 接收xuid参数（可以是真人uid或资产uid），返回转换后的真人uid
func (s userController) ConvertXuidToPersonUidApi(ctx *gin.Context) {
	var params deskInput.ConvertXuidParam
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err := params.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	personUid, err := desk.UserService.ConvertXuidToPersonUid(ctx, params.Xuid)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	// 返回转换后的真人uid
	resp := gin.H{
		"xuid": personUid,
	}
	base.RenderJsonSucc(ctx, resp)
}
