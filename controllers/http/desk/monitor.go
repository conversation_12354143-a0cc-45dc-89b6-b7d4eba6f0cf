package desk

import (
	"fwyytool/components"
	"fwyytool/service/monitor"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

var MonitorController monitorController

type monitorController struct {
}

func (s monitorController) Today(ctx *gin.Context) {
	err := monitor.TeacherService.Today(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
	return
}
