package desk

import (
	"fwyytool/components"
	deskInput "fwyytool/controllers/http/desk/input"
	"fwyytool/libs/json"
	"fwyytool/service/desk"
	"net/http"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

var ArkController arkController

type arkController struct {
}

func (s arkController) Detail(ctx *gin.Context) {
	errMsg := ""
	var params deskInput.DetailParam
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	err := params.Validate(ctx)
	if err != nil {
		errMsg = err.Error()
	}

	data, err := desk.ArkService.Detail(ctx, params)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, "desk/ark/detail.html", output)
	return
}

func (s arkController) DetailApi(ctx *gin.Context) {
	errMsg := ""
	var params deskInput.DetailParam
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	err := params.Validate(ctx)
	if err != nil {
		errMsg = err.Error()
	}

	data, err := desk.ArkService.Detail(ctx, params)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	resp := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	base.RenderJsonSucc(ctx, resp)
}

func (s arkController) JumpTaskList(ctx *gin.Context) {
	output := gin.H{}
	ctx.HTML(http.StatusOK, "desk/ark/jumptasklist.html", output)
}

func (s arkController) JumpTaskListApi(ctx *gin.Context) {
	var params deskInput.JumpTaskListParam
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err := params.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := desk.ArkService.JumpTaskList(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}
