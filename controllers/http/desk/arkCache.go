package desk

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

var ArkCacheController arkCacheController

type arkCacheController struct {
}

func (s arkCacheController) DelAllArkCache(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "desk/cachetool/delarkcache.html", nil)
	return
}

func (s arkCacheController) DelCacheByKey(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "desk/cachetool/delcachebykey.html", nil)
	return
}
