package desk

import (
	deskInput "fwyytool/controllers/http/desk/input"
	"fwyytool/libs/json"
	"fwyytool/service/desk"
	"github.com/gin-gonic/gin"
	"net/http"
)

var CourseController courseController

type courseController struct {
}

func (s courseController) Detail(ctx *gin.Context) {
	errMsg := ""
	var params deskInput.DetailParam
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	err := params.Validate(ctx)
	if err != nil {
		errMsg = err.Error()
	}

	data, err := desk.CourseService.Detail(ctx, params)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, "desk/course/detail.html", output)
	return
}
