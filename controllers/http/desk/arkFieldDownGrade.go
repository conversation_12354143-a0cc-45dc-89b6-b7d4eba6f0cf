package desk

import (
	"fwyytool/libs/json"
	"fwyytool/service/desk"
	"github.com/gin-gonic/gin"
	"net/http"
)

var ArkFieldDownGradeController arkFieldDownGradeController

type arkFieldDownGradeController struct {
}

func (s arkFieldDownGradeController) GetArkFieldDownGradeConfig(ctx *gin.Context) {
	errMsg := ""

	data, err := desk.ArkFieldDownGradeService.GetArkFieldDownGradeConfig(ctx)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
	}
	ctx.HTML(http.StatusOK, "desk/ark/arkFieldDownGrade.html", output)
	return
}

func (s arkFieldDownGradeController) GetNewArkFieldDownGradeConfig(ctx *gin.Context) {
	errMsg := ""

	data, err := desk.ArkFieldDownGradeService.GetNewArkFieldDownGradeConfig(ctx)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
	}
	ctx.HTML(http.StatusOK, "desk/ark/newArkFieldDownGrade.html", output)
	return
}
