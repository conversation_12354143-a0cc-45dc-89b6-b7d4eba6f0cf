package genke

import (
	"fwyytool/controllers/http/genke/input"
	"fwyytool/libs/json"
	"fwyytool/service/genke"
	"github.com/gin-gonic/gin"
	"net/http"
)

var AttendController attendController

type attendController struct {
}

func (s attendController) Detail(ctx *gin.Context) {
	errMsg := ""
	var params input.DetailParam
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	err := params.Validate(ctx)
	if err != nil {
		errMsg = err.Error()
	}

	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	data, err := genke.Attend.Detail(ctx, params.LessonID, params.StudentUid)
	if err != nil {
		errMsg = err.Error()
	}

	dataJson, _ := json.MarshalToString(data)
	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, "genke/attend/detail.html", output)
	return
}
