package dto

import (
	"errors"
	"github.com/gin-gonic/gin"
)

type GetCourseInfoBySkuIdReq struct {
	SkuId int64 `json:"skuId" form:"skuId"`
}

func (gcibsi *GetCourseInfoBySkuIdReq) Validate(ctx *gin.Context) (err error) {
	if gcibsi.SkuId == 0 {
		err = errors.New("请输入商品id")
		return
	}
	return
}

type GetCourseInfoBySkuIdResp struct {
	List []GetCourseInfoBySkuIdItem `json:"list,omitempty"`
}

type GetCourseInfoBySkuIdItem struct {
	CourseId            int64    `json:"courseId,omitempty"`
	CourseName          string   `json:"courseName,omitempty"`
	NewCourseTypeDesc   string   `json:"newCourseTypeDesc,omitempty"`
	MainGradeDesc       string   `json:"mainGradeDesc,omitempty"`
	MainSubjectDesc     string   `json:"mainSubjectDesc,omitempty"`
	YearDesc            string   `json:"yearDesc,omitempty"`
	LearnSeasonDesc     string   `json:"learnSeasonDesc,omitempty"`
	StartTimeDesc       string   `json:"startTimeDesc,omitempty"`
	StopTimeDesc        string   `json:"stopTimeDesc,omitempty"`
	SkuId               int64    `json:"skuId,omitempty"`
	IsOnSaleDesc        string   `json:"isOnSaleDesc,omitempty"`
	IsInnerDesc         string   `json:"isInnerDesc,omitempty"`
	TeachersDesc        []string `json:"teachersDesc,omitempty"`
	AssistantPersonDesc []string `json:"assistantPersonDesc,omitempty"`
}
