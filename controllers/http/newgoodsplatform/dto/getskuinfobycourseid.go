package dto

import (
	"errors"
	"github.com/gin-gonic/gin"
)

type GetSkuInfoByCourseIdReq struct {
	CourseId int64 `json:"courseId" form:"courseId"`
}

func (gsibci *GetSkuInfoByCourseIdReq) Validate(ctx *gin.Context) (err error) {
	if gsibci.CourseId == 0 {
		err = errors.New("请输入课程id")
		return
	}
	return
}

type GetSkuInfoByCourseIdResp struct {
	List []GetSkuInfoByCourseIdItem `json:"list"`
}

type GetSkuInfoByCourseIdItem struct {
	SkuId           int64  `json:"skuId"`
	SkuName         string `json:"skuName"`
	Source          int64  `json:"source"`
	SourceName      string `json:"sourceName"`
	Category        int64  `json:"category"`
	CategoryName    string `json:"categoryName"`
	Price           int64  `json:"price"`
	SkuOriginPrice  int64  `json:"skuOriginPrice"`
	CombinationType int64  `json:"combinationType"`
	SpuId           int64  `json:"spuId"`
	SkuMode         int64  `json:"skuMode"`
	ShopId          int64  `json:"shopId"`
	AttributeTags   string `json:"attributeTags"`
	SpecTags        string `json:"specTags"`
	LabelTags       string `json:"labelTags"`
	OnSaleStatus    int64  `json:"onSaleStatus"` // 上架状态；1=上架，0=下架
	OnSaleStartTime int64  `json:"onSaleStartTime"`
	OnSaleStopTime  int64  `json:"onSaleStopTime"`
	IsInner         int64  `json:"isInner"` // 内外部商品；1=内部测试，0=外部发布
}
