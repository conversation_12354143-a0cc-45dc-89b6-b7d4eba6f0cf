package newgoodsplatform

import (
	"fwyytool/controllers/http/newgoodsplatform/dto"
	"fwyytool/libs/json"
	"fwyytool/service/newgoodsplatform"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"net/http"
)

func GetSkuInfoByCourseId(ctx *gin.Context) {
	params, data, err := doGetSkuInfoByCourseId(ctx)

	var dataJson string
	var errMsg string
	if err == nil {
		dataJson, _ = json.MarshalToString(data)
	} else {
		errMsg = err.Error()
	}
	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}

	ctx.HTML(http.StatusOK, "newgoodsplatform/skucourse/getSkuInfoByCourseId.html", output)
	return
}

func doGetSkuInfoByCourseId(ctx *gin.Context) (req dto.GetSkuInfoByCourseIdReq, resp dto.GetSkuInfoByCourseIdResp, err error) {
	if err = ctx.ShouldBind(&req); err != nil {
		return
	}

	err = req.Validate(ctx)
	if err != nil {
		return
	}

	resp, err = newgoodsplatform.GetSkuInfoByCourseId(ctx, req)
	if err != nil {
		zlog.Warnf(ctx, "GetSkuInfoByCourseId.doGetSkuInfoByCourseId fail, req: %+v, err: %+v", req, err)
		return
	}
	return
}
