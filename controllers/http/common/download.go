package common

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

var DownloadController downloadController

type downloadController struct {
}

const (
	resourceDir = "assets/resource"
)

// Plugin 处理插件文件下载
func (s downloadController) Plugin(ctx *gin.Context) {
	// 获取文件名参数
	fileName := ctx.Query("file")
	if fileName == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "文件名参数不能为空"})
		return
	}

	// 构建文件路径
	filePath := filepath.Join(resourceDir, fileName)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "文件不存在"})
		return
	}

	// 设置响应头
	ctx.Header("Content-Description", "File Transfer")
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	ctx.Header("Content-Type", "application/octet-stream")

	// 发送文件
	ctx.File(filePath)
}

// ListPlugins 获取可下载的插件列表（为扩展性预留）
func (s downloadController) ListPlugins(ctx *gin.Context) {
	// 检查是否请求JSON格式
	if ctx.GetHeader("Accept") == "application/json" || ctx.Query("format") == "json" {
		s.listPluginsJSON(ctx)
		return
	}

	// 默认返回HTML页面
	ctx.HTML(http.StatusOK, "common/download/list.html", gin.H{
		"title": "插件文件列表",
	})
}

// listPluginsJSON 返回JSON格式的文件列表
func (s downloadController) listPluginsJSON(ctx *gin.Context) {
	// 扫描resource目录下的文件
	files := []map[string]any{}

	err := filepath.Walk(resourceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理文件，不处理目录
		if !info.IsDir() {
			// 获取相对于resource目录的路径
			relPath, _ := filepath.Rel(resourceDir, path)
			// 只显示zip文件
			if strings.HasSuffix(strings.ToLower(relPath), ".zip") {
				files = append(files, map[string]any{
					"name":    relPath,
					"size":    info.Size(),
					"modTime": info.ModTime().Format("2006-01-02 15:04:05"),
				})
			}
		}
		return nil
	})

	if err != nil {
		zlog.Errorf(ctx, "listPluginsJSON err:%+v", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "读取文件列表失败"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"files": files,
	})
}
