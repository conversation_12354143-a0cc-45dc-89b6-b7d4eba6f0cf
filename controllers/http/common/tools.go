package common

import (
	"fwyytool/controllers/http/common/input"
	"fwyytool/libs/json"
	"fwyytool/service/common"
	"github.com/gin-gonic/gin"
	"net/http"
)

var ToolsController toolsController

type toolsController struct {
}

func (s toolsController) IdCode(ctx *gin.Context) {
	errMsg := ""
	var params input.IdCodeParam
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	err := params.Validate(ctx)
	if err != nil {
		errMsg = err.Error()
	}

	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	data, err := common.Tools.IdCode(ctx, params.EncodeInput, params.DecodeInput)
	if err != nil {
		errMsg = err.Error()
	}

	dataJson, _ := json.MarshalToString(data)
	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}

	ctx.HTML(http.StatusOK, "common/tools/idcode.html", output)
	return
}
