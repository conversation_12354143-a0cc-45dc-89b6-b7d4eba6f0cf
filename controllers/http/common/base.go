package common

import (
	"github.com/gin-gonic/gin"
)

func RegisterHandlers(rg *gin.RouterGroup) {
	routerGroup := rg.Group("common")
	toolsGroup := routerGroup.Group("tools")
	{
		toolsGroup.GET("idcode", ToolsController.IdCode)
	}

	downloadGroup := routerGroup.Group("download")
	{
		downloadGroup.GET("plugin", DownloadController.Plugin)
		downloadGroup.GET("list", DownloadController.ListPlugins)
	}
}
