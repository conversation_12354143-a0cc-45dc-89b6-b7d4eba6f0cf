package common

import (
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"net/http"
)

func SetOutput(ctx *gin.Context, errMsg string, data, params interface{}, pathName string) {
	dataJson, _ := jsoniter.MarshalToString(data)
	output := gin.H{
		"data":     transDataToMap(ctx, data),
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   transDataToMap(ctx, params),
	}
	ctx.HTML(http.StatusOK, pathName, output)
}

func SetOutputWithStruct(ctx *gin.Context, errMsg string, data, params interface{}, pathName string) {
	dataJson, _ := jsoniter.MarshalToString(data)
	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, pathName, output)
}

func transDataToMap(ctx *gin.Context, data interface{}) map[string]interface{} {
	dataJson, _ := jsoniter.MarshalToString(data)
	result := make(map[string]interface{})
	_ = jsoniter.UnmarshalFromString(dataJson, &result)
	return result
}
