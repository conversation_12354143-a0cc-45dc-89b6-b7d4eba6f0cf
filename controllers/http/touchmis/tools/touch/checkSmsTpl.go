package touch

import (
	"fwyytool/controllers/http/touchmis/common"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmisgo"
	"github.com/gin-gonic/gin"
)

const (
	checkSmsTplHtmlShowPath = "touchmis/tools/checkSmsTpl.html"
)

func CheckSmsTpl(ctx *gin.Context) {
	var params CheckSmsTplReq
	if err := ctx.ShouldBind(&params); err != nil {
		common.SetOutput(ctx, err.Error(), nil, params, checkSmsTplHtmlShowPath)
		return
	}

	req := touchmisgo.CheckSmsTplReq{
		AssistantId: params.AssistantId,
		CourseId:    params.CourseId,
		LessonId:    params.LessonId,
		SendType:    params.SendType,
		Action:      params.Action,
		SceneId:     params.SceneId,
	}
	if params.StudentId > 0 {
		req.StudentIds = []int64{params.StudentId}
	}

	rsp, err := touchmisgo.CheckSmsTpl(ctx, req)
	if err != nil {
		common.SetOutput(ctx, err.Error(), nil, params, checkSmsTplHtmlShowPath)
		return
	} else if rsp == nil {
		common.SetOutput(ctx, "rsp is nil", nil, params, checkSmsTplHtmlShowPath)
		return
	}
	common.SetOutput(ctx, "", rsp, params, checkSmsTplHtmlShowPath)
}
