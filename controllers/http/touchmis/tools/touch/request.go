package touch

type GetCallRecordInfoReq struct {
	CallId int64 `json:"callId" form:"callId"`
}

type CheckSmsTplReq struct {
	AssistantId int64 `json:"assistantId" form:"assistantId"`
	CourseId    int64 `json:"courseId" form:"courseId"`
	LessonId    int64 `json:"lessonId" form:"lessonId"`
	SendType    int64 `json:"sendType" form:"sendType"`
	Action      int64 `json:"action" form:"action"`
	SceneId     int64 `json:"sceneId" form:"sceneId"`
	StudentId   int64 `json:"studentId" form:"studentId"`
}
