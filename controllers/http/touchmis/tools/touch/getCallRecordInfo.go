package touch

import (
	"fwyytool/controllers/http/touchmis/common"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmisgo"
	"github.com/gin-gonic/gin"
)

const (
	pathName = "touchmis/tools/getCallRecordInfo.html"
)

func GetCallRecordInfo(ctx *gin.Context) {
	var params GetCallRecordInfoReq
	if err := ctx.ShouldBind(&params); err != nil {
		common.SetOutput(ctx, err.Error(), nil, params, pathName)
		return
	}

	rsp, err := getCallRecordInfo(ctx, params)
	if err != nil {
		common.SetOutput(ctx, err.Error(), nil, params, pathName)
		return
	}
	common.SetOutput(ctx, "", rsp, params, pathName)
}

func getCallRecordInfo(ctx *gin.Context, req GetCallRecordInfoReq) (*touchmisgo.CallRecordInfo, error) {
	if req.CallId <= 0 {
		return nil, nil
	}

	fields := []string{
		"id",
		"call_id",
		"source_type",
		"from_uid",
		"kms_from_phone",
		"hash_from_phone",
		"to_uid",
		"kms_to_phone",
		"hash_to_phone",
		"resource_type",
		"line",
		"call_mode",
		"start_time",
		"stop_time",
		"duration",
		"call_type",
		"call_result",
		"record_file",
		"trigger_type",
		"course_id",
		"lesson_id",
		"class_id",
		"learn_season",
		"server_id",
		"ext_data",
		"upload_time",
		"device_uid",
		"person_uid",
		"deleted",
		"create_time",
		"update_time",
	}

	recordInfoMap, err := touchmisgo.GetCallRecordInfo(ctx, &touchmisgo.GetCallRecordInfoReq{
		CallIds: []int64{req.CallId},
		Fields:  fields,
	})
	if err != nil {
		return nil, err
	}
	return recordInfoMap[req.CallId], nil
}
