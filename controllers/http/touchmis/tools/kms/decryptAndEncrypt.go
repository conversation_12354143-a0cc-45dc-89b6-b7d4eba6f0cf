package kms

import (
	"fwyytool/controllers/http/touchmis/common"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmis"
	"github.com/gin-gonic/gin"
)

const (
	pathName = "touchmis/tools/decryptAndEncrypt.html"
)

func DecryptAndEncrypt(ctx *gin.Context) {
	var params DecryptAndEncryptReq
	if err := ctx.ShouldBind(&params); err != nil {
		common.SetOutput(ctx, err.Error(), nil, params, pathName)
		return
	}

	rsp, err := decryptAndEncrypt(ctx, params)
	if err != nil {
		common.SetOutput(ctx, err.Error(), nil, params, pathName)
		return
	}
	common.SetOutput(ctx, "", rsp, params, pathName)
}

func decryptAndEncrypt(ctx *gin.Context, req DecryptAndEncryptReq) (*DecryptAndEncryptRsp, error) {
	rsp := &DecryptAndEncryptRsp{}
	var err error
	if len(req.EncodeInput) > 0 {
		rsp.EncodeValue, err = touchmis.Encrypt(ctx, touchmis.EncryptReq{
			SourceStr: req.EncodeInput,
		})
		if err != nil {
			return nil, err
		}
	}

	if len(req.DecodeInput) > 0 {
		rsp.DecodeValue, err = touchmis.Decrypt(ctx, touchmis.DecryptReq{
			EncryptStr: req.DecodeInput,
		})
		if err != nil {
			return nil, err
		}
	}
	return rsp, nil
}
