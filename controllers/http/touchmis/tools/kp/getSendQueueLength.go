package kp

import (
	"errors"
	"fwyytool/controllers/http/touchmis/common"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/kunpeng"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/mesh"
	"github.com/gin-gonic/gin"
)

const (
	htmlShowPath = "touchmis/tools/getSendQueueLength.html"
	MsgCnt       = 100
)

func GetSendQueueLength(ctx *gin.Context) {
	var params GetSendQueueLengthReq
	if err := ctx.ShouldBind(&params); err != nil {
		common.SetOutput(ctx, err.Error(), nil, params, htmlShowPath)
		return
	}

	rsp, err := mesh.GetDeviceListByDeviceUid(ctx, params.AssistantUid)
	if err != nil || rsp == nil {
		common.SetOutput(ctx, err.Error(), nil, params, htmlShowPath)
		return
	}

	res, err := getLengthAndRisk(ctx, rsp.WecomUserID, rsp.WecomCorpID)
	if err != nil {
		common.SetOutput(ctx, err.Error(), nil, rsp, htmlShowPath)
		return
	}
	common.SetOutput(ctx, "", res, params, htmlShowPath)
}
func getLengthAndRisk(ctx *gin.Context, userId, corpId string) (*QueueLengthAndRiskInfoRsp, error) {
	if userId == "" || corpId == "" {
		return nil, errors.New("无企微")
	}
	sqlReq := kunpeng.SendQueueLengthReq{
		CorpId: corpId,
		UserId: userId,
	}
	resLength, err := kunpeng.SendQueueLength(ctx, sqlReq)
	if err != nil {
		return nil, err
	}

	grcReq := kunpeng.GetRiskConfigReq{
		UserId:      userId,
		CorpId:      corpId,
		MsgCnt:      MsgCnt,
		IfSendQueue: 1,
	}
	resRisk, err := kunpeng.GetRiskConfig(ctx, grcReq)
	if err != nil {
		return nil, err
	}
	return &QueueLengthAndRiskInfoRsp{
		QueueLengthInfo: resLength,
		RiskConfigInfo:  resRisk,
	}, nil
}
