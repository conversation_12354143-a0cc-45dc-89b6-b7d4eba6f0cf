package kp

import (
	"fwyytool/controllers/http/touchmis/common"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/kunpeng"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"strings"
)

const (
	pathName = "touchmis/tools/checkFriend.html"
)

func CheckFriend(ctx *gin.Context) {
	var params CheckFriendReq
	if err := ctx.ShouldBind(&params); err != nil {
		common.SetOutput(ctx, err.Error(), nil, params, pathName)
		return
	}

	rsp, err := checkFriend(ctx, params)
	if err != nil {
		common.SetOutput(ctx, err.Error(), nil, params, pathName)
		return
	}
	common.SetOutput(ctx, "", rsp, params, pathName)
}

func checkFriend(ctx *gin.Context, req CheckFriendReq) (*CheckFriendRsp, error) {
	studentIdStrArray := strings.Split(req.StudentIds, ",")
	studentIds := make([]int64, 0)
	for _, s := range studentIdStrArray {
		studentIds = append(studentIds, cast.ToInt64(s))
	}
	wxUserInfoMap, err := kunpeng.GetStudentWxIds(ctx, studentIds, req.AssistantUid)
	if err != nil {
		return nil, err
	}

	noConfirmStudentIds := make([]int64, 0)
	for _, id := range studentIds {
		if _, exist := wxUserInfoMap[id]; !exist {
			noConfirmStudentIds = append(noConfirmStudentIds, id)
		}
	}
	return &CheckFriendRsp{
		NoConfirmStudentIds: noConfirmStudentIds,
	}, nil
}
