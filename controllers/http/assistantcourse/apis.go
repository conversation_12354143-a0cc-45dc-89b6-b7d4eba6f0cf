package assistantcourse

import (
	"fwyytool/controllers/http/assistantcourse/dto"
	"fwyytool/libs/json"
	"fwyytool/service/assistantcourse"
	"github.com/gin-gonic/gin"
	"net/http"
)

var ApisController apisController

type apisController struct {
}

func (s apisController) GetTaskList(ctx *gin.Context) {
	errMsg := ""
	params := &dto.AssistantCoursePersonUid{}
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	data, err := assistantcourse.GetTaskList(ctx, params.PersonUid, params.Status, params.SelectType)
	if err != nil {
		errMsg = err.Error()
	}

	dataJson, _ := json.MarshalToString(data)
	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, "assistantcourse/apis/getTaskList.html", output)
	return
}
