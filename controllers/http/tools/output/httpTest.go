package output

import "fwyytool/api/chain"

type HttpTestCallOutput struct {
	Key       string             `json:"key"`
	Body      string             `json:"body"`
	Detail    HttpTestCallDetail `json:"detail"`
	Header    string             `json:"header"`
	RequestID string             `json:"requestID"`
}

type HttpTestCallDetail struct {
	Url      string `json:"url"`
	Method   string `json:"method"`
	HttpCode int64  `json:"httpCode"`
	Proto    string `json:"proto"`
}

type HttpTestGetReportListOutput struct {
	TarceUrl string         `json:"tarceUrl"`
	Total    int64          `json:"total"`
	List     []*TraceReport `json:"list"`
}

type TraceReport struct {
	ID            int64             `json:"id"`
	TraceID       string            `json:"traceID"`
	TargetURL     string            `json:"targetURL"`
	TraceReport   chain.TraceReport `json:"traceReport"`
	Total         int               `json:"total"`
	TotalDuration int               `json:"totalDuration"`
	RepeatNodeNum int               `json:"repeatNodeNum"`
	Status        int               `json:"status"`
	Operator      string            `json:"operator"`
	OperatorUID   int64             `json:"operatorUID"`
	CreateTime    int64             `json:"createTime"`
	UpdateTime    int64             `json:"updateTime"`
}
