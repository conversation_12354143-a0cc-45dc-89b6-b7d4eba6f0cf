package output

type SlowStatOutput struct {
	Cnt           int64          `json:"cnt"` //慢查询总次数
	Total         int64          `json:"total"`
	TrendDataList []TrendStat    `json:"trendDataList"`
	List          []SlowStatList `json:"list"`
}

type SlowStatList struct {
	CheckSum    string `json:"checkSum"`
	Fingerprint string `json:"fingerPrint"`
	Cnt         uint64 `json:"cnt"`
}

type TrendStat struct {
	Date      string
	TrendList []ClusterTrand
}

type ClusterTrand struct {
	Port string
	Name string
	Cnt  uint64
}
