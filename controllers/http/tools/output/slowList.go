package output

type SlowListOutput struct {
	List  []SlowList `json:"list"`
	Total int64      `json:"total"`
}

type SlowList struct {
	CheckSum    string  `json:"checkSum"`
	Fingerprint string  `json:"fingerPrint"`
	MysqlIp     string  `json:"mysqlIp"`
	MysqlPort   string  `json:"mysqlPort"`
	ClientUser  string  `json:"clientUser"`
	ClientIp    string  `json:"clientIp"`
	Sql         string  `json:"sql"`
	QueryTime   float64 `json:"queryTime"`
	Ts          string  `json:"ts"`
}
