package tools

import (
	"fwyytool/components"
	"fwyytool/consts"
	toolsInput "fwyytool/controllers/http/tools/input"
	"fwyytool/service/tools"
	"fwyytool/stru"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"net/http"
	"strings"
)

var MysqlController mysqlController

type mysqlController struct {
}

func (s mysqlController) SlowStat(ctx *gin.Context) {
	errMsg := ""
	var params toolsInput.SlowStatParam
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	err := params.Validate(ctx)
	if err != nil {
		errMsg = err.Error()
	}

	data, err := tools.MysqlService.SlowStat(ctx, tools.SlowStatParam{
		Port:      params.Port,
		BeginDate: params.BeginDate,
		EndDate:   params.EndDate,
		PageSize:  params.PageSize,
		Page:      params.Page,
		PortArr:   params.PortArr,
	})
	if err != nil {
		errMsg = err.Error()
	}

	portNames := []string{tools.MysqlService.GetClusterName(ctx, params.PortArr)}
	for _, port := range params.PortArr {
		portNames = append(portNames, tools.MysqlService.GetClusterName(ctx, []string{port}))
	}

	output := gin.H{
		"cnt":           data.Cnt,
		"total":         data.Total,
		"list":          data.List,
		"trendDataList": data.TrendDataList,
		"portNames":     portNames,
		"errMsg":        errMsg,
		"params":        params,
		"clusterList":   s.getClusterList(),
	}
	UriRawQuery := ctx.Request.URL.Query()
	UriRawQuery.Del("page")
	output["rawParams"] = UriRawQuery.Encode()

	ctx.HTML(http.StatusOK, "tools/mysql/slowStat.html", output)
	return
}

func (s mysqlController) getClusterList() []stru.ClusterDetail {
	clusterList := make([]stru.ClusterDetail, 0)
	for _, clusterGroup := range consts.ClusterList {
		clusterGroupList := make([]stru.ClusterDetail, 0)
		ports := make([]string, 0)
		for _, detail := range clusterGroup.List {
			ports = append(ports, detail.Port)
			clusterGroupList = append(clusterGroupList, detail)
		}
		clusterList = append(clusterList, stru.ClusterDetail{Name: clusterGroup.Name, Port: strings.Join(ports, ",")})
		clusterList = append(clusterList, clusterGroupList...)
	}
	return clusterList
}

func (s mysqlController) SlowList(ctx *gin.Context) {
	errMsg := ""
	var params toolsInput.SlowListParam
	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	err := params.Validate(ctx)
	if err != nil {
		errMsg = err.Error()
	}

	data, err := tools.MysqlService.SlowList(ctx, tools.SlowListParam{
		Port:      params.Port,
		BeginDate: params.BeginDate,
		EndDate:   params.EndDate,
		CheckSum:  params.CheckSum,
		PageSize:  params.PageSize,
		Page:      params.Page,
		PortArr:   params.PortArr,
	})
	if err != nil {
		errMsg = err.Error()
	}

	output := gin.H{
		"total":       data.Total,
		"list":        data.List,
		"errMsg":      errMsg,
		"params":      params,
		"clusterList": s.getClusterList(),
		"datePosArr":  consts.DatePosArr,
	}
	UriRawQuery := ctx.Request.URL.Query()
	UriRawQuery.Del("page")
	output["rawParams"] = UriRawQuery.Encode()

	ctx.HTML(http.StatusOK, "tools/mysql/slowList.html", output)
}

func (s mysqlController) SlowCollect(ctx *gin.Context) {
	errMsg := ""

	data, err := tools.MysqlService.SlowCollect(ctx)
	if err != nil {
		errMsg = err.Error()
	}

	output := gin.H{
		"list":   data,
		"errMsg": errMsg,
	}

	ctx.HTML(http.StatusOK, "tools/mysql/slowCollect.html", output)
}

func (s mysqlController) SlowStatApi(ctx *gin.Context) {
	var params toolsInput.SlowStatApiParam
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err := params.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := tools.MysqlService.SlowStat(ctx, tools.SlowStatParam{
		Port:      params.Port,
		BeginDate: params.BeginDate,
		EndDate:   params.EndDate,
		PageSize:  params.PageSize,
		Page:      params.Page,
		PortArr:   params.PortArr,
	})
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}

func (s mysqlController) SlowListApi(ctx *gin.Context) {
	var params toolsInput.SlowListApiParam
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	err := params.Validate(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := tools.MysqlService.SlowList(ctx, tools.SlowListParam{
		Port:      params.Port,
		BeginDate: params.BeginDate,
		EndDate:   params.EndDate,
		CheckSum:  params.CheckSum,
		PageSize:  params.PageSize,
		Page:      params.Page,
		PortArr:   params.PortArr,
	})
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
	return
}
