package gray

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"fwyytool/controllers/http/tools/gray/request"
	"fwyytool/helpers"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/intratraffic"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/mesh"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"net/http"
	"sort"
	"strings"
	"time"
)

const (
	GroupLevel = 4

	QueryTagForNotRegister = 0
	QueryTagForQuerying    = 1
	QueryTagForFinish      = 2

	CallResultCacheKeyPre = "fwyy_tool_check_cluster_%s"
	CheckTotalCacheKeyPre = "fwyy_tool_check_cluster_total_%s"
	CheckIndexCacheKeyPre = "fwyy_tool_check_cluster_index_%s"
	CallResultExpire      = 3600 * 3
)

type CheckClusterResult struct {
	QueryTag int64             `json:"queryTag"`
	Current  int               `json:"current"`
	Total    int               `json:"total"`
	Result   map[string]string `json:"result"`
}

func CheckClusterInfo(ctx *gin.Context) {
	var params request.CheckClusterInfoReq
	if err := ctx.ShouldBind(&params); err != nil {
		setOutput(ctx, err.Error(), nil, params)
		return
	}

	CheckCluster(ctx, params)

}

func ConvertArrayStringToArrayInt64(input []string) []int64 {
	ret := make([]int64, 0, len(input))
	for _, e := range input {
		ret = append(ret, cast.ToInt64(strings.TrimSpace(e)))
	}
	return ret
}

func CheckCluster(ctx *gin.Context, params request.CheckClusterInfoReq) {
	if len(params.PersonUids) == 0 && len(params.GroupIds) == 0 {
		setOutput(ctx, "", nil, params)
		return
	}

	personUidArr := strings.Split(params.PersonUids, ",")
	groupIdArr := strings.Split(params.GroupIds, ",")
	personUidList := ConvertArrayStringToArrayInt64(personUidArr)
	groupIdList := ConvertArrayStringToArrayInt64(groupIdArr)
	queryKey := GetQueryKey(personUidList, groupIdList)

	ret, err := getCheckResult(ctx, queryKey)
	if err != nil {
		setOutput(ctx, err.Error(), nil, params)
		return
	}
	if ret.QueryTag != QueryTagForNotRegister {
		setOutput(ctx, "", ret, params)
		return
	}

	go func() {
		dataCtx := gin.CreateNewContext(gin.New())
		dataCtx.Request = ctx.Request

		personUids := fwyyutils.NewInt64Set()
		if len(params.PersonUids) > 0 {
			personUids.Adds(personUidList)
		}
		if len(params.GroupIds) > 0 {
			uids, _err := GetGroupPersonUids(dataCtx, groupIdList)
			if _err != nil {
				setOutput(dataCtx, _err.Error(), nil, params)
				return
			}
			personUids.Adds(uids)
		}

		err = SetTotal(ctx, queryKey, personUids.Len())
		if err != nil {
			zlog.Warnf(ctx, "[CheckCluster] set total failed, err: %+v", err)
		}
		zlog.Infof(ctx, "[CheckCluster] total: %d, params: %+v", personUids.Len(), params)

		clusterInfo := GetClusterInfo(dataCtx, queryKey, personUids.AsList())
		result := make(map[string]string)
		for key, ids := range clusterInfo {
			result[key] = strings.Join(ids, ",")
		}

		cacheRet := CheckClusterResult{
			QueryTag: QueryTagForFinish,
			Result:   result,
		}
		err = SetCheckResult(dataCtx, queryKey, cacheRet)
		if err != nil {
			zlog.Warnf(dataCtx, "[CheckClusterInfo] set result info failed, ret: %+v, err: %+v", cacheRet, err)
		}
		return
	}()

	cacheRet := CheckClusterResult{
		QueryTag: QueryTagForQuerying,
	}
	err = SetCheckResult(ctx, queryKey, cacheRet)
	if err != nil {
		zlog.Warnf(ctx, "[CheckClusterInfo] set querying info failed, ret: %+v, err: %+v", cacheRet, err)
	}
	setOutput(ctx, "", &cacheRet, params)
	return
}

func GetQueryKey(personUids, groupIds []int64) string {
	sort.Slice(personUids, func(i, j int) bool {
		return personUids[i] < personUids[j]
	})
	sort.Slice(groupIds, func(i, j int) bool {
		return groupIds[i] < groupIds[j]
	})
	personUidStr, _ := jsoniter.MarshalToString(personUids)
	groupIdStr, _ := jsoniter.MarshalToString(groupIds)

	str := fmt.Sprintf("%s_%s", personUidStr, groupIdStr)
	h := md5.New()
	h.Write([]byte(str))
	cipherStr := h.Sum(nil)
	return hex.EncodeToString(cipherStr)
}

func IncrIndex(ctx *gin.Context, queryKey string) error {
	cacheKey := fmt.Sprintf(CheckIndexCacheKeyPre, queryKey)
	err := fwyyutils.RunWithRetry(func(idx int) error {
		_, _err := helpers.RedisClient.Incr(ctx, cacheKey)
		return _err
	})
	return err
}

func GetIndex(ctx *gin.Context, queryKey string) int {
	cacheKey := fmt.Sprintf(CheckIndexCacheKeyPre, queryKey)
	value, err := helpers.RedisClient.Get(ctx, cacheKey)
	if err == nil && len(value) > 0 {
		return cast.ToInt(string(value))
	}
	return 0
}

func SetTotal(ctx *gin.Context, queryKey string, total int) error {
	cacheKey := fmt.Sprintf(CheckTotalCacheKeyPre, queryKey)
	err := fwyyutils.RunWithRetry(func(idx int) error {
		return helpers.RedisClient.SetEx(ctx, cacheKey, total, CallResultExpire)
	})
	return err
}

func GetTotal(ctx *gin.Context, queryKey string) int {
	cacheKey := fmt.Sprintf(CheckTotalCacheKeyPre, queryKey)
	value, err := helpers.RedisClient.Get(ctx, cacheKey)
	if err == nil && len(value) > 0 {
		return cast.ToInt(string(value))
	}
	return 0
}

func SetCheckResult(ctx *gin.Context, queryKey string, cacheRet CheckClusterResult) error {
	value, err := jsoniter.MarshalToString(cacheRet)
	if err != nil {
		return err
	}

	cacheKey := fmt.Sprintf(CallResultCacheKeyPre, queryKey)
	err = fwyyutils.RunWithRetry(func(idx int) error {
		return helpers.RedisClient.SetEx(ctx, cacheKey, value, CallResultExpire)
	})
	return err
}

func getCheckResult(ctx *gin.Context, queryKey string) (*CheckClusterResult, error) {
	cacheKey := fmt.Sprintf(CallResultCacheKeyPre, queryKey)
	value, err := helpers.RedisClient.Get(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	var result CheckClusterResult
	if len(value) == 0 {
		result.QueryTag = QueryTagForNotRegister
		return &result, nil
	}

	err = jsoniter.UnmarshalFromString(string(value), &result)
	if err != nil {
		return nil, err
	}

	result.Total = GetTotal(ctx, queryKey)
	result.Current = GetIndex(ctx, queryKey)

	if result.Current > result.Total {
		result.Total = result.Current + 10
	}
	return &result, nil
}

func GetClusterInfo(ctx *gin.Context, queryKey string, uids []int64) map[string][]string {
	ret := make(map[string][]string)
	for _, uid := range uids {
		if err := IncrIndex(ctx, queryKey); err != nil {
			zlog.Warnf(ctx, "[GetClusterInfo] incr index failed, err: %+v", err)
		}
		req := intratraffic.GetClusterInfoReq{
			SystemId:  intratraffic.SystemIdForAssistantDesk,
			PersonUid: uid,
		}
		rsp, err := intratraffic.GetClusterInfo(ctx, &req)
		if err != nil {
			zlog.Infof(ctx, "[GetClusterInfo] get cluster info failed, uid: %d", uid)
			continue
		}

		ret[rsp.CurrentCluster] = append(ret[rsp.CurrentCluster], cast.ToString(uid))
		time.Sleep(time.Millisecond * 100)
	}
	return ret
}

func setOutput(ctx *gin.Context, errMsg string, data *CheckClusterResult, params request.CheckClusterInfoReq) {
	outputInfo := gin.H{
		"errMsg": errMsg,
		"params": gin.H{
			"personUids": params.PersonUids,
			"groupIds":   params.GroupIds,
		},
	}
	if data != nil {
		outputInfo["data"] = data.Result
		outputInfo["queryTag"] = data.QueryTag
		outputInfo["total"] = data.Total
		outputInfo["current"] = data.Current
	}
	UriRawQuery := ctx.Request.URL.Query()
	UriRawQuery.Del("page")
	outputInfo["rawParams"] = UriRawQuery.Encode()

	ctx.HTML(http.StatusOK, "tools/gray/checkclusterinfo.html", outputInfo)
}

func GetGroupPersonUids(ctx *gin.Context, groupIds []int64) ([]int64, error) {
	ret, err := mesh.GetGroupDetailByIds(ctx, groupIds, mesh.NeedParentTreeForDetail)
	if err != nil {
		return nil, err
	}

	groupIdSet := fwyyutils.NewInt64Set()
	for _, groupDetail := range ret {
		levels := strings.Split(groupDetail.LevelStr, ",")
		level := GroupLevel - len(levels)
		if level <= 0 {
			groupIdSet.Add(groupDetail.ID)
			continue
		}

		groupTree, _err := mesh.GetGroupAllChildren(ctx, groupDetail.ID)
		if _err != nil {
			return nil, _err
		}
		groupIdSet.Adds(GetGroupIds(groupTree.Tree, level))

		time.Sleep(time.Millisecond * 100)
	}

	pensonUids := fwyyutils.NewInt64Set()
	ids := groupIdSet.AsList()
	for _, groupId := range ids {
		uids, _err := getPersonUidsByGroupId(ctx, groupId)
		if _err != nil {
			return nil, _err
		}
		pensonUids.Adds(uids)

		time.Sleep(time.Millisecond * 100)
	}
	return pensonUids.AsList(), nil
}

func getPersonUidsByGroupId(ctx *gin.Context, groupId int64) ([]int64, error) {
	groupPersonList, err := mesh.GetAllStaffUidsByGroupIds(ctx, []int64{groupId})
	if err != nil {
		return nil, err
	}

	ret := make([]int64, 0)
	for _, item := range groupPersonList.GroupList {
		ret = append(ret, item.StaffUids...)
	}
	return ret, err
}

func GetGroupIds(tree mesh.GroupItem, level int) []int64 {
	if level <= 0 || len(tree.Children) == 0 {
		return []int64{tree.ID}
	}

	ret := fwyyutils.NewInt64Set()
	for _, item := range tree.Children {
		ret.Adds(GetGroupIds(item, level-1))
	}
	return ret.AsList()
}
