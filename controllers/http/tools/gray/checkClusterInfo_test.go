package gray

import (
	"fwyytool/controllers/http/tools/gray/request"
	"fwyytool/helpers"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/kunpeng"
	"git.zuoyebang.cc/fwyybase/fwyylibs/consts/touchmis"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"testing"
)

func TestGetGroupPersonUids(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	ret, err := GetGroupPersonUids(ctx, []int64{17708})
	t.Log(ret, err)
}

func TestGetProfile(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	ret, err := kunpeng.GetProfile(ctx, 4447478597, touchmis.BusinessLineForDuXue)
	t.Log(ret, err)
}

func TestCheckClusterInfo(t *testing.T) {
	engine := gin.New()
	ctx := gin.CreateNewContext(engine)
	env.SetRootPath("../../../../")
	helpers.PreInit()
	helpers.InitResource(engine)
	defer helpers.Release()

	req := request.CheckClusterInfoReq{
		PersonUids: "4447478597",
		GroupIds:   "17708",
	}
	CheckCluster(ctx, req)
}
