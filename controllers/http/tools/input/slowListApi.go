package input

import (
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"strings"
	"time"
)

type SlowListApiParam struct {
	Port      string `json:"port" form:"port" binding:"required"`
	BeginDate string `json:"beginDate" form:"beginDate"`
	EndDate   string `json:"endDate" form:"endDate" `
	DatePos   string `json:"datePos" form:"datePos"`
	CheckSum  string `json:"checkSum" form:"checkSum" `
	PageSize  int    `json:"pageSize" form:"pageSize" `
	Page      int    `json:"page" form:"page" `
	PortArr   []string
}

func (c *SlowListApiParam) Validate(ctx *gin.Context) error {
	portArr := strings.Split(c.Port, ",")
	for _, port := range portArr {
		c.PortArr = append(c.PortArr, cast.ToString(cast.ToInt64(port)))
	}

	timestamp := time.Unix(time.Now().Unix()-7*24*60*60, 0)
	if len(c.BeginDate) == 0 {
		c.BeginDate = timestamp.Format("2006-01-02") + " 00:00:00"
	}
	if len(c.EndDate) == 0 {
		c.EndDate = time.Now().Format("2006-01-02") + " 23:59:59"
	}
	if len(c.DatePos) > 0 {
		beginTime, _ := fwyyutils.AnyToTimeRange(cast.ToInt64(c.DatePos))
		c.BeginDate = time.Unix(beginTime, 0).Format("2006-01-02") + " 00:00:00"
		c.EndDate = time.Now().Format("2006-01-02") + " 23:59:59"
	}
	if c.Page <= 0 {
		c.Page = 1
	}
	if c.PageSize <= 0 {
		c.PageSize = 50
	}
	return nil
}
