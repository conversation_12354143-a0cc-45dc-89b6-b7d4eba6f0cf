package input

import (
	"errors"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
)

type HttpTestRouterParam struct {
	Domain string `json:"domain" form:"domain" binding:"required"`
}

func (c *HttpTestRouterParam) Validate(ctx *gin.Context) error {
	// 验证domain参数不能为空
	if strings.TrimSpace(c.Domain) == "" {
		return errors.New("domain parameter is required")
	}

	// 验证domain是否为有效的URL格式
	parsedURL, err := url.Parse(c.Domain)
	if err != nil {
		return errors.New("invalid domain URL format")
	}

	// 确保URL包含scheme
	if parsedURL.Scheme == "" {
		return errors.New("domain URL must include scheme (http:// or https://)")
	}

	// 确保URL包含host
	if parsedURL.Host == "" {
		return errors.New("domain URL must include host")
	}

	return nil
}
