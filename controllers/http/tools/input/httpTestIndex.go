package input

import "github.com/gin-gonic/gin"

type HttpTestCallParam struct {
	Method      string `json:"method" form:"method" binding:"required"`
	Url         string `json:"url" form:"url" binding:"required"`
	Body        string `json:"body" form:"body"`
	ContentType string `json:"contentType" form:"contentType"`
	Cookie      string `json:"cookie" form:"cookie"`
	Header      string `json:"header" form:"header"`
	Value       string `json:"value" form:"value"`
}

func (c *HttpTestCallParam) Validate(ctx *gin.Context) error {
	return nil
}

type HttpTestClearQWUrlCacheParam struct {
	MinID int64 `json:"minId" form:"minId"`
	MaxID int64 `json:"maxId" form:"maxId"`
}
