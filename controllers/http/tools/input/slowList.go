package input

import (
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"strings"
	"time"
)

type SlowListParam struct {
	Port      string `json:"port" form:"port" binding:"required"`
	BeginDate string `json:"beginDate" form:"beginDate"`
	EndDate   string `json:"endDate" form:"endDate" `
	CheckSum  string `json:"checkSum" form:"checkSum" `
	PageSize  int    `json:"pageSize" form:"pageSize" `
	Page      int    `json:"page" form:"page" `
	PortArr   []string
}

func (c *SlowListParam) Validate(ctx *gin.Context) error {
	portArr := strings.Split(c.Port, ",")
	for _, port := range portArr {
		c.PortArr = append(c.PortArr, cast.ToString(cast.ToInt64(port)))
	}

	if len(c.BeginDate) == 0 {
		c.BeginDate = fwyyutils.GetTodayDate()
	}
	if len(c.EndDate) == 0 {
		c.EndDate = fwyyutils.GetToDayTime(time.Now().AddDate(0, 0, 1)).Format("2006-01-02")
	}
	if c.Page <= 0 {
		c.Page = 1
	}
	if c.PageSize <= 0 {
		c.PageSize = 50
	}
	return nil
}
