package orderbusiness

import (
	"fwyytool/controllers/http/orderbusiness/dto"
	"fwyytool/service/orderbusiness"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"net/http"
)

func GetUserOrderList(ctx *gin.Context) {
	req, respList, err := doGetUserOrderList(ctx)

	errMsg := ""
	dataJson := ""
	if err != nil {
		errMsg = err.Error()
	} else {
		dataJson, _ = jsoniter.MarshalToString(respList)
	}

	output := gin.H{
		"data":     respList,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   req,
	}

	ctx.HTML(http.StatusOK, "orderbusiness/order/getUserOrderList.html", output)
	return
}

func doGetUserOrderList(ctx *gin.Context) (req dto.GetUserOrderListReq, respList []dto.GetUserOrderListResp, err error) {
	if err = ctx.ShouldBind(&req); err != nil {
		return
	}
	if req.UserId == 0 {
		return
	}

	if err = req.Validate(ctx); err != nil {
		return
	}

	respList, err = orderbusiness.GetUserOrderList(ctx, req)
	return
}
