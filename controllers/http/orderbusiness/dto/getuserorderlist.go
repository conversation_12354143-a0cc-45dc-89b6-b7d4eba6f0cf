package dto

import (
	"errors"
	"github.com/gin-gonic/gin"
)

type GetUserOrderListReq struct {
	UserId  int64  `json:"userId" form:"userId"`
	ShopIds string `json:"shopIds" form:"shopIds"`
	Pn      int64  `json:"pn" form:"pn"`
}

func (r *GetUserOrderListReq) Validate(ctx *gin.Context) error {
	if r.Pn <= 0 {
		return errors.New("页数必须从1开始")
	}
	return nil
}

type GetUserOrderListResp struct {
	UserId                  int64                    `json:"userId"`
	OrderId                 int64                    `json:"orderId"`
	OriginalOrderId         int64                    `json:"originalOrderId"`
	BusinessId              int64                    `json:"businessId"`
	OrderFlags              string                   `json:"orderFlags"`          // https://ued.zuoyebang.cc/documents/docs/dds/orderFlag.html
	OrderBusinessStatus     int64                    `json:"orderBusinessStatus"` // https://ued.zuoyebang.cc/documents/docs/dds/orderBusinessStatus.html
	OrderBusinessStatusName string                   `json:"orderBusinessStatusName"`
	ShopId                  int64                    `json:"shopId"`
	ShopName                string                   `json:"shopName"`
	SaleChannel             int64                    `json:"saleChannel"`
	Quantity                int64                    `json:"quantity"`
	GoodsAmount             int64                    `json:"goodsAmount"`
	PaidAmount              int64                    `json:"paidAmount"`
	Address                 string                   `json:"address"`
	LastFrom                string                   `json:"lastFrom"`
	OrderTimeDesc           string                   `json:"orderTimeDesc"`
	PayTimeDesc             string                   `json:"payTimeDesc"`
	SkuRowList              []GetUserOrderSkuRowInfo `json:"skuRowList"`
}

type GetUserOrderSkuRowInfo struct {
	SkuId             int64  `json:"skuId"`
	SkuRowId          int64  `json:"skuRowId"`
	Quantity          int64  `json:"quantity"`
	SkuName           string `json:"skuName"`
	MainSkuId         int64  `json:"mainSkuId"`
	ProductId         int64  `json:"productId"`
	IsGift            int64  `json:"isGift"`
	ShopId            int64  `json:"shopId"`
	GoodsAmount       int64  `json:"goodsAmount"`
	PaidAmount        int64  `json:"paidAmount"`
	SkuServiceType    int64  `json:"skuServiceType"`
	CourseId          string `json:"courseId"`
	CourseName        string `json:"courseName"`
	CourseGradeId     string `json:"courseGradeId"`
	CourseGradeName   string `json:"courseGradeName"`
	CourseSubjectId   string `json:"courseSubjectId"`
	CourseSubjectName string `json:"courseSubjectName"`
}
