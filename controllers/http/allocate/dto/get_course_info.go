package dto

import (
	"errors"
	"github.com/gin-gonic/gin"
)

type GetCourseInfoReq struct {
	CourseId int64 `json:"courseId" form:"courseId"`
}

func (req *GetCourseInfoReq) Validate(ctx *gin.Context) (err error) {
	if req.CourseId == 0 {
		err = errors.New("请输入课程id")
		return
	}
	return
}

type GetCourseInfoResp struct {
	CourseId      int64                 `json:"courseId"`
	NewCourseType int64                 `json:"newCourseType"`
	PriceTagId    int64                 `json:"priceTagId"`
	SaleMode      int64                 `json:"saleMode"`
	Year          int64                 `json:"year"`
	Season        int64                 `json:"season"`
	Department    int64                 `json:"department"`
	GradeId       int64                 `json:"gradeId"`
	AcList        []GetCourseInfoAcItem `json:"acList"`
}

type GetCourseInfoAcItem struct {
	AssistantUid int64                    `json:"assistantUid"`
	ClassList    []GetCourseInfoClassItem `json:"classList"`
}

type GetCourseInfoClassItem struct {
	ClassId       int64 `json:"classId"`
	Code          int64 `json:"code"`
	StudentMaxCnt int64 `json:"studentMaxCnt"`
}
