package allocate

import (
	"fwyytool/controllers/http/allocate/dto"
	"fwyytool/libs/json"
	"fwyytool/service/allocate"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"net/http"
)

func GetCourseInfo(ctx *gin.Context) {
	params, data, err := doGetCourseInfo(ctx)

	var dataJson string
	var errMsg string
	if err == nil {
		dataJson, _ = json.MarshalToString(data)
	} else {
		errMsg = err.Error()
	}
	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}

	ctx.HTML(http.StatusOK, "allocate/apis/getCourseInfo.html", output)
	return
}

func doGetCourseInfo(ctx *gin.Context) (params dto.GetCourseInfoReq, data dto.GetCourseInfoResp, err error) {
	if err = ctx.ShouldBind(&params); err != nil {
		return
	}

	err = params.Validate(ctx)
	if err != nil {
		return
	}

	data, err = allocate.GetCourseInfo(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetCourseInfo.doGetCourseInfo fail, params: %+v, err: %+v", params, err)
		return
	}
	return
}
