package probe

import "github.com/gin-gonic/gin"

/*
	Kubernetes中的健康检查使用存活性探针（liveness probes）和就绪性探针（readiness probes）来实现
	详细参考： http://wiki.zuoyebang.cc/pages/viewpage.action?pageId=109834312

	golib中默认实现了 health 和 ready 两种探针，业务可以根据具体使用场景实现自己的探针，
	在 Bootstrap 前通过 base.RegReadyProbe(probe.Ready) 注册探针即可。

	不需要（比如，要部署到虚拟机），删掉注册即可（base.RegReadyProbe）
*/
func Ready(c *gin.Context) {
	// 业务逻辑

	// 业务逻辑判断没有问题返回：
	c.String(200, "success")

	// 如果业务逻辑判断资源未就绪，那么返回：
	c.String(500, "fail")
}
