# 配置规范


```
conf
├── app
│   ├── app.yaml
│   └── appConf.go
├── conf.go
└── mount
    ├── api.yaml
    ├── config.yaml
    └── resource.yaml
```

配置全部放到conf下，又根据使用方式不同分为两种：
* mount : 用来放置环境相关的配置，可通过配置中心发布的配置。分为基础配置和用户自定义配置。
    * 基础配置:
        * api.yaml : 用来放置api调用相关的配置
        * config.yaml : 用来放置基础配置,业务想挂载的简单配置可以直接放到这里,以免新增配置。
        * resource.yaml : 用来存放所有资源类相关配置
    * 用户自定义配置
        * custom.yaml : 这是个示例，用来存放业务相关跟随环境变化的配置。
        建议你尽可能的把配置写到一个文件中，如果配置过多导致文件太大可进行拆分，不宜拆分过多。
        
* app : 用来放业务配置，该目录下的配置在部署环境会跟随代码一起发布，不可通过配置中心发布。
一般用来存放很少变化但因环境不同而不同的配置。如果配置几乎不会因为环境不同而发生变化，建议直接在代码中定义即可。
