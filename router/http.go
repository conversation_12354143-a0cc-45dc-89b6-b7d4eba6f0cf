package router

import (
	"fwyytool/components"
	"fwyytool/components/templatefunc"
	"fwyytool/controllers/http/allocate"
	"fwyytool/controllers/http/assistantcourse"
	"fwyytool/controllers/http/common"
	"fwyytool/controllers/http/desk"
	"fwyytool/controllers/http/genke"
	"fwyytool/controllers/http/newgoodsplatform"
	"fwyytool/controllers/http/orderbusiness"
	"fwyytool/controllers/http/tools"
	"fwyytool/controllers/http/touchmis"
	"fwyytool/middleware"
	"fwyytool/pkg/version"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	m "git.zuoyebang.cc/pkg/golib/v2/middleware"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

func HTTP(engine *gin.Engine) {
	// 设置模板中可调用函数
	templatefunc.SetFunc(engine)
	var (
		router = engine.Group("/fwyytool")
	)

	// 加载模板跟静态资源
	engine.LoadHTMLGlob("templates/**/**/*")
	router.StaticFile("/favicon.ico", "./assets/static/favicon.ico")
	router.Static("/assets", "./assets")

	// 通用中间件
	router.Use(m.AddNotice("version", version.Get()))

	engine.NoRoute(func(ctx *gin.Context) {
		if ctx.ContentType() == gin.MIMEJSON || ctx.ContentType() == gin.MIMEPOSTForm {
			base.RenderJsonFail(ctx, components.ErrorSystemError)
			return
		}
		//ctx.HTML(http.StatusOK, "404.html", nil)
	})

	// 这个会把500的错误转为http code = 200， 特定业务错误码
	// 开发环境可以不使用这个中间件，以便打印错误栈方便调试
	if env.GetRunEnv() == env.RunEnvOnline {
		router.Use(middleware.Recover)
	}

	router.GET("/index", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"timestamp": time.Now().Unix(),
			"title":     "小工具",
			"isTest":    env.GetRunEnv() == env.RunEnvTest,
		})
	})

	router.Use(middleware.Auth())

	desk.RegisterHandlers(router)
	genke.RegisterHandlers(router)
	common.RegisterHandlers(router)
	allocate.RegisterHandlers(router)
	assistantcourse.RegisterHandlers(router)
	newgoodsplatform.RegisterHandlers(router)
	tools.RegisterHandlers(router)
	touchmis.RegisterHandlers(router)
	orderbusiness.RegisterHandlers(router)
}
