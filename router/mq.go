package router

import (
	"fmt"
	"fwyytool/controllers/mq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"go.uber.org/zap"

	"fwyytool/conf"
	"fwyytool/libs/mqc"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"github.com/gin-gonic/gin"
)

// MQ mq消费者回调入口
func MQ(g *gin.Engine) {
	c := mqc.New()
	mq.RegisterMQConsumer(c)
	startRmqConsumers(g, c)
}

func startRmqConsumers(g *gin.Engine, consumer *mqc.MQC) {
	// rocketMQ 消费回调handler注册 , service 需要在 helpers/init.go 中注册（InitRocketMq中的 rmq.InitRmq）。
	// 一个应用尽可能用一个Topic，而消息子类型则可以用tags来标识。tags可以由应用自由设置，
	// 只有生产者在发送消息设置了tags，消费方在订阅消息时才可以利用tags通过broker做消息过滤
	for _, consumerConf := range conf.RConf.Rmq.Consumer {
		service := consumerConf.Service
		// 初始化消费者
		if err := rmq.InitConsumer(consumerConf); err != nil {
			panic("register rmq[" + service + "] error: " + err.Error())
		}
		zlog.InfoLogger(nil, "InitConsumer", zap.String("service", service))

		// rmq 消费回调 handler 注册
		// service 参数需要与 resource.yaml 中对应 consumer 配置的 service 字段对应
		err := rmq.StartConsumer(g, service, consumer.ServeMQ)
		if err != nil {
			panic(fmt.Sprintf("Start consumer error %s: %s", service, err.Error()))
		}
		zlog.InfoLogger(nil, "StartConsumer", zap.String("service", service))
	}
}
