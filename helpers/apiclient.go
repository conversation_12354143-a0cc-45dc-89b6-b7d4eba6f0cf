package helpers

import (
	"fwyytool/conf"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/apis"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/zbcore"
	"git.zuoyebang.cc/pkg/golib/v2/base"
)

func InitApiClient() {
	var (
		ApiClientList = []apis.ApiClientInfo{
			{
				Roots:  apis.UserprofileRoots,
				Client: conf.API.UserProfile,
			},
			{
				Roots:  apis.Tower,
				Client: conf.API.Tower,
			},
			{
				Roots:  apis.MeshRoots,
				Client: conf.API.Mesh,
			},
			{
				Roots:  apis.Assistantdeskgo,
				Client: conf.API.AssistantDeskGo,
			},
			{
				Roots:  apis.KpApiRoots,
				Client: conf.API.KunPeng,
			},
			{
				Roots:  apis.KpStaffRoots,
				Client: conf.API.KPStaff,
			},
			{
				Roots:  apis.Intratraffic,
				Client: conf.API.Intratraffic,
			},
			{
				Roots:  apis.Touchmis,
				Client: conf.API.TouchMis,
			},
			{
				Roots:  apis.Touchmisgo,
				Client: conf.API.TouchMisGo,
			},
		}

		zbCoreClientList = map[string]*base.ApiClient{
			zbcore.ServiceTypeDal: conf.API.ZbCoreDal,
			zbcore.ServiceTypeDau: conf.API.ZbCoreDau,
			zbcore.ServiceTypeDat: conf.API.ZbCoreDat,
		}
	)

	conf.API.UserProfile.AppKey = "68DA5D3D7F2859B0"
	apis.RegisterApiClient(ApiClientList)
	err := zbcore.RegisterClient(zbCoreClientList)
	if err != nil {
		panic("zbCoreClient register error: %v" + err.Error())
	}
}
