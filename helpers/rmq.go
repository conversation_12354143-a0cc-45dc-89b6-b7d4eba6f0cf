package helpers

import (
	"fwyytool/conf"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

const (
	// RmqMark service of mark
	RmqMark      = "fwyytool"
	MarkAPIMark  = "markapi"
	MarkTask     = "mktask"
	SmartPenMark = "mksmartpen"
)

func InitRmq() {
	// set of started services
	startedServices := make(map[string]struct{})
	// rmq集群迁移期间，如果RConf.RmqNew.Producer中有对应service，则使用RmqNew
	// 迁移完成后删掉
	for _, producerConf := range conf.RConf.RmqNew.Producer {
		startProducer(producerConf)
		startedServices[producerConf.Service] = struct{}{}
	}

	for _, producerConf := range conf.RConf.Rmq.Producer {
		if _, exists := startedServices[producerConf.Service]; !exists {
			startProducer(producerConf)
		}
	}
}

func startProducer(producerConf rmq.ProducerConf) {
	zlog.Debugf(nil, "register Rmq producer: %s", producerConf.Service)
	if err := rmq.InitProducer(producerConf); err != nil {
		panic("register Rmq producer[" + producerConf.Service + "] error: " + err.Error())
	}

	if err := rmq.StartProducer(producerConf.Service); err != nil {
		panic("Rmq StartProducer[" + producerConf.Service + "] error: " + err.Error())
	}
	zlog.InfoLogger(nil, "StartProducer",
		zap.String("service", producerConf.Service),
		zap.String("nameServer", producerConf.NameServer),
		zap.String("topic", producerConf.Topic),
	)
}

func CloseRocketMq() {
	rmq.StopRmqConsume()
}

func SendMsgByInterface(ctx *gin.Context, cmdno string, content interface{}) error {
	c, err := jsoniter.Marshal(content)
	if err != nil {
		return errors.WithMessagef(err, "content: %s", content)
	}
	msg, err := rmq.NewMessage("fwyytool", c)
	if err != nil {
		return errors.WithMessagef(err, "NewMessage() error, content: %s", c)
	}
	msgID, err := msg.WithTag(cmdno).Send(ctx)
	if err != nil {
		return errors.WithMessagef(err, "tag: %s, content: %s", cmdno, c)
	}
	zlog.Infof(ctx, "sent message id=%s, tag=%s content=%s", msgID, cmdno, c)
	return nil
}

func SendMsg(ctx *gin.Context, cmdno string, content map[string]interface{}) error {
	c, err := jsoniter.Marshal(content)
	if err != nil {
		return errors.WithMessagef(err, "content: %s", content)
	}

	msg, err := rmq.NewMessage("fwyytool", c)
	if err != nil {
		return errors.WithMessagef(err, "NewMessage() error, content: %s", c)
	}

	/*
		topic: 消息主题，通过 Topic 对不同的业务消息进行分类。
		tag: 消息标签，用于对某个 Topic 下的消息进行分类。生产者在发送消息时，已经指定消息的 Tag，消费者需根据已经指定的 Tag 来进行订阅。
		比如：订单消息和支付消息属于不同业务类型的消息，对应两个topic。其中订单消息根据商品品类以不同的 Tag 再进行细分，
		列如电器类、服装类、图书类等被各个不同的系统所接收。
		通过合理的使用 Topic 和 Tag，可以让业务结构清晰，更可以提高效率。
	*/

	msgID, err := msg.WithTag(cmdno).Send(ctx)
	if err != nil {
		return errors.WithMessagef(err, "tag: %s, content: %s", cmdno, c)
	}

	zlog.Infof(ctx, "sent message id=%s, tag=%s content=%s", msgID, cmdno, c)
	return nil
}

func SendMsgWithDelay(ctx *gin.Context, cmdno string, content map[string]interface{}) error {
	c, err := jsoniter.Marshal(content)
	if err != nil {
		return errors.WithMessagef(err, "content: %s", content)
	}

	msg, err := rmq.NewMessage("fwyytool", c)
	if err != nil {
		return errors.WithMessagef(err, "NewMessage() error, content: %s", c)
	}

	msgID, err := msg.WithTag(cmdno).WithDelay(rmq.Seconds5).Send(ctx)
	if err != nil {
		return errors.WithMessagef(err, "tag: %s, content: %s", cmdno, c)
	}

	zlog.Infof(ctx, "sent message with delay (5) id=%s, tag=%s content=%s", msgID, cmdno, c)
	return nil
}

func SendDelayMsg(ctx *gin.Context, serviceName string, cmdno string, content map[string]interface{}, delayLevel rmq.DelayLevel) error {
	c, err := jsoniter.Marshal(content)
	if err != nil {
		return errors.WithMessagef(err, "content: %s", content)
	}

	msg, err := rmq.NewMessage(serviceName, c)
	if err != nil {
		return errors.WithMessagef(err, "NewMessage() error, content: %s", c)
	}

	msgID, err := msg.WithTag(cmdno).WithDelay(delayLevel).Send(ctx)
	if err != nil {
		return errors.WithMessagef(err, "tag: %s, content: %s", cmdno, c)
	}

	zlog.Infof(ctx, "sent message id=%s, tag=%s content=%s", msgID, cmdno, c)
	return nil
}

func SendMsgBytesWithMarkTopic(ctx *gin.Context, cmdno string, c []byte) error {
	msg, err := rmq.NewMessage("fwyytool", c)
	if err != nil {
		return errors.WithMessagef(err, "NewMessage() error, content: %s", c)
	}
	msgID, err := msg.WithTag(cmdno).Send(ctx)
	if err != nil {
		return errors.WithMessagef(err, "tag: %s, content: %s", cmdno, c)
	}
	zlog.Infof(ctx, "sent message id=%s, tag=%s content=%s", msgID, cmdno, c)
	return nil
}

func SendMsgMapWithMarkTopic(ctx *gin.Context, cmdno string, content map[string]interface{}) error {
	c, err := jsoniter.Marshal(content)
	if err != nil {
		return errors.WithMessagef(err, "content: %s", content)
	}

	msg, err := rmq.NewMessage("fwyytool", c)
	if err != nil {
		return errors.WithMessagef(err, "NewMessage() error, content: %s", c)
	}
	msgID, err := msg.WithTag(cmdno).Send(ctx)
	if err != nil {
		return errors.WithMessagef(err, "tag: %s, content: %s", cmdno, c)
	}
	zlog.Infof(ctx, "sent message id=%s, tag=%s content=%s", msgID, cmdno, c)
	return nil
}
