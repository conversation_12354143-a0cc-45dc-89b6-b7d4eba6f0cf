package helpers

import "git.zuoyebang.cc/pkg/golib/v2/pool/gpool"

// GoPool 全局共用的协程池
var GoPool *gpool.Pool

func InitGPool() {
	var err error
	//nolint:gomnd
	GoPool, err = gpool.NewPool(100)
	if err != nil {
		panic("[initGPool error: " + err.Error())
	}
}

func CloseGPool() {
	if GoPool != nil {
		GoPool.Release()
		// zlog.Debugf(nil, "exit, runtime goroutine is %d", runtime.NumGoroutine())
	}
}
