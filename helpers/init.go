package helpers

import (
	"fwyytool/conf"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"math/rand"
	"time"
)

var PodID int

func Init(engine *gin.Engine) {
	PreInit()
	InitResource(engine)

}

func initPodID() int {
	rand.Seed(time.Now().UnixNano())
	return rand.Int()
}

// PreInit 必要初始化
func PreInit() {
	// 用于日志中展示模块的名字，开发环境需要手动指定，容器中无需手动指定
	env.SetAppName("fwyytool")

	// 配置加载
	conf.InitConf()

	// 日志初始化
	zlog.InitLog(conf.BasicConf.Log)

	PodID = initPodID()

}

// Clear 服务结束时的清理工作，对应 PreInit 初始化资源的清理
func Clear() {
	zlog.CloseLogger()
	Release()
}

// InitResource 初始化http server需要的资源
func InitResource(engine *gin.Engine) {
	InitMysql()
	InitClickHouese()
	InitRedis()
	InitJob(engine)
	InitRmq()
	//InitGCache()
	//InitEs()
	InitCos()
	InitGPool()
	InitValidator()
	InitApiClient()
}

// Release 释放http server启动的资源
func Release() {
	CloseMysql()
	CloseRedis()
	//CloseES()
	CloseRocketMq()
	CloseGPool()
}

// InitResourceForCron 任务启动所需init的资源
func InitResourceForCron(engine *gin.Engine) {
	PodID = initPodID()
	InitJob(engine)

	// 按需初始化资源
	InitGPool()
	//InitGCache()
	InitMysql()
	InitRedis()
	InitCos()
}
