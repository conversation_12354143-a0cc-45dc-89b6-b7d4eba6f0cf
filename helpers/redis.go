package helpers

import (
	"fwyytool/conf"

	"git.zuoyebang.cc/pkg/golib/v2/redis"
)

// RedisClient 推荐，直接使用
var RedisClient *redis.Redis

// InitRedis 初始化redis
func InitRedis() {
	c := conf.RConf.Redis[conf.RedisDefaultServiceName]
	var err error
	RedisClient, err = redis.InitRedisClient(c)
	if err != nil || RedisClient == nil {
		panic("init redis failed!")
	}
}

func CloseRedis() {
	if RedisClient != nil {
		_ = RedisClient.Close()
	}
}
