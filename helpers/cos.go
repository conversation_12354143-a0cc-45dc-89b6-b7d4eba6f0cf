package helpers

import (
	"fmt"
	"fwyytool/conf"
	"git.zuoyebang.cc/pkg/golib/v2/cos"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

var Bucket cos.Bucket
var BosBucket cos.Bucket

func InitCos() {
	b := conf.RConf.Cos["image10"]
	Bucket = cos.NewBucket(b)

	baidu := conf.RConf.Cos["bos"]
	BosBucket = cos.NewBucket(baidu)
}

func UploadFile2Cos(ctx *gin.Context, localFile, fileName, fileType string, category string) (pathFile, dwUrl string, err error) {
	remoteFile := fmt.Sprintf("arkgo/%s/%s", category, fileName)
	zlog.Infof(ctx, "UploadFile2Cos req:%+v", []interface{}{ctx, localFile, remoteFile, fileType})
	u, err := BosBucket.UploadLocalFile(ctx, localFile, remoteFile, fileType, true)
	zlog.Infof(ctx, "UploadFile2Cos res:%+v,err:%+v", u, err)
	if err != nil {
		zlog.Warnf(ctx, "upload file fail err:%+v", err)
		return "", u, err
	}
	return remoteFile + "." + fileType, u, nil
}

func GetUrlByFileName(ctx *gin.Context, name string) (string, error) {
	return BosBucket.GetUrlByFileName(ctx, name, 24*60*time.Second)
}
