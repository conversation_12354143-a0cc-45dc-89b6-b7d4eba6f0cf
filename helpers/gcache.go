package helpers

import (
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/gcache"
)

//Bucket cache instance
var (
	// Cache1 a cache with a default expiration time of 5 minutes, and which
	// purges expired items every 10 minutes
	Cache1 *gcache.BucketCache
	// Cache2 a cache with a default expiration time of 45 minutes, and which
	// purges expired items every 1 hours
	Cache2 *gcache.BucketCache
)

//nolint:gomnd
func InitGCache() {
	Cache1 = gcache.NewBucketCache(5*time.Minute, 10*time.Minute, 10)
	Cache2 = gcache.NewBucketCache(45*time.Minute, 1*time.Hour, 10)
}
