package helpers

import (
	"fmt"
	"fwyytool/conf"
	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
)

var (
	ClickHouseClient driver.Conn
)

func InitClickHouese() {
	var err error
	for name, clickHouseConf := range conf.RConf.ClickHouse {
		switch name {
		case conf.DefaultServiceName:
			fmt.Println(clickHouseConf)
			ClickHouseClient, err = clickhouse.Open(&clickhouse.Options{
				Addr: []string{clickHouseConf.Addr},
				Auth: clickhouse.Auth{
					Database: clickHouseConf.Database,
					Username: clickHouseConf.Username,
					Password: clickHouseConf.Password,
				},
			})
			if err != nil {
				fmt.Println(err)
			}

		}
	}
}

func CloseClickHouese() {
	_ = ClickHouseClient.Close()
}
