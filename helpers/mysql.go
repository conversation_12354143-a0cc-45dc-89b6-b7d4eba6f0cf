package helpers

import (
	"fmt"

	"fwyytool/conf"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"gorm.io/gorm"
)

var (
	// MysqlClient gorm.DB instance
	MysqlClient       *gorm.DB
	ShadowMysqlClient *gorm.DB
)

func InitMysql() {
	var err error
	for name, dbConf := range conf.RConf.Mysql {
		switch name {
		case conf.DefaultServiceName:
			MysqlClient, err = base.InitMysqlClient(dbConf)
		case conf.ShadowServiceName:
			ShadowMysqlClient, err = base.InitMysqlClient(dbConf)
		}

		if err != nil {
			panic("mysql connect error: %v" + err.<PERSON>rror())
		}
	}
}

func CloseMysql() {
	db, err := MysqlClient.DB()
	if err != nil {
		fmt.Println(err)
		return
	}
	_ = db.<PERSON>()
}
