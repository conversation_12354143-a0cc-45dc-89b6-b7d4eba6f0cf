var tableToExcel = (function () {
    var uri = 'data:application/vnd.ms-excel;base64,',
        template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>',
        base64 = function (s) { return window.btoa(unescape(encodeURIComponent(s))) },
        format = function (s, c) { return s.replace(/{(\w+)}/g, function (m, p) { return c[p]; }) };
    return function (table, name, filename) {
        if (!table.nodeType) table = document.getElementById(table)
        var ctx = { worksheet: name || 'Worksheet', table: table.innerHTML }

        document.getElementById("dlink").href = uri + base64(format(template, ctx));
        document.getElementById("dlink").download = filename;
        document.getElementById("dlink").click();
    }
})()

function highlightJson(jsonString, idSelector) {
    const json = JSON.parse(jsonString);
    const container = document.getElementById(idSelector);
    container.innerHTML = JSON.stringify(json, null, 2);
    container.innerHTML = container.innerHTML.replace(/\".*?\"/g, function(matched) {
        return '<span class="json-string">' + matched + '</span>';
    }).replace(/:\s*(-?\d+(?:\.\d+)?)/g, function(matched) {
        return ':<span class="json-number">' + matched.substring(1) + '</span>';
    }).replace(/:\s*(true|false)/g, function(matched) {
        return ':<span class="json-boolean">' + matched.substring(1) + '</span>';
    }).replace(/:\s*null/g, function(matched) {
        return ':<span class="json-null">' + matched.substring(1) + '</span>';
    }).replace(/(\b[A-Za-z_][A-Za-z0-9_]*\b):/g, function(matched) {
        return '<span class="json-key">' + matched + '</span>:';
    }) + "<style>.json-string{color:#c7254e;} .json-number{color:#299056;} .json-boolean{color:#f90;} .json-null{color:#880;} .json-key{color:#00f;}</style>";
}