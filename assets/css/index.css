@charset "utf-8";
/*CSS reset*/
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, input, textarea, p, blockquote, th, td, hr {
    margin:0;
    padding:0;
}
li{
    margin-top: 15px;
}
ol, ul, li {
    list-style:none;
}
caption, th {
    text-align:left;
}
h1, h2, h3, h4, h5, h6 {
    font-size:100%;
    font-weight:normal;
}
a {
    text-decoration:none;
    color: #3E3E3E;
    outline: none;
}
a:hover {
    color: #FF6600;
}
/*清除浮动*/
.clearfix:after {
    content:"\0020";
    height:0;
    display:block;
    clear:both;
    visibility:hidden;
}
.clearfix {
    *zoom:1;
}
/* 文字超长隐藏并加省略号 */
.text-overflow {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
/*原子样式*/
.l {
    float: left;
}
.r {
    float: right;
}
.cl {
    clear: both;
}


body {
    background: repeat scroll 0 0 rgba(0, 0, 0, 0);
    font-size: 14px;
}
input, textarea, button, a {
    outline: 0 none;
}
fieldset, legend, textarea, input {
    padding: 0;
}
a {
    color: #3F65B5;
    text-decoration: none;
}

/*左侧菜单结束}*/
/*{主体部分控制*/
.col_main {
    width: 2000px;
    background-color: #FFFFFF;
    border: 1px solid #D3D3D3;
    border-radius: 3px;
    box-shadow: 0 2px 2px 0 #E3E3E3;
}
.col_main, .col_side {
    display: table-cell;
    height: 650px;
    vertical-align: top;
}
.main_hd {
    background-color: #E9E9E9;
    background-image: linear-gradient(to bottom, #F3F3F3 0px, #E3E3E3 100%);
    border-bottom: 1px solid #D3D3D3;
    box-shadow: 0 1px 0 0 #FCFCFC inset;
    color: #545454;
    font-size: 14px;
    height: 36px;
    line-height: 36px;
}
.title_tab_navs:after {
    clear: both;
    content: "​";
    display: block;
    height: 0;
}
.title_tab_navs {
    background-color: #E9E9E9;
    background-image: linear-gradient(to bottom, #F3F3F3 0px, #E3E3E3 100%);
    border-bottom: 1px solid #D3D3D3;
    box-shadow: 0 1px 0 0 rgba(255, 255, 255, 0.5) inset;
    line-height: 36px;
    text-align: center;
}

.title_tab_nav.selected {
    border-top: 3px solid #70B213;
    top: -1px;
}
.title_tab_nav {
    float: left;
    font-size: 14px;
    position: relative;
}
.title_tab_nav a {
    border-right: 1px solid #D3D3D3;
    box-shadow: 1px 0 0 0 rgba(255, 255, 255, 0.5);
    color: #222222;
    display: block;
    outline: 0 none;
    padding: 0 20px;
    text-decoration: none;
}
.title_tab_nav.selected a {
    background-color: #FFFFFF;
    line-height: 35px;
    margin-bottom: -2px;
    position: relative;
}
.title_tab_nav.no_extra a {
    border-right-width: 0;
    box-shadow: none;
}
.main_hd .info {
    font-size: 14px;
    margin-right: 20px;
    margin-top: -36px;
    text-align: right;
}
.icon_dot {
    color: #AAAAAA;
    display: inline-block;
    font-size: 8px;
    font-style: normal;
    font-weight: 400;
    vertical-align: middle;
}
.main_hd h2 {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    padding-left: 20px;
    padding-right: 20px;
}

.main_bd {
    padding: 20px;
}

.weburl{
    margin-top: 32px;
}
.weburl li {
    float: left;
}
.weburl a{
    margin-left: 16px;
    border: 1px solid #eaeaea;
    padding: 6px 10px 4px 10px;
}
.weburl a:hover{
    background-color: #fcefa1;
    border-color: #F1AD71;
    text-decoration-line: none;
}
.tips-window {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    padding: 3% 5%;
    background-color: #ffffff;
    border:  12px solid #9d9d9d;
}
.tips-close {
    position: absolute;
    top: 88%;
    left: 90%;
    width: 8%;
    height: 8%;
    background-color: #4cae4c;
    cursor: pointer;
    text-align: center;
    padding: 0.7%;
}
.tips-window a {
    text-decoration-line: none;
}
.tips-window table,th,td {
    border: 1px solid black;
    padding: 5px 7px;
}
.helper-target {
    margin-left: -1px !important;
    padding-left: 15px !important;
    background-image: url('../../assets/static/Snipaste_2024-04-25_17-00-03.png');
    background-size: 80% 80%;
    background-repeat: no-repeat;
    background-position: center;
}

.json-container {
    background-color: #f8f8f8; /* 背景色 */
    color: #444; /* 文字颜色 */
    padding: 10px;
    border: 1px solid #ddd;
    font-family: monospace;
    white-space: pre;
}
