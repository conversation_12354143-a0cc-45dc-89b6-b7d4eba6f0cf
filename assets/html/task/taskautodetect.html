<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题检测工具</title>
    <link rel="stylesheet" href="https://fastly.jsdelivr.net/npm/animate.css/animate.min.css">
    <script src="https://fastly.jsdelivr.net/npm/vue/dist/vue.js"></script>
    <script src="https://fastly.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>

<body>
<h1>问题检测工具</h1>
<div id="app">
    <a class="navbar-brand" href="/fwyytool/index">回到首页</a><br><br>
    <input type="text" class="apple-input" v-model="inputValue"  @keyup.enter="fetchData"   placeholder="输入完毕后回车">
    <div class>

        <button    @click="jumpToTask" >任务详情</button>
    </div>
    <div class="history">
        <div v-for="historyItem in history" :key="historyItem.taskId" @click="fillInput(historyItem.taskId)">
            {{ historyItem.taskId }}
        </div>
        <button class="apple-button" @click="clearHistory">清除历史缓存</button>
    </div>

    <div class="apple-button">
        如果问题现象符合【问题描述】，关注【检查列表】，如果【检查结果】是❌或者❓，关注【解释】<br>
        ✅: 不需要关注<br>
        ❓: 可能是此原因导致的 <br>
        ❌: 很可能是此原因导致的问题现象</div>
    <table v-if="dataLoaded" class="apple-table">
        <thead>
        <tr>
            <th>问题描述</th>
            <th>检查列表</th>
            <th width="60">检查结果</th>
            <th>解释</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(item, index) in data" :key="index" :class="{ 'animated fadeIn': !item.hidden }">
            <td>{{ item.ProblemDesc }}</td>
            <td>
                <ul>
                    <li v-for="(checkItem, checkIndex) in item.CheckList" :key="checkIndex" class="multilines">{{ checkItem }}</li>
                </ul>
            </td>
            <td>
                <ul>
                    <li v-for="(resultItem, resultIndex) in item.CheckResult" :key="resultIndex" class="multilines">{{ getCheckResultSymbol(resultItem) }}</li>
                </ul>
            </td>
            <td>
                <ul>
                    <li v-for="(remarkItem, remarkIndex) in item.RemarkList" :key="remarkIndex" class="remark-item multilines">
                        <span v-html="highlightLink(remarkItem)"></span>
                    </li>



                </ul>

            </td>
        </tr>
        </tbody>
    </table>
    <p v-if="!dataLoaded" class="loading">正在加载数据...</p>
    <div v-show="showErrorMessage" class="error-message">
        {{ errorMessage }}
    </div>
</div>





<script>
    new Vue({
        el: '#app',
        data: {
            inputValue: '',
            data: [],
            dataLoaded: false,
            showErrorMessage: false,
            errorMessage: '',
            history: [

            ]
        },
        created() {
            const storedHistory = localStorage.getItem('taskHistory');
            if (storedHistory) {
                this.history = JSON.parse(storedHistory);
            }
            const urlParams = new URLSearchParams(window.location.search);
            const taskId = urlParams.get('taskId');
            if (taskId && taskId.trim() !== '') {
                this.inputValue = taskId;
                this.fetchData();
            }
        },
        directives: {
            // 自定义指令，用于将带有链接的文本标蓝
            linkify: {
                inserted(el) {
                    const links = el.getElementsByTagName('a');
                    for (let i = 0; i < links.length; i++) {
                        links[i].classList.add('remark-link');
                    }
                }
            }
        },
        methods: {
            fetchData() {
                const taskId = this.inputValue.trim(); // 获取输入框中的 taskId
                const queryParams = new URLSearchParams(window.location.search); // 获取当前 URL 中的查询参数
                queryParams.set('taskId', taskId); // 设置 taskId 到查询参数中
                const newUrl = `${window.location.pathname}?${queryParams.toString()}`; // 构建新的 URL

                // 使用 history.pushState 方法更新 URL
                history.pushState({ taskId }, '', newUrl);
                // 去重逻辑
                const existingIndex = this.history.findIndex(item => item.taskId === taskId);
                if (existingIndex !== -1) {
                    this.history.splice(existingIndex, 1);
                }

                this.dataLoaded = false;
                axios.post('/fwyytool/task/taskdetectall', { taskId: this.inputValue })
                    .then(response => {

                        const responseData = response.data;
                        console.log(responseData);
                        if (responseData.errNo === -1) {
                            this.showErrorMessage = true;
                            this.errorMessage = responseData.errMsg;
                            this.data = [];
                            setTimeout(() => {
                                this.showErrorMessage = false;
                                this.errorMessage = '';
                            }, 5000);
                        } else {
                            this.data = responseData.data.list.map(item => {
                                return {
                                    ...item,
                                    hidden: true
                                };
                            });
                            this.history.unshift({ taskId: this.inputValue }); // 将当前查询的 taskId 添加到历史记录开头
                        }

                        this.dataLoaded = true;
                        this.animateRows();
                        // 存储历史记录到 localStorage
                        localStorage.setItem('taskHistory', JSON.stringify(this.history));
                    })
                    .catch(error => {
                        this.history.unshift({ taskId: this.inputValue });
                        console.error(error);
                        this.dataLoaded = true;
                    });
            },
            animateRows() {
                const rows = document.querySelectorAll('tbody tr');
                rows.forEach((row, index) => {
                    setTimeout(() => {
                        this.data[index].hidden = false;
                    }, (index + 1) * 500);
                });
            },
            getCheckResultSymbol(result) {
                console.log(result)
                return result === 'pass' ? '✅' : result === 'maybe' ? '❓': '❌';
            },
            fillInput(taskId) {
                this.inputValue = taskId;
                this.fetchData();
            },
            clearHistory() {
                localStorage.removeItem('taskHistory'); // 移除历史记录缓存
                this.history = []; // 重置历史记录数组
            },
            highlightLink(remarkItem) {
                return remarkItem.replace(/<a href/g, '<a v-linkify href');
            },jumpToTask() {
                const taskId = this.inputValue;
                window.open('/fwyytool/task/taskinfo?taskId='+taskId)
            }

        }
    });
</script>

<style>
    .apple-button {
        background-color: #fff;
        color: #333;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 16px;
        border: none;
        box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
        cursor: pointer;
        transition: background-color 0.3s, color 0.3s, transform 0.3s;
    }

    .apple-button:hover {
        background-color: #f0f0f0;
        color: #222;
        transform: scale(1.05);
    }

    .apple-button:active {
        background-color: #e0e0e0;
        color: #111;
        transform: scale(0.95);
    }
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }

    ul {
        margin: 0;
        padding: 0;
    }

    .loading {
        text-align: center;
        padding: 16px;
    }
    td {
        word-break: break-all;
        overflow-wrap: break-word;
        vertical-align: top;
    }
    .error-message {
        position: fixed;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        padding: 10px;
        background-color: #ffcccc;
        border: 1px solid #ff6666;
        border-radius: 4px;
        animation: fadeOut 5s ease-in-out;
    }

    @keyframes fadeOut {
        0% {
            opacity: 1;
        }
        100% {
            opacity: 0;
        }
    }

    .history {
        margin-top: 10px;
    }
    .history div {
        cursor: pointer;
        margin-right: 5px;
        display: inline-block;
        padding: 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
    .history div:hover {
        background-color: #f0f0f0;
    }
    .apple-input {
        width: 230px;
        background-color: #f0f0f0;
        border: none;
        padding: 14px;
        border-radius: 20px;
        font-size: 16px;
        box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
        transition: background-color 0.3s, box-shadow 0.3s;
    }

    .apple-input:focus {
        outline: none;
        background-color: #fff;
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
    }
    .apple-table {
        border-collapse: collapse;
        width: 100%;
    }

    .apple-table th,
    .apple-table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }

    .apple-table th {
        background-color: #f0f0f0;
        font-weight: bold;
    }

    .apple-table td {
        background-color: #fff;
    }

    .multilines {
        line-height: 1.5em; /* 设置行高为 1.5 倍字体大小，可根据需要调整 */
        height: 4em; /* 设置高度为 3 倍行高，即两行高度，可根据需要调整 */
    }
 </style>
</body>
</html>