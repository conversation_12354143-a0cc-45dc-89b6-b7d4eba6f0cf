export default [
    {
        groupName: "方舟",
        groupKey: "arkgo",
        presetRequestList: [
            {
                apiName: "学生列表",
                requestConfig: {
                    method: "POST",
                    url: "https://assistantdesk.zuoyebang.cc/arkgo/innerapi/ark/arklist",
                    body: `{"tplId":1216,"serviceId":63,"courseId":3428556,"lessonId":4567265,"isNew":1,"keyword":"","pn":0,"rn":10,"sorts":"[]","source":"taskStudentList","taskId":0,"timestamp":1747016942,"needFieldKeys":"[]","personUid":3000158588,"assistantUid":4307300888}`,
                    header: "",
                    cookie: "",
                    contentType: "application/json;",
                    value: ""
                }
            },
            {
                apiName: "获取筛选项",
                requestConfig: {
                    method: "POST",
                    url: "https://assistantdesk.zuoyebang.cc/arkgo/innerapi/ark/getfilterconfig",
                    body: `{"courseId":545433,"lessonId":527811,"serviceId":66,"isMergeCourse":0,"personUid":2579599987,"assistantUid":4344662135}`,
                    header: "",
                    cookie: "",
                    contentType: "application/json;",
                    value: ""
                }
            },
            {
                apiName: "获取dataPanel数据",
                requestConfig: {
                    method: "POST",
                    url: "https://assistantdesk.zuoyebang.cc/arkgo/innerapi/ark/getdatapanel",
                    body: `{"courseId":3527767,"lessonId":4689512,"serviceId":788,"taskId":120,"timestamp":1746616927,"personUid":3000161219,"assistantUid":2498168732}`,
                    header: "",
                    cookie: "",
                    contentType: "application/json;",
                    value: ""
                }
            },
            {
                apiName: "清理方舟规则缓存",
                requestConfig: {
                    method: "POST",
                    url: "https://assistantdesk.zuoyebang.cc/arkgo/tool/delarkkeycache",
                    body: `{"keys":["ArkGo:ArkModuleRuleConfig_20_noCourseLastFrom"]}`,
                    header: "",
                    cookie: "",
                    contentType: "application/json;",
                    value: ""
                }
            },
        ]
    },
    {
        groupName: "工作台",
        groupKey: "assistantdesk",
        presetRequestList: [
            {
                apiName: "获取作答信息",
                requestConfig: {
                    method: "POST",
                    url: "https://assistantdesk.zuoyebang.cc/assistantdesk/desk/exam/getexaminfo",
                    body: `{"courseId":3356701,"lessonId":4462706,"studentUid":2416639881}`,
                    header: "",
                    cookie: "",
                    contentType: "application/json;",
                    value: ""
                }
            }
        ]
    },
    {
        groupName: "zbcore",
        groupKey: "zbcore",
        presetRequestList: [
            {
                apiName: "DAL",
                requestConfig: {
                    method: "POST",
                    url: "https://sellmis.zuoyebang.cc/zbcore/api/api",
                    body: `module=dal&entity=lesson&api=getKV&lessonIds=[4072361]&lessonFields=["lessonId","courseId","lessonName","subjectId","startTime","stopTime","status","lessonType","hasHomework","hasPlayback","finishTime","previewNoteUri","classNoteUri","reopenLessonId","fileList","outlineId","reopenLessonId","serviceInfo","stageTest","unLockTime","t007Tag","playType"]&noUseCache=0&raw={"lessonIds":[4072361],"lessonFields":["lessonId","courseId","lessonName","subjectId","startTime","stopTime","status","lessonType","hasHomework","hasPlayback","finishTime","previewNoteUri","classNoteUri","reopenLessonId","fileList","outlineId","reopenLessonId","serviceInfo","stageTest","unLockTime","t007Tag","playType"],"noUseCache":0}`,
                    header: "",
                    cookie: "",
                    contentType: "application/x-www-form-urlencoded;",
                    value: ""
                }
            },
            {
                apiName: "DAU",
                requestConfig: {
                    method: "POST",
                    url: "https://dau-api.zuoyebang.cc/zbcore/api/api",
                    body: `caller=assistantdesk&tm=1701944990&module=dau&entity=student&api=getKVByStudentUids&raw={"studentUid":[2574412652,2415068214,2467654779,4124788827,4081025537,4006217207,2146239590,4128305973,2522428231,2715971332,2169458299,2322430238,2152394914,2242045042,4053717004,2494020504,2522626385,2333670728,4106912286,2402674690,2427345704,2327422943,2418160832,2482221071,2316678157,2343363591,2376237972,4008149838,2498940887,2447633767,2570648998,2396438158,2336110270,2524539352,2138279669,2342646633],"fields":["studentUid","studentName","sex","grade","phone","guardian","guardianPhone","parentWebchat","fatherPhone","motherPhone","area","school","registerPhone","avatar","uname","regTime"]}`,
                    header: "",
                    cookie: "",
                    contentType: "application/x-www-form-urlencoded;",
                    value: ""
                }
            },
            {
                apiName: "DAS",
                requestConfig: {
                    method: "POST",
                    url: "https://das-api.zuoyebang.cc/zbcore/api/api",
                    body: `token=&caller=assistantdesk&module=das&entity=studentLesson&api=getKVByLessonId&raw={"studentLessonIds":["2447633767_3041387"],"studentLessonFields":["lessonId","studentUid","homeworkStatus","homeworkRecorrect"]}`,
                    header: "",
                    cookie: "",
                    contentType: "application/x-www-form-urlencoded;",
                    value: ""
                }
            }
        ]
    },
    {
        groupName: "tower",
        groupKey: "tower",
        presetRequestList: [
            {
                apiName: "查询课程的排班老师",
                requestConfig: {
                    method: "POST",
                    url: "http://tower-svc.support:8080/tower/allocatetask/getassistantbycourse?courseId=525535",
                    body: ``,
                    header: "",
                    cookie: "",
                    contentType: "none",
                    value: ""
                }
            }
        ]
    },
    {
        groupName: "jymis",
        groupKey: "jymis",
        presetRequestList: [
            {
                apiName: "获取知识目标",
                requestConfig: {
                    method: "POST",
                    url: "http://jymis-svc.edu-research:8080/jymis/knowledge/api/bindrelation/getnormal",
                    body: `{"relationKeys":["lesson_535362:exam_200561986"]}`,
                    header: "",
                    cookie: "",
                    contentType: "none",
                    value: ""
                }
            }
        ]
    },

];