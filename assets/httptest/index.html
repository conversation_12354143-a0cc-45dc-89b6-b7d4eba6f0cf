<!DOCTYPE html>
<html>
<head>
    <title>Tester - 轻量API接口调试工具</title>
    <meta charset="UTF-8">
    <meta content='api,tester,test,接口,测试,调试,http,https,开发,markdown,文档,生成' name='Keywords'>
    <meta content='一个轻量API接口调试工具，支持自定义Header，自定义Cookies，支持GET/POST/PUT/DELETE/PATCH/TRACE/OPTIONS等请求方式，支持快速生成Markdown接口文档，支持分享当前请求链接等' itemprop='description' name='Description'>
    <link rel="stylesheet" href="/fwyytool/assets/httptest/css/element.css">
    <style>
        body,
        html {
            padding: 0;
            margin: 0;
            background-color: #f5f5f5;
        }

        * {
            font-family: consolas, PingFang SC, Microsoft YaHei;
        }

        [v-cloak] {
            visibility: hidden !important;
        }

        .el-tabs--border-card {
            box-shadow: none;
        }

        #app {
            margin: 20px;
        }

        .logo {
            font-size: 32px;
            color: #666;
        }

        .no-select * {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .el-select .el-input {
            /*width: 100px;*/
        }

        .type {
            cursor: pointer;
        }

        .tips {
            font-size: 12px;
            color: #999;
            padding-left: 80px;
        }

        .contentType {
            margin-right: 10px !important;
        }

        .contentType .el-input {
            width: 90px;
        }

        .el-tabs__header,
        .el-tabs__nav-scroll,
        .el-tabs--border-card {
            border-radius: 5px;
        }

        textarea {
            resize: none !important;
        }

        pre {
            overflow: auto;
        }

        pre>* {
            font-size: 14px;
            margin: 0;
        }

        .header {
            margin-bottom: 10px;
            height: 20px !important;
        }

        .key {
            color: #333;
        }

        .hljs-string {
            color: green !important;
        }

        .hljs-number {
            color: orangered !important;
        }

        .hljs-literal {
            color: red !important;
        }

        .hljs {
            background-color: white !important;
        }

        ::-webkit-scrollbar {
            width: 5px;
            height: 5px;
        }

        ::-webkit-scrollbar-track {
            background-color: rgba(50, 50, 50, 0.1);
            border-radius: 5px;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 5px;
            background-color: rgba(0, 0, 0, 0.2);
        }

        ::-webkit-scrollbar-button {
            background-color: transparent;
        }

        ::-webkit-scrollbar-corner {
            background: transparent;
        }

        .input-with-select {
            display: block;
        }
        input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
            -webkit-appearance: none;
        }

        input[type="number"]{
            -moz-appearance: textfield;
        }
        .preview{
            padding: 0;
            margin:-15px;
        }
        .preview iframe {
            width: 100%;
            height: 500px;
            border: none;
        }
        .presetRequest {
            width: 400px;
        }
        .presetRequest input {
            width: 400px;
        }
        .presetRequestGroup {
            width: 200px;
        }
        .presetRequestGroup input {
            width: 200px;
        }
        .pre-width {
            width: 100px;
        }


    </style>
</head>

<body>
<div id="app" v-cloak>
    <el-container>
        <el-header style="height: 120px;">
            <el-tabs v-model="activeTab" type="border-card" @tab-click="handleTabClick">
                <el-tab-pane label="预置接口" name="preset">
                    <div style="padding: 10px;">
                        <el-select class="presetRequestGroup no-select" v-model="presetRequestGroup.groupKey" placeholder="请选择分组" @change="presetRequestGroupChanged" style="margin-right: 10px;">
                            <el-option :label="item.groupName" :value="item.groupKey" v-for="item in presetRequestGroupList"></el-option>
                        </el-select>

                        <el-select class="presetRequest no-select" v-model="presetRequest.apiIndex" placeholder="请选择预置接口" @change="presetRequestChanged">
                            <el-option :label="item.name" :value="item.apiIndex" v-for="item in currentPresetRequestList"></el-option>
                        </el-select>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="链路报告" name="report">
                    <div style="padding: 10px;">
                        <el-button @click="loadReportList" type="primary" size="small">刷新报告</el-button>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </el-header>
        <el-main id="requestBody" v-show="activeTab === 'preset'">
            <div>
                <el-autocomplete placeholder="请输入请求的URL" @input="requestUrlChanged" class="input-with-select no-select" v-model="request.url" :fetch-suggestions="querySearch" @select="handleSelect">
                    <el-select class="method no-select pre-width" v-model="request.method" slot="prepend" placeholder="请选择请求方式">
                        <el-option :label="item" :value="item" v-for="item in factory.methodList"></el-option>
                    </el-select>
                    <el-select slot="append" class="contentType no-select" v-model="request.contentType" placeholder="ContentType" @change="contentTypeChanged">
                        <el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in factory.contentTypeList"> <span style="float: left">{{ item.label }}　　</span>
                            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                        </el-option>
                    </el-select>
                    <el-button slot="append" icon="el-icon-s-promotion" @click="onSubmit" v-loading.fullscreen.lock="loading">请求</el-button>
                </el-autocomplete>
            </div>
            <br>
            <el-tabs type="border-card" v-model="factory.requestActive">
                <el-tab-pane label="Body" name="Body">
                    <el-input type="textarea" rows="5" class="data" placeholder="a=b&c=d&#10;&#10;{'a':'b','c':'d'}&#10;&#10;xml" v-model="request.body"  :autosize="{ minRows: 2, maxRows: 30}"></el-input>
                </el-tab-pane>
                <el-tab-pane label="Header" name="Header">
                    <el-input type="textarea" rows="5" class="data" placeholder="user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36
referer: https://assistantdesk-base-cc.suanshubang.cc/assistantdesk/view/assistant-first-line-teacher-v2/first-line-teacher/task/crm-task-v2" v-model="request.header"  :autosize="{ minRows: 2, maxRows: 30}"></el-input>
                </el-tab-pane>
                <el-tab-pane label="Cookie" name="Cookie">
                    <el-input type="textarea" rows="5" class="data" v-model="request.cookie" placeholder="access_token=abcdefghijklmnopqrstuvwxyz;&#10;可直接复制Chrome控制台Set-Cookie的内容" :autosize="{ minRows: 2, maxRows: 30}"></el-input>
                </el-tab-pane>
            </el-tabs>
            <br>
            <el-tabs type="border-card">
                <el-tab-pane label="Body">
                    <pre v-html="response.body"></pre>
                </el-tab-pane>
                <el-tab-pane label="Preview" class="preview">
                    <iframe :srcdoc="escape2Html(response.body)"></iframe>
                </el-tab-pane>
                <el-tab-pane label="header">
                    <pre v-html="response.header"></pre>
                </el-tab-pane>
                <el-tab-pane label="Detail">
                    <pre v-html="response.detail"></pre>

                </el-tab-pane>
                <el-tab-pane label="MarkDown">
                    <el-input type="textarea" autosize placeholder="文档读取中" v-model="response.markdown"></el-input>
                </el-tab-pane>
            </el-tabs>
        </el-main>
        <el-main id="reportBody" v-show="activeTab === 'report'">
            <div>
                <el-table :data="reportList.list" style="width: 100%" v-loading="reportLoading">
                    <el-table-column label="TraceID" width="200">
                        <template slot-scope="scope">
                            <el-link @click="openTraceLink(scope.row.traceID)" type="primary" :underline="false" style="cursor: pointer;">
                                {{ scope.row.traceID }}
                            </el-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="targetURL" label="TargetURL" ></el-table-column>
                    <el-table-column prop="total" label="请求数" width="100"></el-table-column>
                    <el-table-column label="总耗时(ms)" width="150">
                        <template slot-scope="scope">
                            {{ microsecondsToMilliseconds(scope.row.totalDuration) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="重复请求接口数" width="150">
                        <template slot-scope="scope">
                            <span v-if="getRepeatNodeNum(scope.row) > 0"
                                  style="color: red; cursor: pointer; text-decoration: underline;"
                                  @click="viewRepeatNodes(scope.row)">
                                {{ getRepeatNodeNum(scope.row) }}
                            </span>
                            <span v-else>0</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="状态" width="180">
                        <template slot-scope="scope">
                            {{ formatStatus(scope.row.status) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间" width="180">
                        <template slot-scope="scope">
                            {{ formatTime(scope.row.createTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                        <template slot-scope="scope">
                            <el-button @click="viewReport(scope.row)" type="text" size="small">查看详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div style="margin-top: 20px; text-align: center;">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="reportPagination.currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="reportPagination.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="reportList.total">
                    </el-pagination>
                </div>
            </div>
        </el-main>
    </el-container>

    <!-- Trace 详情弹窗 -->
    <el-dialog :visible.sync="traceDetailDialog.visible" width="1100px" top="5vh">
        <div slot="title">
            Trace详情 -
            <el-link @click="openTraceLink(traceDetailDialog.currentTraceID)" type="primary" :underline="false" style="cursor: pointer;" v-if="traceDetailDialog.currentTraceID">
                {{ traceDetailDialog.currentTraceID }}
            </el-link>
            <span v-else>{{ traceDetailDialog.currentTraceID }}</span>
        </div>
        <div v-if="traceDetailDialog.traceReport">
            <h4>基本信息</h4>
            <el-row :gutter="20">
                <el-col :span="8">
                    TraceID:
                    <el-link @click="openTraceLink(traceDetailDialog.traceReport.traceID)" type="primary" :underline="false" style="cursor: pointer;">
                        {{ traceDetailDialog.traceReport.traceID }}
                    </el-link>
                </el-col>
                <el-col :span="8">总耗时: {{ microsecondsToMilliseconds(traceDetailDialog.traceReport.apiDuration) }}ms</el-col>
                <el-col :span="8">请求数: {{ traceDetailDialog.traceReport.nodeNum }}</el-col>
            </el-row>
            <br>
            <h4>调用链路节点列表</h4>
            <el-table :data="traceDetailDialog.nodeList" style="width: 100%" max-height="400" :default-sort="{prop: 'startTime', order: 'ascending'}">
                <el-table-column prop="app" label="模块" width="180"></el-table-column>
                <el-table-column prop="operationName" label="请求" min-width="500"></el-table-column>
                <el-table-column prop="startTime" label="调用时间" width="160" sortable align="center">
                    <template slot-scope="scope">
                        {{ formatTimestamp(scope.row.startTime) }}
                    </template>
                </el-table-column>
                <el-table-column label="耗时 (ms)" width="120" sortable align="center" :sort-method="sortByDuration">
                    <template slot-scope="scope">
                        {{ microsecondsToMilliseconds(scope.row.duration) }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="traceDetailDialog.visible = false">关闭</el-button>
        </span>
    </el-dialog>

    <!-- 重复节点弹窗 -->
    <el-dialog :visible.sync="repeatNodeDialog.visible" width="850px" top="5vh">
        <div slot="title">
            重复节点报告 -
            <el-link @click="openTraceLink(repeatNodeDialog.traceID)" type="primary" :underline="false" style="cursor: pointer;" v-if="repeatNodeDialog.traceID">
                {{ repeatNodeDialog.traceID }}
            </el-link>
            <span v-else>{{ repeatNodeDialog.traceID }}</span>
        </div>
        <el-table :data="repeatNodeDialog.nodeList" style="width: 100%" max-height="500" :default-sort="{prop: 'apiDuration', order: 'descending'}">
            <el-table-column prop="requestUrl" label="Request URL" min-width="500"></el-table-column>
            <el-table-column label="耗时 (ms)" width="120" sortable align="center" :sort-method="sortByApiDuration">
                <template slot-scope="scope">
                    {{ microsecondsToMilliseconds(scope.row.apiDuration) }}
                </template>
            </el-table-column>
            <el-table-column prop="nodeNum" label="重复次数" width="100" sortable align="center"></el-table-column>
            <el-table-column label="操作" width="100" align="center">
                <template slot-scope="scope">
                    <el-button @click="viewRepeatNodeDetail(scope.row)" type="text" size="small">查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
            <el-button @click="repeatNodeDialog.visible = false">关闭</el-button>
        </span>
    </el-dialog>
</div>
</body>
<script src="/fwyytool/assets/httptest/js/vue-2.6.10.min.js"></script>
<script src="/fwyytool/assets/httptest/js/axios.min.js"></script>
<script src="/fwyytool/assets/httptest/js/element.js"></script>
<link rel="stylesheet" href="/fwyytool/assets/httptest/css/highlight.min.css">
<script src="/fwyytool/assets/httptest/js/highlight.min.js"></script>
<script type="module">
    import presetRequestConfig from './js/presetRequest.js?3';
    new Vue({
        el: '#app',
        data() {
            return {
                loading: false,
                dialogForSetting: false,
                activeTab: 'preset', // 默认显示预置接口
                reportLoading: false,
                request: {
                    method: "POST",
                    url: "https://assistantdesk.zuoyebang.cc/zbcore/api/api",
                    body: "access_token=abcdefghijklmnopqrstuvwxyz",
                    header: "",
                    cookie: "",
                    value: "",
                    contentType:""
                },
                presetRequestGroup: {
                    groupKey: "arkgo",
                    groupName: "方舟",
                },
                presetRequest: {
                    apiIndex: null,
                    name: "请选择预置接口模版",
                },
                presetRequestConfig: presetRequestConfig,
                reportList: {
                    total: 0,
                    list: [],
                    tarceUrl: '' // 保存 trace URL 前缀
                },
                reportPagination: {
                    currentPage: 1,
                    pageSize: 20
                },
                // 弹窗相关数据
                traceDetailDialog: {
                    visible: false,
                    title: '',
                    currentTraceID: '',
                    traceReport: null,
                    nodeList: [],
                    repeatNodeList: []
                },
                repeatNodeDialog: {
                    visible: false,
                    title: '',
                    traceID: '',
                    nodeList: []
                },
                response: {
                    body: "欢迎使用fwyytool httpTest工具",
                    detail: "欢迎使用fwyytool httpTest工具",
                    header: "欢迎使用fwyytool httpTest工具",
                    markdown: "欢迎使用fwyytool httpTest工具"
                },
                historyMax: 100, //最大历史
                historyList: [], //历史
                factory: {
                    requestActive: "Body",
                    header: {
                    },
                    contentType: 'application/json;',
                    contentTypeList: [
                        {
                            label: 'JSON',
                            value: 'application/json;'
                        },
                        {
                            label: 'XML',
                            value: 'application/xml;'
                        },
                        {
                            label: '表单',
                            value: 'application/x-www-form-urlencoded;'
                        },
                        {
                            label: '文本',
                            value: 'text/plain;'
                        },
                        {
                            label: 'HTML',
                            value: 'text/html;'
                        }
                    ],
                    methodList: [
                        'GET', 'POST'
                    ]

                }
            }
        },
        computed: {
            presetRequestGroupList() {
                return this.presetRequestConfig.map((config) => ({
                    groupKey: config.groupKey,
                    groupName: config.groupName
                }));
            },
            currentPresetRequestList() {
                if (!this.presetRequestGroup.groupKey) {
                    return [];
                }
                const group = this.presetRequestConfig.find(config => config.groupKey === this.presetRequestGroup.groupKey);
                if (!group || !group.presetRequestList) {
                    return [];
                }
                return group.presetRequestList.map((item, index) => ({
                    apiIndex: index,
                    name: item.apiName,
                    requestConfig: item.requestConfig
                }));
            }
        },
        created() {
            axios.defaults.timeout = 10000;
            //alert(1);
            this.updateData();
            try {
                this.historyList = !localStorage.getItem('history') ? [] : JSON.parse(localStorage.getItem('history'));
            } catch (e) {
                this.historyList = [];
            }
            this.request.contentType = this.factory.contentType;

            // 确保默认分组被正确设置
            this.$nextTick(() => {
                this.presetRequestGroupChanged();
            });
        },
        updated() {
            document.querySelectorAll('pre').forEach(function(block) {
                hljs.highlightBlock(block);
            });
        },
        methods: {
            // Tab 切换处理
            handleTabClick(tab, event) {
                this.activeTab = tab.name;
                if (tab.name === 'report' && this.reportList.list.length === 0) {
                    this.loadReportList();
                }
            },
            // 加载报告列表
            loadReportList() {
                this.reportLoading = true;
                const params = {
                    pn: this.reportPagination.currentPage,
                    rn: this.reportPagination.pageSize
                };

                axios.get('/fwyytool/tools/httptest/getreportlist', { params })
                    .then(response => {
                        if (response.data.errNo === 0) {
                            this.reportList.total = response.data.data.total;
                            this.reportList.list = response.data.data.list;
                            this.reportList.tarceUrl = response.data.data.tarceUrl || '';
                            console.log('Loaded tarceUrl:', this.reportList.tarceUrl);
                        } else {
                            this.$message.error('获取报告列表失败：' + response.data.errMsg);
                        }
                    })
                    .catch(error => {
                        this.$message.error('请求失败：' + error.message);
                    })
                    .finally(() => {
                        this.reportLoading = false;
                    });
            },
            // 分页大小改变
            handleSizeChange(val) {
                this.reportPagination.pageSize = val;
                this.reportPagination.currentPage = 1;
                this.loadReportList();
            },
            // 当前页改变
            handleCurrentChange(val) {
                this.reportPagination.currentPage = val;
                this.loadReportList();
            },
            // 格式化时间
            formatTime(timestamp) {
                if (!timestamp) return '';
                const date = new Date(timestamp * 1000);
                return date.toLocaleString('zh-CN');
            },
            // 格式化状态
            formatStatus(status) {
                if (status === 3) {
                    return '已生成';
                } else {
                    return '待生成';
                }
            },
            // 查看报告详情
            viewReport(row) {
                console.log('viewReport called with row:', row);
                try {
                    if (!row.traceReport) {
                        console.log('No traceReport data found');
                        this.$message.warning('该报告暂无详细数据');
                        return;
                    }

                    console.log('traceReport object:', row.traceReport);

                    // traceReport 已经是一个对象，不需要 JSON.parse
                    const traceReport = row.traceReport;

                    this.traceDetailDialog.title = `Trace详情 - ${row.traceID}`;
                    this.traceDetailDialog.currentTraceID = row.traceID;
                    this.traceDetailDialog.traceReport = traceReport;
                    this.traceDetailDialog.nodeList = traceReport.traceReportNodeList || [];
                    this.traceDetailDialog.visible = true;

                    console.log('Dialog should be visible now, nodeList:', this.traceDetailDialog.nodeList);
                } catch (error) {
                    console.error('处理 traceReport 失败:', error);
                    this.$message.error('处理报告数据失败: ' + error.message);
                }
            },
            // 获取重复节点数量
            getRepeatNodeNum(row) {
                try {
                    // 直接使用 repeatNodeNum 字段
                    if (row.repeatNodeNum !== undefined && row.repeatNodeNum !== null) {
                        console.log('repeatNodeNum from field:', row.repeatNodeNum);
                        return row.repeatNodeNum;
                    }

                    // 备用方案：从 traceReport 对象中获取
                    if (!row.traceReport) return 0;
                    const traceReport = row.traceReport;
                    console.log('getRepeatNodeNum - traceReport object:', traceReport);
                    const count = traceReport.repeatNodeReport ? traceReport.repeatNodeReport.length : 0;
                    console.log('repeatNodeReport count from object:', count);
                    return count;
                } catch (error) {
                    console.error('getRepeatNodeNum error:', error);
                    return 0;
                }
            },
            // 查看重复节点
            viewRepeatNodes(row) {
                console.log('viewRepeatNodes called with row:', row);
                try {
                    if (!row.traceReport) {
                        console.log('No traceReport data for repeat nodes');
                        this.$message.warning('该报告暂无重复节点数据');
                        return;
                    }

                    // traceReport 已经是一个对象，不需要 JSON.parse
                    const traceReport = row.traceReport;
                    console.log('traceReport object for repeat nodes:', traceReport);

                    if (!traceReport.repeatNodeReport || traceReport.repeatNodeReport.length === 0) {
                        console.log('No repeatNodeReport data');
                        this.$message.warning('该报告暂无重复节点数据');
                        return;
                    }

                    console.log('repeatNodeReport data:', traceReport.repeatNodeReport);

                    this.repeatNodeDialog.title = `重复节点报告 - ${row.traceID}`;
                    this.repeatNodeDialog.traceID = row.traceID;
                    this.repeatNodeDialog.nodeList = traceReport.repeatNodeReport;
                    this.repeatNodeDialog.visible = true;

                    console.log('Repeat node dialog should be visible now');
                } catch (error) {
                    console.error('处理重复节点数据失败:', error);
                    this.$message.error('处理重复节点数据失败: ' + error.message);
                }
            },
            // 查看重复节点详情
            viewRepeatNodeDetail(repeatNode) {
                this.traceDetailDialog.title = `重复节点详情 - ${repeatNode.requestUrl}`;
                // 使用原始的 TraceID，而不是 requestUrl
                this.traceDetailDialog.currentTraceID = this.repeatNodeDialog.traceID;
                this.traceDetailDialog.traceReport = {
                    traceID: this.repeatNodeDialog.traceID, // 使用原始的 TraceID
                    apiDuration: repeatNode.apiDuration,
                    nodeNum: repeatNode.nodeNum
                };
                this.traceDetailDialog.nodeList = repeatNode.traceReportNodeList || [];
                this.traceDetailDialog.visible = true;
                this.repeatNodeDialog.visible = false; // 关闭重复节点弹窗
            },
            // 格式化时间戳
            formatTimestamp(timestamp) {
                if (!timestamp) return '';
                // 如果是微秒时间戳，转换为毫秒
                const ms = timestamp > 1e12 ? Math.floor(timestamp / 1000) : timestamp;
                const date = new Date(ms);
                return date.toLocaleString('zh-CN');
            },
            // 按耗时排序（用于 duration 字段）
            sortByDuration(a, b) {
                const durationA = a.duration || 0;
                const durationB = b.duration || 0;
                return durationA - durationB;
            },
            // 按API耗时排序（用于 apiDuration 字段）
            sortByApiDuration(a, b) {
                const durationA = a.apiDuration || 0;
                const durationB = b.apiDuration || 0;
                return durationA - durationB;
            },
            // 微秒转毫秒
            microsecondsToMilliseconds(microseconds) {
                if (!microseconds && microseconds !== 0) return '';
                return (microseconds / 1000).toFixed(2);
            },
            // 打开 trace 链接
            openTraceLink(traceID) {
                if (!this.reportList.tarceUrl || !traceID) {
                    this.$message.warning('无法打开 trace 链接，缺少必要信息');
                    return;
                }
                const traceUrl = this.reportList.tarceUrl + traceID;
                console.log('Opening trace URL:', traceUrl);
                window.open(traceUrl, '_blank');
            },
            presetRequestGroupChanged() {
                // 当分组改变时，清空当前选择的API
                this.presetRequest.apiIndex = null;
                this.presetRequest.name = "请选择预置接口模版";

                // 更新分组名称
                const selectedGroup = this.presetRequestConfig.find(config => config.groupKey === this.presetRequestGroup.groupKey);
                if (selectedGroup) {
                    this.presetRequestGroup.groupName = selectedGroup.groupName;
                }
            },
            presetRequestChanged() {
                if (this.presetRequest.apiIndex === null || !this.presetRequestGroup.groupKey) {
                    return;
                }

                // 获取选中的API配置
                const group = this.presetRequestConfig.find(config => config.groupKey === this.presetRequestGroup.groupKey);
                if (!group || !group.presetRequestList) {
                    return;
                }

                const selectedApi = group.presetRequestList[this.presetRequest.apiIndex];
                if (!selectedApi || !selectedApi.requestConfig) {
                    return;
                }

                const config = selectedApi.requestConfig;

                var body = config.body
                if (config.contentType === "application/json;") {
                    body = JSON.stringify(JSON.parse(config.body), undefined, 4) || ''
                }

                // 更新request数据
                this.request.method = config.method || 'GET';
                this.request.url = config.url || '';
                this.request.body = body || '';
                this.request.header = config.header || '';
                this.request.cookie = config.cookie || document.cookie;
                this.request.contentType = config.contentType || 'application/json;';
                this.request.value = config.value || '';

                // 更新显示名称
                this.presetRequest.name = selectedApi.apiName;
            },
            handleSelect(item) {
                this.request = item;
            },
            querySearch(queryString, cb) {
                //设置历史
                cb(JSON.parse(JSON.stringify(this.historyList)));
            },
            requestUrlChanged() {
                this.request.url = this.request.url.replace(/\s+/g, "");
            },
            contentTypeChanged() {
                // this.factory.requestActive = 'Header';
                this.updateData();
            },
            updateData() {
                var headerStr = '';
                for (var key in this.factory.header) {
                    headerStr += key + ":" + this.factory.header[key] + "\n";
                }
                this.request.header = headerStr;
            },
            onSubmit() {
                var that = this;
                that.loading = true;
                if (that.request.url.indexOf('http://') == -1 && that.request.url.indexOf('https://') == -1) {
                    that.request.url = "http://" + that.request.url;
                }
                var historyItem = JSON.parse(JSON.stringify(that.request));
                historyItem.value = historyItem.url;
                if (that.historyList.length > that.historyMax) {
                    that.historyList.pop();
                }
                that.historyList.unshift(historyItem);
                localStorage.setItem('history', JSON.stringify(that.historyList));

                this.request.contentType = this.request.contentType || this.factory.contentType;

                // 如果是 JSON 协议，压缩 body 内容
                var requestData = JSON.parse(JSON.stringify(that.request));
                if (requestData.contentType && requestData.contentType.includes('application/json') && requestData.body) {
                    try {
                        // 尝试解析并压缩 JSON
                        var jsonObj = JSON.parse(requestData.body);
                        requestData.body = JSON.stringify(jsonObj);
                        console.log('JSON body compressed:', requestData.body);
                    } catch (e) {
                        // 如果解析失败，保持原样
                        console.warn('Failed to parse JSON body, keeping original:', e);
                    }
                }

                var arr = that.request.header.split('\n');
                that.factory.header = {};
                for (var index in arr) {
                    var match = arr[index].match(/(.*?):/);
                    if (match) {
                        var value = arr[index].replace(match[0], '');
                        that.factory.header[match[1]] = value;
                    }
                }
                console.log(that.factory.header)
                that.updateData();
                axios.post('/fwyytool/tools/httptest/call', requestData, that.factory.header)
                    .then(function(response) {
                        that.loading = false;
                        if (response.data.errNo == 0) {
                            that.$message({
                                message: '请求成功',
                                type: 'success'
                            });
                            that.decodeResponseDataOnline(response);
                        } else {
                            that.$message.error(response.data.errMsg);
                        }
                    })
                    .catch(function(error) {
                        that.loading = false;
                        console.log('Error details:', error);
                        console.log('Error response:', error.response);
                        console.log('Error message:', error.message);

                        // 优先显示服务器返回的错误信息
                        if (error.response && error.response.data && error.response.data.errMsg) {
                            that.$message.error(error.response.data.errMsg);
                        } else if (error.message == 'timeout of 10000ms exceeded') {
                            that.$message.error("请求API接口网络超时！");
                        } else if (error.response && error.response.status) {
                            // 显示 HTTP 状态码错误
                            that.$message.error(`请求失败，状态码: ${error.response.status}`);
                        } else {
                            // 最后的兜底错误信息
                            that.$message.error('网络请求失败，请检查网络连接或稍后重试');
                        }
                    });

            },
            //解析在线版本返回的数据
            decodeResponseDataOnline(response) {
                var that = this;
                try {
                    that.response.body = unescape(that.JsonFormat(JSON.parse(response.data.data.body)))
                } catch (error) {
                    that.response.body = that.html2Escape(response.data.data.body);
                }
                try {
                    that.response.detail = unescape(that.JsonFormat(response.data.data.detail))
                } catch (error) {
                    that.response.detail = that.html2Escape(response.data.data.detail);
                }
                try {
                    that.response.header = unescape(that.JsonFormat(JSON.parse(response.data.data.header)))
                } catch (error) {
                    that.response.header = that.html2Escape(response.data.data.header);
                }
                that.response.httpcode = response.data.data.detail.httpCode;
                //location.href = "/#/" + response.data.data.key;

                that.response.markdown = '';
                that.response.markdown += '## xxx API接口文档\n\n';
                that.response.markdown += '> 本文档由 [fwyytool] 自动生成，最后修改时间 ' + that.getNowDateTime() +
                    '\n\n';

                that.response.markdown += '#### 一、接口说明\n\n';
                that.response.markdown += '你可以在这里对接口进行一些简单的描述\n\n';
                that.response.markdown += '#### 二、请求方式\n\n';
                that.response.markdown += '```' + that.request.method + ' ' + that.request.url + '```\n\n';
                that.response.markdown += '#### 三、请求参数\n\n';
                if (that.request.method != 'GET') {
                    switch (that.factory.contentType) {
                        case 'application/json;':
                            try {
                                var obj = JSON.parse(that.request.body);
                                that.response.markdown += '|  字段  |  类型  |  必填  |  示例  |  说明  |\n';
                                that.response.markdown += '|  -  |  -  |  -  |  -  |  -  |\n';
                                that.response.markdown += that.getJsonMarkdown(obj);

                                that.response.markdown += '\n示例JSON：\n\n';
                                that.response.markdown += '```\n' + that.JsonFormat(JSON.parse(that.request.body)) +
                                    '\n```\n\n';
                            } catch (error) {
                                that.response.markdown += '> ' + that.request.body + '\n\n';
                            }
                            break;
                        case 'application/x-www-form-urlencoded;':
                            that.response.markdown += '|  字段  |  类型  |  必填  |  说明  |\n';
                            that.response.markdown += '|  -  |  -  |  -  |  -  |\n';
                            var arr = that.request.body.split('&');
                            for (var index in arr) {
                                var item = arr[index].split('=');
                                if (item.length == 2) {
                                    var type = typeof(item[1]);
                                    that.response.markdown += '  |  ' + item[0] + '  |  ' + type + '  |  是  |  -  |\n';
                                }
                            }
                            if (arr.length == 0 || arr.length == 1 && !arr[0]) {
                                that.response.markdown += '|  -  |  -  |  -  |  -  |\n';
                            }
                            that.response.markdown += '\n示例请求参数：\n\n';
                            try {
                                that.response.markdown += '```\n' + that.JsonFormat(JSON.parse(that.request.body)) +
                                    '\n```\n\n';
                            } catch (error) {
                                that.response.markdown += '> ' + that.request.body + '\n\n';
                            }
                            break;
                        default:
                            try {
                                var obj = JSON.parse(that.request.body);
                                that.response.markdown += '|  字段  |  类型  |  必填  |  示例  |  说明  |\n';
                                that.response.markdown += '|  -  |  -  |  -  |  -  |  -  |\n';
                                that.response.markdown += that.getJsonMarkdown(obj);

                                that.response.markdown += '\n示例JSON：\n\n';
                                that.response.markdown += '```\n' + that.JsonFormat(JSON.parse(that.request.body)) +
                                    '\n```\n\n';
                            } catch (error) {
                                that.response.markdown += '\n示例请求参数：\n\n';
                                that.response.markdown += '> ' + that.request.body + '\n\n';
                            }
                    }
                } else {
                    that.response.markdown += '请求参数请查看URL\n\n';
                }
                that.response.markdown += '#### 四、更多参数\n\n';
                that.response.markdown += 'Headers:\n\n';
                that.response.markdown += '```\n' + that.request.header + '\n```\n\n';
                if (that.request.cookie) {
                    that.response.markdown += 'Cookies:\n\n';
                    that.response.markdown += '```\n' + that.request.cookie + '\n```\n\n';
                }
                that.response.markdown += '#### 五、返回数据\n\n';
                try {
                    that.response.markdown += '|  字段  |  类型  |  固定  |  示例值  |  说明  |\n';
                    that.response.markdown += '|  -  |  -  |  -  |  -  |  -  |\n';
                    var obj = JSON.parse(that.response.body);
                    that.response.markdown += that.getJsonMarkdown(obj);
                } catch (error) {
                    that.response.markdown += that.response.body + '\n\n';
                }
                that.response.markdown += '\n示例返回结果：\n\n';
                that.response.markdown += '```\n' + that.response.body + '\n```\n\n';
            },
            getJsonMarkdown(obj, prefix = "", isArray = false) {
                try {
                    var that = this;
                    var _markdown = '';
                    if (isArray) {
                        for (var key in obj) {
                            var type = typeof(obj[key]);
                            if (type == 'object') {
                                if (obj[key] instanceof Array) {
                                    _markdown += '|  ' + prefix + key + '  |  array  |  是  |  []  |  -  |\n';
                                    _markdown += that.getJsonMarkdown(obj[key], prefix + key + ".", true);
                                } else if (obj[key] instanceof Object) {
                                    // _markdown += '|' + prefix + key + '|object|是|{}|-|\n';
                                    _markdown += that.getJsonMarkdown(obj[key], prefix);
                                } else {
                                    _markdown += '|  ' + prefix + key + '  |  array  |  是  |  []  |  -  |\n';
                                }
                            }
                            break;
                        }
                    } else {
                        for (var key in obj) {
                            var type = typeof(obj[key]);
                            if (obj[key] == null || obj[key] == undefined) {
                                type = 'null';
                            }
                            if (type == 'object') {
                                if (obj[key] instanceof Array) {
                                    _markdown += '|  ' + prefix + key + '  |  array  |  是  |  []  |  -  |\n';
                                    _markdown += that.getJsonMarkdown(obj[key], prefix + key + ".", true);
                                } else if (obj[key] instanceof Object) {
                                    _markdown += '|  ' + prefix + key + '  |  object  |  是  |  {}  |  -  |\n';
                                    _markdown += that.getJsonMarkdown(obj[key], prefix + key + ".");
                                } else {
                                    _markdown += '|  ' + prefix + key + '  |  array  |  是  |  []  |  -  |\n';
                                }
                            } else {
                                _markdown += '|  ' + prefix + key + '  |  ' + type + '  |  是  |  ' + obj[key] + '  |  -  |\n';
                            }
                        }
                    }
                    return _markdown;
                } catch (error) {
                    console.log(error)
                }
            },
            get_url_params() { //获取url里面的id参数
                var arr = window.location.href.split('/#/');
                if (arr.length == 2) {
                    return arr[1];
                } else {
                    return false;
                }
            },
            getNowDateTime: function() {
                var now = new Date(),
                    y = now.getFullYear(),
                    m = now.getMonth() + 1,
                    d = now.getDate();
                return y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " " + now.toTimeString().substr(
                    0, 8);
            },
            encodeUTF8(s) {
                var i, r = [],
                    c, x;
                for (i = 0; i < s.length; i++)
                    if ((c = s.charCodeAt(i)) < 0x80) r.push(c);
                    else if (c < 0x800) r.push(0xC0 + (c >> 6 & 0x1F), 0x80 + (c & 0x3F));
                    else {
                        if ((x = c ^ 0xD800) >> 10 == 0) //对四字节UTF-16转换为Unicode
                            c = (x << 10) + (s.charCodeAt(++i) ^ 0xDC00) + 0x10000,
                                r.push(0xF0 + (c >> 18 & 0x7), 0x80 + (c >> 12 & 0x3F));
                        else r.push(0xE0 + (c >> 12 & 0xF));
                        r.push(0x80 + (c >> 6 & 0x3F), 0x80 + (c & 0x3F));
                    };
                return r;
            },
            sha1(s) {
                var data = new Uint8Array(this.encodeUTF8(s))
                var i, j, t;
                var l = ((data.length + 8) >>> 6 << 4) + 16,
                    s = new Uint8Array(l << 2);
                s.set(new Uint8Array(data.buffer)), s = new Uint32Array(s.buffer);
                for (t = new DataView(s.buffer), i = 0; i < l; i++) s[i] = t.getUint32(i << 2);
                s[data.length >> 2] |= 0x80 << (24 - (data.length & 3) * 8);
                s[l - 1] = data.length << 3;
                var w = [],
                    f = [

                        function() {
                            return m[1] & m[2] | ~m[1] & m[3];
                        },

                        function() {
                            return m[1] ^ m[2] ^ m[3];
                        },

                        function() {
                            return m[1] & m[2] | m[1] & m[3] | m[2] & m[3];
                        },

                        function() {
                            return m[1] ^ m[2] ^ m[3];
                        }
                    ],
                    rol = function(n, c) {
                        return n << c | n >>> (32 - c);
                    },
                    k = [1518500249, 1859775393, -1894007588, -899497514],
                    m = [1732584193, -271733879, null, null, -1009589776];
                m[2] = ~m[0], m[3] = ~m[1];
                for (i = 0; i < s.length; i += 16) {
                    var o = m.slice(0);
                    for (j = 0; j < 80; j++)
                        w[j] = j < 16 ? s[i + j] : rol(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1),
                            t = rol(m[0], 5) + f[j / 20 | 0]() + m[4] + w[j] + k[j / 20 | 0] | 0,
                            m[1] = rol(m[1], 30), m.pop(), m.unshift(t);
                    for (j = 0; j < 5; j++) m[j] = m[j] + o[j] | 0;
                };
                t = new DataView(new Uint32Array(m).buffer);
                for (var i = 0; i < 5; i++) m[i] = t.getUint32(i << 2);

                var hex = Array.prototype.map.call(new Uint8Array(new Uint32Array(m).buffer), function(e) {
                    return (e < 16 ? "0" : "") + e.toString(16);
                }).join("");
                return hex;
            },
            getRandId() {
                return this.sha1(new Date().valueOf() + "." + Math.random());
            },
            JsonFormat(json) {
                if (typeof json != 'string') {
                    json = JSON.stringify(json, undefined, 4);
                }
                return json;
            },
            html2Escape(sHtml) {
                return sHtml.replace(/[<>&"]/g, function(c) {
                    return {
                        '<': '&lt;',
                        '>': '&gt;',
                        '&': '&amp;',
                        '"': '&quot;'
                    } [c];
                });
            },
            escape2Html(str) {
                var arrEntities = {
                    'lt': '<',
                    'gt': '>',
                    'nbsp': ' ',
                    'amp': '&',
                    'quot': '"'
                };
                return str.replace(/&(lt|gt|nbsp|amp|quot);/ig, function(all, t) {
                    return arrEntities[t];
                });
            }
        }
    })
</script>
</html>